using System;
using System.Collections;
using System.Net.NetworkInformation;
using TMPro;
using UnityEngine;

public class LevelResultAssigner : MonoBehaviour
{
    [SerializeField] private TMP_Text operationTMP;
    [SerializeField] private TMP_Text timeTMP;
    [SerializeField] private GameObject operationNewRecordIcon;
    [SerializeField] private GameObject timeNewRecordIcon;
    [SerializeField] private Material flipbookMat;

    private RectTransform operationNewRecordRT;
    private RectTransform timeNewRecordRT;

    [SerializeField]private float newRocordAnimSinFreq = 2;
    

    private void OnDisable()
    {
        StopAllCoroutines();
    }

    public void ShowLevelResult(int operation, float time, bool operationNewRecord, bool timeNewRecord)
    {
        timeTMP.text = time.ToString("F3");
        operationTMP.text = operation.ToString();
        operationNewRecordIcon.SetActive(operationNewRecord);
        timeNewRecordIcon.SetActive(timeNewRecord);

        StartCoroutine(FlipbookAnimation());
        

        if (timeNewRecord)
        {
            if (!timeNewRecordRT)
                timeNewRecordRT = timeNewRecordIcon.GetComponent<RectTransform>();

            StartCoroutine(NewRecordAnimation(timeNewRecordRT));
        }

        if (operationNewRecord)
        {
            if (!operationNewRecordRT)
                operationNewRecordRT = operationNewRecordIcon.GetComponent<RectTransform>();

            StartCoroutine(NewRecordAnimation(operationNewRecordRT));
        }
        
    }

    private IEnumerator FlipbookAnimation()
    {
        int flipbookPage = 4;
        int animateRound = 4;
        int allPageCount = flipbookPage * animateRound;
        int pageIndex = 0;
        float step = GameConst.completeLevelDur / allPageCount;
        WaitForSecondsRealtime animUpdateInterval = new WaitForSecondsRealtime(step);
        while (pageIndex <= allPageCount)
        {
            pageIndex++;
            float modIndex = pageIndex % flipbookPage;
            flipbookMat.SetFloat("_FlipbookPage", modIndex);
            yield return animUpdateInterval;
        }
    }

    
    
    private IEnumerator NewRecordAnimation(RectTransform rt)
    {
        float timer = 0;
        WaitForSecondsRealtime animUpdateInterval = new WaitForSecondsRealtime(GameConst.animUpdateInterval);
        while (timer <= GameConst.completeLevelDur)
        {
            timer += 0.02f;
            rt.localScale = Vector3.one*(1 + 0.2f*Mathf.Sin(timer * newRocordAnimSinFreq));
            yield return animUpdateInterval;
        }
    }
    
}
