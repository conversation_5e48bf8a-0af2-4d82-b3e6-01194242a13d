// using UnityEditor;
// using UnityEngine;
//
// [CustomEditor(typeof(MapUpdater))]
// public class MapUpdaterEditor : Editor
// {
//     private MapUpdater mapUpdater;
//     public override void OnInspectorGUI()
//     {
//         // Draw the default inspector first
//         DrawDefaultInspector();
//
//         // Get reference to the target script
//         mapUpdater = (MapUpdater)target;
//
//         // Add a button
//         if (GUILayout.Button("Retrieve"))
//         {
//             RetrieveAndShow();
//         }
//         
//     }
//
//     private async void RetrieveAndShow()
//     {
//         await mapUpdater.RetrieveAllBuiltinMaps();
//         Debug.Log("retrieved");
//         EditorGUILayout.LabelField("Retrieved", "true");
//     }
//     
// }