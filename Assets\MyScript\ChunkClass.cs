using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using Random = System.Random;
using RandomRange = UnityEngine.Random;

public class ChunkClass : MonoBehaviour
{
    //将每个链接的物体看成一个chunk
    //chunk管理贴在本chunk上的sticker，
    //通过子集和父亲的相对移动来调整中心位置
    //chunk的速度应该在gameManager中确定
    private float moveSpeed = 1;

    [Serializable]
    public class StickableClass
    {
        public StickableClass(int id, int dir, GameObject obj)
        {
            toolID = (ToolID)id;
            toolDir = (ToolDirection)dir;
            stickableObj = obj;
            stickableRenderer = obj.GetComponent<SpriteRenderer>();
            colorList = new List<ColorEnum>();
            highlightBy = -1;
            isHighlighted = false;
        }

        public bool sticked = false;
        public ToolID toolID;
        public ToolDirection toolDir;
        public GameObject stickableObj;

        [HideInInspector] public int highlightBy;
        [HideInInspector] public bool isHighlighted;
        [HideInInspector] public List<ColorEnum> colorList;

        [HideInInspector] public SpriteRenderer stickableRenderer;

        [HideInInspector] public MaterialPropertyBlock mpb;

        public Action closeToolAction;
    }

    [SerializeField] public List<StickableClass> chunkChildList = new List<StickableClass>();

    private Transform chunkTransform;

    private Vector3 presentCentre;

    private float screenVerticalBound;
    private float screenHorizontalBound;

    private ColorManager colorManager;

    private PortalConnectionManager portalConnectionManager;

    private MaterialPropertyBlock blockStickMatProp;

    private BlockShaderParam currentBSP;

    private Vector3 showInstructionOffset = 1.5f * Vector3.up;


    #region Init

    public void InitChunk()
    {
        //init parameters
        InitParameters();

        InitProp();

        //init centre
        InitCentre();
    }

    private void InitProp()
    {
        int index = 0;
        foreach (var item in chunkChildList)
        {
            //init sprite
            UpdateSprite(index, ToolID.Block);

            //init sprite color
            item.colorList = new List<ColorEnum>() { currentBSP.colorType };
            UpdateSpriteColor(index);

            //init sprite direction and show state
            GlobalMethod.OperateUIDirection(item.stickableObj, (int)ToolDirection.Original);
            GlobalMethod.HideTool(item.stickableObj);
            if (item.toolID != ToolID.Block)
                GlobalMethod.ShowTool(item.stickableObj, new BagTool(item.toolID, item.toolDir));

            item.mpb = new MaterialPropertyBlock();
            // Set a unique color
            item.mpb.SetFloat("_Hastool", 1);
            item.mpb.SetFloat("_Density", currentBSP.density);
            item.mpb.SetFloat("_Size", currentBSP.size);

            if (GameManager.Instance.ReturnPresentState() == StateEnum.GamePlay)
            {
                item.mpb.SetFloat("_Offset", RandomRange.Range(0, currentBSP.maxOffset));
                item.mpb.SetFloat("_Speed", RandomRange.Range(0.005f, currentBSP.maxSpeed));
            }
            else
            {
                //make property same to reduce draw call in editor
                item.mpb.SetFloat("_Offset", 0.5f * currentBSP.maxOffset);
                item.mpb.SetFloat("_Speed", 0.5f * currentBSP.maxSpeed);
            }

            UpdateStickState(item);

            index++;
        }
    }

    private void InitParameters()
    {
        chunkTransform = transform;
        moveSpeed = GameConst.moveToolSpeed;
        inRotateProcedure = false;
        rotateTimer = 0;
        screenVerticalBound = GameConst.verticalBound;
        screenHorizontalBound = GameConst.horizontalBound;
        colorManager = GameManager.Instance.ReturnColorManager();
        currentBSP = GlobalParameters.Instance.currentBlockShaderParam;

        blockStickMatProp = new MaterialPropertyBlock();
        blockStickMatProp.SetFloat("_Hastool", 20);

        if (portalConnectionManager == null)
            portalConnectionManager = FindObjectOfType<PortalConnectionManager>();

        playerUpdatedBlockAction = null;
    }

    private void InitCentre()
    {
        presentCentre = chunkChildList[0].stickableObj.transform.position;
        MoveChunkToCentre();
        CalculatePresentKinematic();
    }

    #endregion

    #region Tools

    private struct BagIOStruct
    {
        public int objIndex;
        public int playerIndex;
        public ToolID toolID;
        public ToolDirection toolDir;
    }

    public bool OperateTool(GameObject obj, int playerIndex, BagTool selectedTool)
    {
        int index = ReturnIndexByObj(obj);

        if (index >= 0)
        {
            //是否有符号
            if (!chunkChildList[index].sticked)
            {
                //stick
                return StickTool(index, playerIndex, selectedTool);
            }

            if (chunkChildList[index].toolID == ToolID.Stave && selectedTool.toolID == ToolID.Note)
            {
                //stick
                return StickTool(index, playerIndex, selectedTool);
            }
            
            if (chunkChildList[index].toolID == ToolID.Note && selectedTool.toolID == ToolID.Stave)
            {
                //wrong sequence
                ShowInGameInstruction(InstructionType.WrongSequenceInstruction,GameManager.Instance.playerList[playerIndex].playerObj.transform.position + showInstructionOffset, true);
                return false;
            }

            if (BagManager.Instance.IsFull())
            {
                //bag is full
                ShowInGameInstruction(InstructionType.BagFullInstruction, GameManager.Instance.playerList[playerIndex].playerObj.transform.position + showInstructionOffset, true);
                return false;
            }

            //collect
            return CollectTool(index, playerIndex);
        }

        return false;
    }

    private bool StickTool(int index, int playerIndex, BagTool selectedTool)
    {
        AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.PlayerStick);
        if (index >= 0 && !inRotateProcedure && selectedTool.toolID != ToolID.Block)
        {
            BagIOStruct newData = new BagIOStruct
            {
                objIndex = index,
                playerIndex = playerIndex,
                toolID = selectedTool.toolID,
                toolDir = selectedTool.toolDirection
            };

            if (!chunkChildList[index].sticked)
            {
                PlayerCompleteStick(newData);
                return true;
            }
            else
            {
                //check if the note is being put in stave
                if (chunkChildList[index].toolID == ToolID.Stave && selectedTool.toolID == ToolID.Note)
                {
                    //check if their direction match
                    if (GlobalMethod.PairNoteStaveDirection(selectedTool.toolDirection, chunkChildList[index].toolDir))
                    {
                        //success
                        PlayerCompleteStick(newData);
                        return true;
                    }
                    else
                    {
                        ShowInGameInstruction(InstructionType.NoteWrongDirectionInstruction,
                            GameManager.Instance.playerList[playerIndex].playerObj.transform.position +
                            showInstructionOffset, true);
                    }
                }
            }
        }

        return false;
    }

    private void PlayerCompleteStick(BagIOStruct d)
    {
        if (!GlobalParameters.Instance.editMode)
            BagManager.Instance.DeleteSelectedTool();

        if (chunkChildList[d.objIndex].toolID == ToolID.Stave && d.toolID == ToolID.Note)
        {
            CreateBeauty(d.playerIndex, chunkChildList[d.objIndex].stickableObj);
            return;
        }

        chunkChildList[d.objIndex].toolID = d.toolID;
        chunkChildList[d.objIndex].toolDir = d.toolDir;

        GlobalMethod.ShowTool(chunkChildList[d.objIndex].stickableObj, new BagTool(d.toolID, d.toolDir));

        if (d.toolID == ToolID.Flip)
            ApplyFlip(d.objIndex, d.toolDir, d.playerIndex);

        if (d.toolID == ToolID.Checkpoint)
            chunkChildList[d.objIndex].closeToolAction = AssignCheckpoint(d.objIndex, d.playerIndex);

        if (d.toolID == ToolID.Portal)
            chunkChildList[d.objIndex].closeToolAction = portalConnectionManager.AssignPortal(
                chunkChildList[d.objIndex].stickableObj,
                GameManager.Instance.playerList[d.playerIndex].playerObj);

        playerUpdatedBlockAction?.Invoke(d.playerIndex, d.objIndex);

        UpdateChunkState(d.objIndex);
    }

    public bool PlanterStickTool(int playerIndex, int index, BagTool tool)
    {
        if (index >= 0 && !inRotateProcedure && tool.toolID != ToolID.Block)
        {
            if (!chunkChildList[index].sticked)
            {
                //the block was empty
                //put the tool on the empty block
                chunkChildList[index].toolID = tool.toolID;
                chunkChildList[index].toolDir = tool.toolDirection;

                GlobalMethod.ShowTool(chunkChildList[index].stickableObj, tool);

                //flip is little bit different with others
                if (tool.toolID == ToolID.Flip)
                    ApplyFlip(index, tool.toolDirection, -1);

                if (tool.toolID == ToolID.Checkpoint)
                    chunkChildList[index].closeToolAction = AssignCheckpoint(index, playerIndex);

                if (tool.toolID == ToolID.Portal)
                    chunkChildList[index].closeToolAction =
                        portalConnectionManager.AssignPortal(chunkChildList[index].stickableObj);


                UpdateChunkState(index);
                return true;
            }
            
            //the block must have "stave"
            //then, check if the direction match
            if (GlobalMethod.PairNoteStaveDirection(tool.toolDirection, chunkChildList[index].toolDir))
            {
                //The winner is who put the note under the Switch at the first place
                //success
                if (playerIndex >= 0)
                {
                    CreateBeauty(playerIndex, chunkChildList[index].stickableObj);
                    return true;
                }
            }
            else
            {
                ShowInGameInstruction(InstructionType.NoteWrongDirectionInstruction, chunkChildList[index].stickableObj.transform.position +
                    2.5f * Vector3.up, true);
            }
        }

        return false;
    }

    private bool CollectTool(int index, int playerIndex)
    {
        AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.PlayerCollect);
        if (!BagManager.Instance.IsFull() || GlobalParameters.Instance.editMode)
        {
            if (index >= 0 && chunkChildList[index].sticked && !inRotateProcedure)
            {
                BagIOStruct newData = new BagIOStruct
                {
                    objIndex = index,
                    playerIndex = playerIndex,
                    toolID = chunkChildList[index].toolID,
                    toolDir = chunkChildList[index].toolDir
                };

                if (chunkChildList[index].toolID != ToolID.Desire)
                {
                    GlobalMethod.HideTool(chunkChildList[index].stickableObj);

                    if (chunkChildList[index].toolID == ToolID.Flip)
                        ApplyFlip(index, chunkChildList[index].toolDir, playerIndex);

                    CloseTool(index);

                    chunkChildList[index].toolID = ToolID.Block;
                    chunkChildList[index].toolDir = ToolDirection.Original;
                }

                if (!GlobalParameters.Instance.editMode)
                {
                    BagManager.Instance.AddTool(
                        new BagTool(newData.toolID, newData.toolDir));

                    if (newData.toolID == ToolID.Desire)
                    {
                        GlobalMethod.HideTool(chunkChildList[newData.objIndex].stickableObj);
                        CreateBeauty(newData.playerIndex, chunkChildList[newData.objIndex].stickableObj);
                    }

                    playerUpdatedBlockAction?.Invoke(newData.playerIndex, newData.objIndex);
                }


                UpdateChunkState(index);

                return true;
            }
        }

        return false;
    }

    public void UpdateChunkState(int index)
    {
        UpdateStickState(chunkChildList[index]);
        MoveChunkToCentre();
        CalculatePresentKinematic();
    }

    private void UpdateSprite(int index, ToolID toolID)
    {
        chunkChildList[index].stickableObj.GetComponent<SpriteRenderer>().sprite =
            ToolDataManager.Instance.ReturnToolSprite(toolID);
    }

    private void UpdateStickState(StickableClass item)
    {
        item.sticked = item.toolID != ToolID.Block;
        BlockSolidColor(item);
    }

    private void CreateBeauty(int playerIndex, GameObject stickableObj)
    {
        AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.StarGlow);
        GameManager.Instance.CompletedLevel(playerIndex, stickableObj);
    }

    #endregion

    #region Chunk motion

    private void MoveChunkToCentre()
    {
        Vector3 offset = chunkTransform.position - presentCentre;

        foreach (var item in chunkChildList)
        {
            item.stickableObj.transform.position += offset;
        }

        chunkTransform.position -= offset;
    }

    [HideInInspector] public Vector3 accumulatedMove = Vector3.zero;

    private void CalculatePresentKinematic()
    {
        CalculateMove();
    }

    private void CalculateMove()
    {
        accumulatedMove = Vector3.zero;
        foreach (var item in chunkChildList)
        {
            if (item.toolID == ToolID.Move)
            {
                accumulatedMove += GlobalMethod.ReturnVectorDirByDirectionAndTool(item.toolDir, ToolID.Move) *
                                   moveSpeed;
            }
        }
    }

    private void UpdateAfterRotation(int rotDir)
    {
        //update direction
        foreach (var item in chunkChildList)
        {
            if (item.toolID != ToolID.Rotate)
            {
                UpdateToolDirectionByRotate(item, rotDir);
            }
        }

        //calculate move after rotation
        CalculateMove();
    }

    private GameObject tempPlayerObj;

    private void ApplyFlip(int index, ToolDirection dir, int playerIndex)
    {
        presentCentre = chunkChildList[index].stickableObj.transform.position;
        MoveChunkToCentre();
        if (playerIndex >= 0)
        {
            tempPlayerObj = GameManager.Instance.playerList[playerIndex].playerObj;
            tempPlayerObj.transform.SetParent(chunkTransform);
        }

        Vector3 flipDir = GlobalMethod.ReturnVectorDirByDirectionAndTool(dir, ToolID.Flip);
        Vector3 transformedRotDir = chunkTransform.InverseTransformDirection(flipDir);
        chunkTransform.rotation *= Quaternion.AngleAxis(180, transformedRotDir);

        if (playerIndex >= 0 && (dir == ToolDirection.Original || dir == ToolDirection.Left))
            tempPlayerObj.transform.rotation *= Quaternion.AngleAxis(180, Vector3.up);

        if (playerIndex >= 0)
        {
            tempPlayerObj.transform.SetParent(GameManager.Instance.playerObjs.transform);
            tempPlayerObj.GetComponent<PlayerController>().FlipByChunk(dir);
        }

        UpdateAfterFlip(flipDir);
    }

    private void UpdateAfterFlip(Vector3 dir)
    {
        foreach (var item in chunkChildList)
        {
            UpdateToolDirectionByFlip(item, dir);
        }

        CalculatePresentKinematic();
    }

    private void UpdateToolDirectionByFlip(StickableClass item, Vector3 flipDir)
    {
        //if flipDir align with or is opposite the effected 
        //then there is no need to change the direction
        Vector3 effectedToolDir = GlobalMethod.ReturnVectorDirByDirectionAndTool(item.toolDir, item.toolID);
        bool effectCondition = Mathf.Abs(Vector3.Dot(flipDir, effectedToolDir)) < 1;
        if (effectCondition)
        {
            if (item.toolID == ToolID.Rotate)
            {
                //the "rotate" is only "two directional"
                item.toolDir = item.toolDir == ToolDirection.Original ? ToolDirection.Flip : ToolDirection.Original;
            }
            else
            {
                //flip "four directional" tool
                item.toolDir = (ToolDirection)(((int)item.toolDir + 2) % 4);
            }
        }
    }

    private void UpdateToolDirectionByRotate(StickableClass item, int rotationDir)
    {
        if (item.toolID != ToolID.Rotate)
        {
            int dirIndex = ((int)item.toolDir + rotationDir) % 4;
            if (dirIndex < 0)
                dirIndex += 4;
            item.toolDir = (ToolDirection)dirIndex;
        }
    }


    public bool inRotateProcedure { get; private set; } = false;
    private float rotateRhythm = 2;
    private float rotateTimer = 0;

    private void FixedUpdate()
    {
        ApplyMovement();
        ApplyRotation();
        UpdateAttack();
        CheckChunkInScreen();
    }

    private void ApplyMovement()
    {
        if (!inRotateProcedure)
            chunkTransform.position += accumulatedMove * Time.fixedDeltaTime;
    }

    private void ApplyRotation()
    {
        if (CanRotate(out int index))
        {
            rotateTimer += Time.fixedDeltaTime;
            if (rotateTimer >= rotateRhythm)
            {
                if (rotateProcedure != null)
                {
                    StopCoroutine(rotateProcedure);
                }

                presentCentre = chunkChildList[index].stickableObj.transform.position;
                MoveChunkToCentre();

                Vector3 rotateDir =
                    GlobalMethod.ReturnVectorDirByDirectionAndTool(chunkChildList[index].toolDir, ToolID.Rotate);

                int dir = rotateDir.z > 0 ? 1 : -1;

                rotateProcedure = RotateProcedure(0.5f, dir);
                StartCoroutine(rotateProcedure);

                rotateTimer = 0;
            }
        }
        else
        {
            rotateTimer = 0;
        }
    }

    private void CheckChunkInScreen()
    {
        bool outOfScreen = chunkChildList.All(i =>
            Mathf.Abs(i.stickableObj.transform.position.x) > screenHorizontalBound + 3 ||
            Mathf.Abs(i.stickableObj.transform.position.y) > screenVerticalBound + 3);
        if (outOfScreen)
        {
            GlobalParameters.Instance.chunkPool.ReturnObj(this);
            GlobalParameters.Instance.SetCsIndexDict();
            
            ShowInGameInstruction(InstructionType.QuitGameInstruction, new Vector3(2, -6, 0), true);
        }
    }

    private bool CanRotate(out int index)
    {
        int count = 0;
        index = 0;
        for (int i = 0; i < chunkChildList.Count; i++)
        {
            if (chunkChildList[i].toolID == ToolID.Rotate)
            {
                index = i;
                count++;
            }
        }

        return count == 1;
    }

    private event Action<Vector3> moveByAttach;

    public void MoveByAttach(Vector3 move)
    {
        chunkTransform.position += move;
        moveByAttach?.Invoke(move);
    }

    private event Action chunkChangeByAttach;

    public void ChunkChangeByAttach()
    {
        chunkChangeByAttach?.Invoke();
    }

    #endregion

    #region Attack functionality

    private void UpdateAttack()
    {
        if (!inRotateProcedure)
        {
            for (int i = 0; i < chunkChildList.Count; i++)
            {
                if (chunkChildList[i].toolID == ToolID.Attack)
                    ApplyAttack(i);
            }
        }
    }

    private Collider2D attackedCollider;

    private void ApplyAttack(int index)
    {
        float boxThickness = 0.05f;
        Vector3 attackDir =
            GlobalMethod.ReturnVectorDirByDirectionAndTool(chunkChildList[index].toolDir, ToolID.Attack);
        Vector3 attackBoxPos =
            attackDir * (boxThickness + .55f) + chunkChildList[index].stickableObj.transform.position;
        Vector2 attackBoxScale = new Vector2(boxThickness + Mathf.Abs(attackDir.y) * 0.3f,
            boxThickness + Mathf.Abs(attackDir.x) * 0.3f);

        attackedCollider = Physics2D.OverlapBox(attackBoxPos, attackBoxScale, 0);
        if (attackedCollider != null)
        {
            attackedCollider.GetComponent<IAlive>()?.GotAttacked();
        }
    }

    #endregion

    #region Coroutine

    private IEnumerator rotateProcedure;
    private WaitForSeconds rotateDelta = new WaitForSeconds(0.01f);
    private float rotateDeltaFloat = 0.01f;

    private IEnumerator RotateProcedure(float procedureDur, int rotateDirection)
    {
        //在此过程中，停止移动
        inRotateProcedure = true;
        float timer = 0;
        bool ifChunkFlipped = chunkTransform.forward.z < 0;
        Quaternion presentRot = chunkTransform.rotation;
        float rotateAngle = ifChunkFlipped ? -rotateDirection * 90 : rotateDirection * 90;
        Quaternion targetRot = presentRot * Quaternion.AngleAxis(rotateAngle, Vector3.forward);
        while (timer <= procedureDur)
        {
            timer += rotateDeltaFloat;
            chunkTransform.rotation = Quaternion.Lerp(presentRot, targetRot, timer / procedureDur);
            yield return rotateDelta;
        }

        UpdateAfterRotation(rotateDirection);

        inRotateProcedure = false;
    }

    #endregion

    #region Helper

    public void SubscribeToAttachEvent(Action<Vector3> moveAction, Action stickableAction)
    {
        moveByAttach += moveAction;
        chunkChangeByAttach += stickableAction;
    }

    public void UnsubscribeToAttachEvent(Action<Vector3> moveAction, Action stickableAction)
    {
        moveByAttach -= moveAction;
        chunkChangeByAttach -= stickableAction;
    }

    public void CloseAllTools()
    {
        //invoke all "closeToolAction"
        for (int i = 0; i < chunkChildList.Count; i++)
        {
            CloseTool(i);
        }

        chunkChildList.Clear();
    }

    private void CloseTool(int index)
    {
        if (index >= 0 && index < chunkChildList.Count)
        {
            chunkChildList[index].closeToolAction?.Invoke();
            chunkChildList[index].closeToolAction = null;
        }
    }

    private void ShowInGameInstruction(InstructionType type, Vector3 pos, bool show)
    {
        GameManager.Instance.ShowInGameInstruction(type, pos, show);
    }

    private Action AssignCheckpoint(int index, int playerIndex)
    {
        PlayerController controller = GameManager.Instance.playerList[playerIndex].playerController;
        Vector3 checkpointDir =
            GlobalMethod.ReturnVectorDirByDirectionAndTool(chunkChildList[index].toolDir, ToolID.Checkpoint);
        Vector3 playerRevivePos = chunkChildList[index].stickableObj.transform.position + checkpointDir;
        GameObject reviveIndicator = ReviveSpotIndicator(playerIndex, index, playerRevivePos);

        //if there is a checkpoint created by the same player
        //then clear the former before creating a new one
        controller.ClearCheckpointAction();

        //assign check point action
        controller.CreateCheckpointAction(reviveIndicator);

        return controller.ClearCheckpointAction;
    }

    private GameObject ReviveSpotIndicator(int playerIndex, int index, Vector3 position)
    {
        GameObject o = new GameObject("ReviveSpotIndicator");
        SpriteRenderer spriteRenderer = o.AddComponent<SpriteRenderer>();
        spriteRenderer.sprite = ToolDataManager.Instance.ReturnToolSprite(ToolID.Player);
        Color playerColor = GameManager.Instance.ReturnInGamePlayerColorByIndex(playerIndex);
        playerColor.a = 0.5f;
        spriteRenderer.color = playerColor;
        o.transform.position = position;
        o.transform.SetParent(chunkChildList[index].stickableObj.transform);
        return o;
    }

    public void PlayerAddHighLight(GameObject obj)
    {
        int index = ReturnIndexByObj(obj);
        if (index >= 0)
        {
            AddHighLight(index, ColorEnum.PlayerHighLightBlock);
        }
    }

    public void AddHighLight(int index, ColorEnum colorType)
    {
        //remove planter highlight
        chunkChildList[index].colorList.RemoveAll(i => i == ColorEnum.PlanterHighLightBlock);

        //don't worry if this would overwrite the highlight of player
        //because, planter won't consider the block selected by player
        chunkChildList[index].colorList.Add(colorType);

        UpdateHighlightBy(index);

        chunkChildList[index].isHighlighted = chunkChildList[index].colorList.Count > 1;

        UpdateSpriteColor(index);
    }

    public void PlayerRemoveHighLight(GameObject obj)
    {
        int index = ReturnIndexByObj(obj);
        if (index >= 0)
        {
            RemoveHighLight(index);
        }
    }

    public void RemoveHighLight(int index)
    {
        int count = chunkChildList[index].colorList.Count;
        if (count > 1)
        {
            chunkChildList[index].colorList.RemoveAt(count - 1);
        }

        UpdateHighlightBy(index);

        chunkChildList[index].isHighlighted = chunkChildList[index].colorList.Count > 1;

        UpdateSpriteColor(index);
    }

    private void UpdateHighlightBy(int index)
    {
        switch (chunkChildList[index].colorList.Last())
        {
            case ColorEnum.PlayerHighLightBlock:
                chunkChildList[index].highlightBy = (int)ToolID.Player;
                break;
            case ColorEnum.PlanterHighLightBlock:
                chunkChildList[index].highlightBy = (int)ToolID.Planter;
                break;
            default:
                chunkChildList[index].highlightBy = -1;
                break;
        }
    }

    private void UpdateSpriteColor(int index)
    {
        Color currentColor = colorManager.ReturnColorByType(chunkChildList[index].colorList.Last());
        if (chunkChildList[index].colorList.Last() == ColorEnum.PlayerHighLightBlock)
            currentColor = colorManager.ReturnColorByType(currentBSP.colorType) * 1.3f;
        chunkChildList[index].stickableRenderer.color = currentColor;
        BlockSolidColor(chunkChildList[index]);
    }

    public int ReturnIndexByObj(GameObject obj)
    {
        int index = -1;
        for (int i = 0; i < chunkChildList.Count; i++)
        {
            if (chunkChildList[i].stickableObj == obj)
                index = i;
        }

        return index;
    }

    public bool RemoveTrash(GameObject obj)
    {
        bool isTrash = chunkChildList.Any(i => (i.toolID == ToolID.Trash && i.stickableObj == obj));

        if (isTrash)
        {
            int index = ReturnIndexByObj(obj);
            GlobalMethod.HideTool(chunkChildList[index].stickableObj);

            chunkChildList[index].toolID = ToolID.Block;
            chunkChildList[index].toolDir = ToolDirection.Original;

            UpdateStickState(chunkChildList[index]);
            return true;
        }

        return false;
    }

    public bool TransmitDesire(GameObject obj)
    {
        int index = ReturnIndexByObj(obj);
        if (index >= 0)
        {
            if (chunkChildList[index].toolID == ToolID.Desire)
            {
                GlobalMethod.HideTool(chunkChildList[index].stickableObj);

                chunkChildList[index].toolID = ToolID.Block;
                chunkChildList[index].toolDir = ToolDirection.Original;
                UpdateStickState(chunkChildList[index]);
                return true;
            }
        }

        return false;
    }

    public bool GotEmptySpace()
    {
        return chunkChildList.Any(i => !i.sticked && !i.isHighlighted);
    }

    public bool GotStave(out int index)
    {
        index = -1;
        for (int i = 0; i < chunkChildList.Count; i++)
        {
            if (chunkChildList[i].toolID == ToolID.Stave)
            {
                index = i;
                return true;
            }
        }

        return false;
    }

    public bool GotChildInRange(Vector3 pos, float range)
    {
        return chunkChildList.Any(i => Vector3.Distance(i.stickableObj.transform.position, pos) <= range);
    }

    private Random _random = new Random();

    public int ReturnEmptyRandomIndex()
    {
        var pick = chunkChildList.Select((p, i) => new { Class = p, Index = i })
            .Where(i => !i.Class.sticked && !i.Class.isHighlighted)
            .OrderBy(i => _random.Next())
            .Select(x => x.Index)
            .Take(1);

        int pickedIndex = pick.ElementAt(0);

        return pickedIndex;
    }

    private Action<int, int> playerUpdatedBlockAction;

    public void AddListenerForChunkToolUpdate(Action<int, int> cAction)
    {
        playerUpdatedBlockAction += cAction;
    }

    public void RemoveListenerForChunkToolUpdate(Action<int, int> cAction)
    {
        playerUpdatedBlockAction -= cAction;
    }

    private void BlockSolidColor(StickableClass item)
    {
        if (item.sticked || item.isHighlighted)
        {
            item.stickableRenderer.SetPropertyBlock(blockStickMatProp);
        }
        else
        {
            item.stickableRenderer.SetPropertyBlock(item.mpb);
        }
    }

    #endregion
}