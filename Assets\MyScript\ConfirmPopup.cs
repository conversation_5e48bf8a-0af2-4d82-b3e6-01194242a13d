using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ConfirmPopup : MonoBehaviour
{
    [SerializeField] private TMP_Text warningTMP;

    [SerializeField] private Button confirmBtn;

    [SerializeField] private Button cancelBtn;
    
    

    public void ShowConfirmPopup(string warningKey, Action ctx)
    {
        gameObject.SetActive(true);
        warningTMP.text = MyLocalizationManager.Instance.RetrieveStringByKey(warningKey);
        
        confirmBtn.onClick.AddListener(()=> ctx?.Invoke());
        cancelBtn.onClick.AddListener(CancelBtnAction);
    }

    private void CancelBtnAction()
    {
        confirmBtn.onClick.RemoveAllListeners();
        cancelBtn.onClick.RemoveAllListeners();
        gameObject.SetActive(false);
    }
}
