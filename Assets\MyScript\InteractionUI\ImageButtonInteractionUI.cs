using UnityEngine;
using UnityEngine.UI;

public class ImageButtonInteractionUI : InteractionUI
{
    private Color imageHighLightColor;

    private Color imageNormalColor;
    

    protected override void DecideColor()
    {
        imageHighLightColor = ReturnColor(ColorEnum.UIButtonHighLight);
        imageNormalColor = ReturnColor((ColorEnum.UIButtonOriginal));
    }

    protected override void HighLight(int index, bool highlight)
    {
        if (index >= 0 && index < buttonList.Count && !disabledButtonList.Contains(index))
            buttonList[index].GetComponent<Image>().color = highlight ? imageHighLightColor : imageNormalColor;
    }
    
    
    
    public override void UIRespondPlayerSubmitInput()
    {
        TriggerSelectedButtonEvent();
    }

    public override void UIRespondPlayerVerticalInput(int changeAmount)
    {
        
        changeAmount *= -1;
        UpdateSelection(changeAmount);
    }

}
