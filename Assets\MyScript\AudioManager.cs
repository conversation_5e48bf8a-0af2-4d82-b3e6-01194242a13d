using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using Random = UnityEngine.Random;

public class AudioManager : MonoBehaviour
{
    [Serializable]
    public class AudioClipClass
    {
        public PlayerAudioEnum playerAudioName;
        public AudioClip audioClip;
        public float volumeRatio = 1;
    }
    
    [SerializeField]
    public List<AudioClipClass> audioClipList;
    

    [HideInInspector] public List<AudioSource> playerAudioSourceList = new List<AudioSource>();

    [Serializable]
    public class BGMClipClass
    {
        public BGMType bgmType;
        public bool multipleClip;
        public AudioClip[] clipArr;
    }

    [SerializeField] public List<BGMClipClass> bgmClipList = new List<BGMClipClass>();

    private Dictionary<BGMType, BGMClipClass> bgmClipClassDict = new Dictionary<BGMType, BGMClipClass>();
    

    public AudioSource BGMAudioSource;

    [SerializeField] private AudioSource DialogueAudioSource;
    
    
    [Range(0, 10)]
    public int defaultMusicVolume;
    [Range(0, 10)]
    public int defaultSoundVolume;

    private int musicVolume;
    private int soundVolume;

    [SerializeField] private TMP_Text musicVolumeText;
    [SerializeField] private TMP_Text soundVolumeText;

    [SerializeField] private GameObject addMusicVolumeButton;
    [SerializeField] private GameObject decreaseMusicVolumeButton;
    [SerializeField] private GameObject addSoundVolumeButton;
    [SerializeField] private GameObject decreaseSoundVolumeButton;

    private int volumeScale = 10;

    private int playerCount;

    private string playerPrefsMusicKey = "MusicVolume";
    private string playerPrefsSoundKey = "SoundVolume";

    private ChunkAttachEffect chunkAttachEffect;
    

    public static AudioManager Instance;
    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(this);
        playerCount = 0;
        InitAudio();
        PlayBGM(BGMType.Menu);
    }

    private void InitAudio()
    {
        InitBGMDict();
        InitVolume();
        AssignPlayerAudioSource();
        UpdateMusicVolume();
        UpdateSoundVolume();
    }

    private void InitBGMDict()
    {
        bgmClipClassDict.Clear();
        foreach (var clipClass in bgmClipList)
        {
            bgmClipClassDict.Add(clipClass.bgmType, clipClass);
        }
    }

    private void InitVolume()
    {
        if (PlayerPrefs.HasKey(playerPrefsMusicKey))
        {
            musicVolume = PlayerPrefs.GetInt(playerPrefsMusicKey);
        }
        else
        {
            musicVolume = defaultMusicVolume;
        }

        if (PlayerPrefs.HasKey(playerPrefsSoundKey))
        {
            soundVolume = PlayerPrefs.GetInt(playerPrefsSoundKey);
        }
        else
        {
            soundVolume = defaultSoundVolume;
        }
    }
    

    private void AssignPlayerAudioSource()
    {
        //if player count changed then we need to rearrange the audio source
        if (playerCount != GameManager.Instance.playerList.Count)
        {
            playerAudioSourceList.Clear();
            foreach (var obj in GameManager.Instance.playerList)
            {
                playerAudioSourceList.Add(obj.playerAudioSource);
            }
        }
    }
    
    private void SetVolume(AudioSource source, int volume)
    {
        source.volume = (float)volume/volumeScale;
    }

    private void SetVolume(AudioSource source, float volume)
    {
        source.volume = (float)volume/volumeScale;
    }
    
    private void SetMusicVolumeText()
    {
        musicVolumeText.text = musicVolume.ToString();
    }

    private void SetSoundVolumeText()
    {
        soundVolumeText.text = soundVolume.ToString();
    }
    
    private void SetAllPlayerVolume(int volume)
    {
        foreach (var source in playerAudioSourceList)
        {
            SetVolume(source, volume);
        }
    }

    private void UpdateMusicVolume()
    {
        PlayerPrefs.SetInt(playerPrefsMusicKey, musicVolume);
        SetVolume(BGMAudioSource, musicVolume);
        SetMusicVolumeText();
        UpdateMusicButtonAppearance();
    }

    private void UpdateMusicButtonAppearance()
    {
        addMusicVolumeButton.SetActive(musicVolume != volumeScale);
        decreaseMusicVolumeButton.SetActive(musicVolume != 0);
    }

    private void UpdateSoundVolume()
    {
        PlayerPrefs.SetInt(playerPrefsSoundKey, soundVolume);
        SetVolume(DialogueAudioSource, soundVolume);
        SetAllPlayerVolume(soundVolume);
        SetSoundVolumeText();
        UpdateSoundButtonAppearance();

        if (chunkAttachEffect == null)
            chunkAttachEffect = FindObjectOfType<ChunkAttachEffect>();
        chunkAttachEffect.SetVolume((float)soundVolume/volumeScale);
    }
    

    private void UpdateSoundButtonAppearance()
    {
        addSoundVolumeButton.SetActive(soundVolume != volumeScale);
        decreaseSoundVolumeButton.SetActive(soundVolume != 0);
    }

    #region API

    private BGMType currentBGMType;
    public void PlayBGM(BGMType type)
    {
        currentBGMType = type;
        //stop current audio
        BGMAudioSource.Stop();
        
        //stop coroutine if there is one
        if(playChapterBGM != null)
            StopCoroutine(playChapterBGM);
        
        //switch clip
        SetBGMAudioClip();
        
        BGMAudioSource.Play();
        
        playChapterBGM = PlayChapterBGM(BGMAudioSource.clip.length);
        StartCoroutine(playChapterBGM);
    }
    
    public void PlayerAudioSourcePlay(int playerIndex, PlayerAudioEnum playerAudioEnum)
    {
        playerAudioSourceList[playerIndex].clip = audioClipList[(int)playerAudioEnum].audioClip;
        SetVolume(playerAudioSourceList[playerIndex], soundVolume*audioClipList[(int)playerAudioEnum].volumeRatio);
        playerAudioSourceList[playerIndex].Play();
    }

    public void PlayerAudioSourceStop(int playerIndex)
    {
        playerAudioSourceList[playerIndex].Stop();
    }

    public void DialogueAudioSourcePlay(AudioClip dialogueClip, float pitch)
    {
        DialogueAudioSource.clip = dialogueClip;
        DialogueAudioSource.pitch = pitch;
        DialogueAudioSource.Play();
    }

    public void AddMusicVolumeButtonAction()
    {
        if (musicVolume < volumeScale)
        {
            musicVolume++;
            UpdateMusicVolume();
        }
            
    }

    public void DecreaseMusicVolumeButtonAction()
    {
        if (musicVolume > 0)
        {
            musicVolume--;
            UpdateMusicVolume();
        }
    }

    public void AddSoundVolumeButtonAction()
    {
        if (soundVolume < volumeScale)
        {
            soundVolume++;
            UpdateSoundVolume();
        }
    }

    public void DecreaseSoundVolumeButtonAction()
    {
        if (soundVolume > 0)
        {
            soundVolume--;
            UpdateSoundVolume();
        }
    }

    private int inDialogueMusicVolume = 1;
    public void VolumeSetupInDialogue(bool inDialogue)
    {
        if (inDialogue)
        {
            if(musicVolume > 1)
               SetVolume(BGMAudioSource, inDialogueMusicVolume);
        }
        else
        {
            SetVolume(BGMAudioSource, musicVolume);
        }
    }
    #endregion

    private void SetBGMAudioClip()
    {
        if (bgmClipClassDict[currentBGMType].multipleClip)
        {
            int clipCount = bgmClipClassDict[currentBGMType].clipArr.Length;
            
            int randomIndex = ReturnBGMIndex(clipCount);
            BGMAudioSource.clip = bgmClipClassDict[currentBGMType].clipArr[randomIndex];
        }
        else
        {
            BGMAudioSource.clip = bgmClipClassDict[currentBGMType].clipArr[0];
        }
    }

    private int ReturnBGMIndex(int count)
    {
        int index = 0;

        if (GameManager.Instance.presentProgress.levelResultList != null)
        {
            int level = GameManager.Instance.presentProgress.levelResultList.Count % 32;
            //over half of chapter
            if (level >= 16)
                index = Random.Range(0, count);
        }
        
        return index;
    }
    private IEnumerator playChapterBGM;
    private IEnumerator PlayChapterBGM(float clipLength)
    {
        yield return new WaitForSecondsRealtime(clipLength);
        PlayBGM(currentBGMType);
    }

    
    
}
