using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Events;



public class DropdownStateComponent : MonoBehaviour
{
    private TMP_Dropdown dropdown;

    private List<string> currentOptionKeys = new List<string>();

    private int currentSelectedState = 0;
    private bool hasInitialized = false;
    

    private void Start()
    {
        //subscribe to language change
        MyLocalizationManager.Instance.SubscribeLanguageChangeAction(UpdateLanguageAction);
    }

    public void InitDropdown(List<string> optionKeys, UnityAction<int> action)
    {
        if (!hasInitialized)
        {
            dropdown = GetComponent<TMP_Dropdown>();
            currentOptionKeys = optionKeys;
            dropdown.ClearOptions();
            dropdown.AddOptions(UpdateLanguage(optionKeys));
            dropdown.onValueChanged.AddListener(action);
            hasInitialized = true;
        }
    }

    private void UpdateLanguageAction()
    {
        if (hasInitialized)
        {
            dropdown.ClearOptions();
            dropdown.AddOptions(UpdateLanguage(currentOptionKeys));
        }
    }

    private List<string> UpdateLanguage(List<string> optionKeys)
    {
        List<string> newList = new List<string>();
        foreach (string s in optionKeys)
        {
            newList.Add(MyLocalizationManager.Instance.RetrieveStringByKey(s));
        }

        return newList;
    }
    
}