using TMPro;
using UnityEngine;

public class TextButtonInteractionUI : InteractionUI
{
    private Color textHighLightColor;
    private Color textNormalColor;
    
    protected override void DecideColor()
    {
        textHighLightColor = ReturnColor(ColorEnum.UIButtonHighLight);
        textNormalColor = ReturnColor(ColorEnum.UIButtonOriginal);
    }
    protected override void HighLight(int index, bool highlight)
    {
        if (index >= 0 && index < buttonList.Count)
        {
            buttonList[index].GetComponent<TMP_Text>().color = highlight ? textHighLightColor : textNormalColor;
        }
    }

    public override void UIRespondPlayerSubmitInput()
    {
        TriggerSelectedButtonEvent();
    }

    public override void UIRespondPlayerVerticalInput(int changeAmount)
    {
        changeAmount *= -1;
        UpdateSelection(changeAmount);
    }

}
