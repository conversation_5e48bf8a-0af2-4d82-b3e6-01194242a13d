using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI; 
using System.Text;

public class CollectionEditorDataAssigner : MonoBehaviour
{
    [SerializeField] private Transform content;
    [SerializeField] private GameObject itemTemplate;
    [SerializeField] private MapDataAssigner mapDataAssigner;
    [SerializeField] private CollectionEditorInspector inspectorDataAssigner;
    [SerializeField] private int itemObjCount = 13;
    [SerializeField] private RectTransform upperBound;
    [SerializeField] private RectTransform lowerBound;

    private class CollectionObj
    {
        public CollectionObj(GameObject obj, int index)
        {
            UIObj = obj;
            itemDataAssigner = obj.GetComponent<MapOrCollectionItemDataAssigner>();
            rectTransform = obj.GetComponent<RectTransform>();
            collectionIndex = index;
            
        }

        public GameObject UIObj;
        public MapOrCollectionItemDataAssigner itemDataAssigner;
        public RectTransform rectTransform;
        public int collectionIndex;
    }

    private List<CollectionObj> collectionObjList = new List<CollectionObj>();
    
    private List<CollectionClass> allCollectionList;

    private int currentCollectionListCount;

    private RectTransform contentRectTransform;
    private VerticalLayoutGroup contentVerticalLayoutGroup;

    private int headPointer;
    private int rearPointer;

    private bool firstTime = true;

    private int selectedObjIndex = -1;

    private int selectedCollectionIndex;

    public void ShowCollection()
    {
        canScroll = false;
        SetParameters();
        UpdateCollection();
        ClearSelection();
        canScroll = true;
        
        mapDataAssigner.ClearMap();
        inspectorDataAssigner.ClearInspector();
        firstTime = false;
    }

    private void SetParameters()
    {
        if (firstTime)
        {
            allCollectionList = GlobalParameters.Instance.allCollectionList;

            contentRectTransform = content.GetComponent<RectTransform>();
            contentVerticalLayoutGroup = content.GetComponent<VerticalLayoutGroup>();
        }


        contentRectTransform.anchoredPosition = new Vector2(0, 0);
        UpdateItemCountAndContentVerticalSize();
        
        posDiff = 0;
        
        headPointer = 0;
        rearPointer = itemObjCount - 1;
    }

    private void UpdateCollection()
    {
        if (itemObjCount != collectionObjList.Count)
        {
            collectionObjList.Clear();
            for (int i = 0; i < itemObjCount; i++)
            {
                if (i == 0)
                    collectionObjList.Add(new CollectionObj(itemTemplate, i));
                else
                {
                    collectionObjList.Add(new CollectionObj(Instantiate(itemTemplate, content), i));
                }

                AssignOneButtonEvent(i);
            }
        }

        for (int i = 0; i < itemObjCount; i++)
        {
            AssignOneData(i, i);
        }
    }

    private void AssignOneData(int objIndex, int sequence)
    {
        int collectionIndex = QueryCollectionIndex(sequence);
        if (collectionIndex >= 0)
        {
            CorrectPlayerCountMax(collectionIndex);
            collectionObjList[objIndex].UIObj.SetActive(true);
            collectionObjList[objIndex].collectionIndex = collectionIndex;
            collectionObjList[objIndex].itemDataAssigner.SetCollectionData(allCollectionList[collectionIndex]);
        }
        else
        {
            collectionObjList[objIndex].UIObj.SetActive(false);
        }
    }

    private int QueryCollectionIndex(int sequence)
    {
        int count = 0;
        int index = -1;
        for (int i = 0; i < allCollectionList.Count; i++)
        {
            if (FitCondition(i))
            {
                if (count == sequence)
                    index = i;
                count++;
            }
        }

        return index;
    }

    private void AssignOneButtonEvent(int index)
    {
        collectionObjList[index].UIObj.GetComponent<Button>().onClick
            .AddListener(() => ClickedUIObjButtonAction(index));
    }

    private void ClickedUIObjButtonAction(int index)
    {
        //highlight selected item
        HighlightObj(index);
        mapDataAssigner.ClearSelection();
        
        selectedCollectionIndex = collectionObjList[index].collectionIndex;
        inspectorDataAssigner.ShowCollectionInspector(allCollectionList[selectedCollectionIndex], collectionObjList[index].itemDataAssigner);
        mapDataAssigner.ShowMap(allCollectionList[selectedCollectionIndex]);
    }
    
    private void HighlightObj(int index)
    {
        if (index != selectedObjIndex)
        {
            if (selectedObjIndex >= 0)
                collectionObjList[selectedObjIndex].itemDataAssigner.IfItemSelected(false);
            
            if(index >= 0)
                collectionObjList[index].itemDataAssigner.IfItemSelected(true);
            
            selectedObjIndex = index;
        }
        
    }

    public void ClearSelection()
    {
        selectedCollectionIndex = -1;
        
        if (selectedObjIndex >= 0)
            collectionObjList[selectedObjIndex].itemDataAssigner.IfItemSelected(false);
        selectedObjIndex = -1;
    }

    private bool FitCondition(int index)
    {
        bool showChapter = false;
#if UNITY_EDITOR
        showChapter = true;
#endif
        bool chapterMatch = showChapter || !allCollectionList[index].isChapter;

        return chapterMatch;
    }
    
    private void CorrectPlayerCountMax(int collectionIndex)
    {
        if (allCollectionList[collectionIndex].playerCountMax < allCollectionList[collectionIndex].playerCount)
            allCollectionList[collectionIndex].playerCountMax = allCollectionList[collectionIndex].playerCount;
    }

    private void UpdateItemCountAndContentVerticalSize()
    {
        currentCollectionListCount = allCollectionList.Count;
        contentRectTransform.sizeDelta =
            new Vector2(contentRectTransform.sizeDelta.x, currentCollectionListCount * changeStep);
    }

    #region Scroll move & arrange

    private float changeStep = 60;
    
    private float contentPosY;
    private float posDiff;
    private bool canScroll = false;

    private void Update()
    {
        if (canScroll)
        {
            CheckStep();
        }
    }

    private void CheckStep()
    {
        float diff = contentRectTransform.anchoredPosition.y - contentPosY;
        contentPosY = contentRectTransform.anchoredPosition.y;
        
        if (Mathf.Abs(diff) > 0.5f)
            posDiff = diff;
        

        if (upperBound == null || lowerBound == null)
            return;
        
        if (posDiff > 0 && collectionObjList[headPointer].rectTransform.position.y > upperBound.position.y)
        {
            MoveItem(1);
        }else if (posDiff < 0 && collectionObjList[rearPointer].rectTransform.position.y < lowerBound.position.y)
        {
            MoveItem(-1);
        }
        
    }

    private void MoveItem(int dir)
    {
        if (dir > 0)
        {
            //check if there is new data behind rear
            //the head button goes to rear, and take the new data
            int newDataIndex = collectionObjList[rearPointer].collectionIndex + 1;
            if (newDataIndex < currentCollectionListCount)
            {
                collectionObjList[headPointer].UIObj.transform.SetSiblingIndex(itemObjCount - 1);
                AssignOneData(headPointer, newDataIndex);
                
                if(headPointer == selectedObjIndex)
                    HighlightObj(-1);
                if(collectionObjList[headPointer].collectionIndex == selectedCollectionIndex)
                    HighlightObj(headPointer);
                
                rearPointer = headPointer;
                headPointer = headPointer + 1 >= itemObjCount ? 0 : headPointer + 1;
                contentVerticalLayoutGroup.padding.top += (int)changeStep;
            }
        }
        else
        {
            //check if there is new data before head
            //the rear goes to head, and take the new data
            int newDataIndex = collectionObjList[headPointer].collectionIndex - 1;
            if (newDataIndex >= 0)
            {
                collectionObjList[rearPointer].UIObj.transform.SetSiblingIndex(0);
                AssignOneData(rearPointer, newDataIndex);
                
                if(rearPointer == selectedObjIndex)
                    HighlightObj(-1);
                if(collectionObjList[rearPointer].collectionIndex == selectedCollectionIndex)
                    HighlightObj(rearPointer);
                
                headPointer = rearPointer;
                rearPointer = rearPointer - 1 < 0 ? itemObjCount - 1 : rearPointer - 1;
                contentVerticalLayoutGroup.padding.top -= (int)changeStep;
            }
        }
    }

    #endregion

    #region Button action

    private StringBuilder collectionNameSB = new StringBuilder();
    public void AddCollectionButtonAction()
    {
        CollectionClass newCollection = new CollectionClass();
        collectionNameSB.Clear();
        collectionNameSB.Append("New Collection");
        GlobalParameters.Instance.GetNewCollectionName(collectionNameSB, 0);
        newCollection.name = collectionNameSB.ToString();
        newCollection.playerCount = 1;
        
        allCollectionList.Insert(0, newCollection);
        GlobalParameters.Instance.SaveCollectionList();
        
        ShowCollection();
    }

    public void DuplicateButtonAction()
    {
        //chapter cannot be copied
        if (selectedCollectionIndex >= 0 && selectedCollectionIndex < allCollectionList.Count)
        {
            if (!allCollectionList[selectedCollectionIndex].isChapter)
            {
                CollectionClass newCollection = new CollectionClass();
                collectionNameSB.Clear();
                collectionNameSB.Append(allCollectionList[selectedCollectionIndex].name);
                GlobalParameters.Instance.GetNewCollectionName(collectionNameSB, 0);
                newCollection.name = collectionNameSB.ToString();
                newCollection.description = allCollectionList[selectedCollectionIndex].description;
                newCollection.mapIDList = allCollectionList[selectedCollectionIndex].mapIDList;
            
                allCollectionList.Insert(0, newCollection);
                GlobalParameters.Instance.SaveCollectionList();
            
                ShowCollection();
            }
        }
    }

    public void DeleteButtonAction()
    {
        if (selectedCollectionIndex >= 0 && selectedCollectionIndex < allCollectionList.Count)
        {
            if (!allCollectionList[selectedCollectionIndex].isChapter)
            {
                allCollectionList.RemoveAt(selectedCollectionIndex);
                GlobalParameters.Instance.SaveCollectionList();
                ShowCollection();
            }
        }
    }
    

    #endregion
}