using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class MapOrCollectionItemDataAssigner : MonoBehaviour
{
    [SerializeField] private TMP_Text name;

    [SerializeField] private TMP_Text player;

    private bool hasInit = false;

    private Color selectedColor;
    private Color normalColor;
    private Color disabledColor;

    private bool itemSelected = false;

    public void SetMapData(MapPreview map)
    {
        name.text = map.mapName;
        int playerCount = map.playerCount;
        int playerCountMax = map.playerCountMax;
        player.text = playerCount == playerCountMax ? $"{playerCount}" : $"{playerCount}~{playerCountMax}";
    }

    public void MapFitCollection(bool fit)
    {
        name.fontStyle = fit ? FontStyles.Normal :FontStyles.Strikethrough;
    }

    public void SetCollectionData(CollectionClass collection)
    {
        name.text = collection.name;
        int playerCount = collection.playerCount;
        int playerCountMax = collection.playerCountMax;
        player.text = playerCount == playerCountMax ? $"{playerCount}" : $"{playerCount}~{playerCountMax}";
    }

    public void IfItemSelected(bool selected)
    {
        SetParameters();
        itemSelected = selected;
        name.color = selected ? selectedColor : normalColor;
    }

    private void SetParameters()
    {
        if (!hasInit)
        {
            normalColor = GameManager.Instance.ReturnColorManager().UIButtonOriginal;
            selectedColor = GameManager.Instance.ReturnColorManager().UIButtonHighLight;
            disabledColor = GameManager.Instance.ReturnColorManager().UIButtonDisabled;
        }
    }
}
