using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.InputSystem;


public class MapEditor : MonoBehaviour
{
    public GameObject gridTile;

    public GameObject gridObjs;

    public Color gridOriginalColor;
    public Color gridHighlightColor;
    public Color gridSelectedHighlightColor;

    private Camera mainCamera;
    [SerializeField] private CameraBehavior cameraBehavior;

    [SerializeField] private GameObject presentBrush;
    private SpriteRenderer presentBrushRenderer;

    [SerializeField] private GameObject objectBarObj;

    [SerializeField] private TMP_Text levelNameTMP;

    private GameObject objectBarContent;
    private RectTransform objectBarContentRT;
    private GameObject objectBarContentPage;
    private int pageCount;
    private int pageIndex;
    private int pageCapacity;
    private Vector2 centrePagePos;
    private List<GameObject> pageList = new List<GameObject>();

    private class GridClass
    {
        public GridClass(Vector3 index, GameObject obj, bool has)
        {
            //Vector3 startPos = new Vector3(-Mathf.Floor(width / 2), -Mathf.Floor(height / 2));
            gridPos = gridStartPos + index;
            gridObj = obj;
            gridObjSR = obj.GetComponent<SpriteRenderer>();
            levelObj = null;
            gridTool = new BagTool();
            hasBlock = has;
            hasChunk = false;
        }

        public Vector3 gridPos;
        public GameObject gridObj;
        public SpriteRenderer gridObjSR;
        public GameObject levelObj;
        public BagTool gridTool;
        public bool hasBlock;
        public bool hasChunk;

        public static Vector2 ConvertPositionToIndex(Vector3 pos)
        {
            return pos - gridStartPos;
        }
    }

    private GridClass[,] gridArr;

    private bool editorActivated = true;

    private static int width = 27;
    private static int height = 15;
    private static Vector3 gridStartPos = new Vector3(-Mathf.Floor(width / 2), -Mathf.Floor(height / 2));
    private static Vector2 verticalBound = new Vector2(-Mathf.Floor(height / 2), Mathf.Floor(height / 2));
    private static Vector2 horizontalBound = new Vector2(-Mathf.Floor(width / 2), Mathf.Floor(width / 2));

    private Vector2 presentHoveredIndex;
    private Vector2 dragStartIndex;
    private GridClass presentClickedGrid;

    private BagTool selectedTool;

    private Sprite presentToolSprite;

    private Color blockOriginalColor;

    [SerializeField] private PlayerSpawnIndexManager playerSpawnIndexManager;

    [SerializeField] private Sprite selectionHighlightSprite;

    private ColorManager colorManager;

    private PortalConnectionManager portalConnectionManager;

    private MaterialPropertyBlock currentBlockMPB;
    private MaterialPropertyBlock currentStickBlockMPB;

    public static MapEditor Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        InitParameters();
        InitGrid();
        InitTab();
        InitInspector();
        InitLevelObjs();
        InitGroupSelect();
        DontDestroyOnLoad(this);
    }

    private void InitParameters()
    {
        mainCamera = Camera.main;

        presentBrush.SetActive(false);
        presentBrushRenderer = presentBrush.GetComponent<SpriteRenderer>();

        //the following requires the color manager be instantiated first
        colorManager = GameManager.Instance.ReturnColorManager();

        if (portalConnectionManager == null)
            portalConnectionManager = FindObjectOfType<PortalConnectionManager>();
    }

    //init this after loading level objs;
    private void InitGrid()
    {
        gridArr = new GridClass[width, height];
        editorActivated = false;
        for (int i = 0; i < height; i++)
        {
            for (int j = 0; j < width; j++)
            {
                gridArr[j, i] = new GridClass(new Vector3(j, i),
                    Instantiate(gridTile, gridStartPos + new Vector3(j, i), Quaternion.identity, gridObjs.transform),
                    false);
                gridArr[j, i].gridObj.GetComponent<SpriteRenderer>().color = gridOriginalColor;
            }
        }

        gridObjs.SetActive(false);
    }

    public void ActivateEditor(bool activated)
    {
        editorActivated = activated;
        gridObjs.SetActive(activated);
        presentBrush.SetActive(activated);
        selectedTool = new BagTool(ToolID.Mouse, ToolDirection.Original);
        presentClickedGrid = null;
        ShowBrush(false);
        cameraBehavior.ActivateMapEditorZoom(activated);
        editorInstructionAction?.Invoke(InstructionType.SaveSuccessResultInstruction, false, "");

        if (activated)
        {
            levelNameTMP.text = GlobalParameters.Instance.ReturnCurrentMapPreview().mapName;
            UpdateGrid();
            DetachChunk();
            SetPlayerSpawn();
            SetDialogueArea();
            SetOtherCharacterArea();
            InitEditorPause();
            ClearInspector();
            SetCurrentBlockMBP();
            UpdateObjectBar();
            portalConnectionManager.MapEditorInitPortalConnection();
        }
        else
        {
            CancelFormerGroupSelection();

            portalConnectionManager.ClearPortal();

            //hide player spawn index should be after "ClearFormerGroupSelection"
            //to prevent index showing up out of map editor
            playerSpawnIndexManager.HideAll();

            BagManager.Instance.SelectedBagItemHighlight(false);
        }
    }

    private void UpdateGrid()
    {
        for (int i = 0; i < height; i++)
        {
            for (int j = 0; j < width; j++)
            {
                gridArr[j, i].hasBlock = false;
                gridArr[j, i].hasChunk = false;
                gridArr[j, i].levelObj = null;
                gridArr[j, i].gridTool = new BagTool();
            }
        }
    }

    // Update is called once per frame
    void Update()
    {
        if (editorActivated)
        {
            CheckOnGridArea();
            MouseClickCancel();
            if (onGridArea)
            {
                CheckMouseOnGrid();
                MouseClickAndDrag();

                presentBrush.transform.position = mouseWorldPos;
                ShowBrush(selectedTool.toolID != ToolID.Mouse);
            }
            else
            {
                ShowBrush(false);
                HighLightGrid(presentHoveredIndex, false);
            }
        }
    }

    private void MouseClickCancel()
    {
        if (Input.GetMouseButtonDown(0) && !MouseOnInspector())
        {
            CancelBagSelection();
            CancelFormerGroupSelection();
            if (presentClickedGrid != null)
                HighlightSelectedObj(presentClickedGrid.levelObj, false);
        }
    }

    private void MouseClickAndDrag()
    {
        // If mouse button is pressed and dragging is not yet started, begin drag
        if (Mouse.current.leftButton.isPressed && !isDragging)
        {
            isDragging = true;
            startDragPos = mouseWorldPos;
            dragStartIndex = presentHoveredIndex;

            if (selectedTool.toolID == ToolID.Mouse)
                lineRenderer.enabled = true;
        }

        // If mouse button is released, stop dragging
        if (!Mouse.current.leftButton.isPressed && isDragging)
        {
            isDragging = false;

            if (groupSelectedGrid.Count <= 1)
            {
                if (CheckMouseClick())
                {
                    if (!groupSelectedGrid.Contains(presentClickedGrid))
                        groupSelectedGrid.Add(presentClickedGrid);
                    HighlightSelectedObj(presentClickedGrid.levelObj, true);
                }
                else
                {
                    ClearInspector();
                }
            }
            else
            {
                presentClickedGrid = null;
                UpdateGroupSelectInspector();
            }


            lineRenderer.enabled = false;
        }

        // If dragging, update position
        if (isDragging && selectedTool.toolID == ToolID.Mouse)
        {
            DrawDashedRectangle(startDragPos, mouseWorldPos);
            GroupSelectRectangle();
        }
    }

    private Vector2 mouseWorldPos;
    private bool onGridArea = false;

    private void CheckOnGridArea()
    {
        mouseWorldPos = mainCamera.ScreenToWorldPoint(Input.mousePosition);

        if (mouseWorldPos.x >= -.5f * width && mouseWorldPos.x <= .5f * width && mouseWorldPos.y >= -.5f * height &&
            mouseWorldPos.y <= .5f * height)
            onGridArea = true;
        else
        {
            onGridArea = false;
        }

        if (GameManager.Instance.ReturnPresentState() == StateEnum.MapEditorPause)
            onGridArea = false;
    }

    private void CheckMouseOnGrid()
    {
        //method 1
        DecideHoveredGridMethodOne();

        //method 2
        //DecideHoveredGridMethodTwo();
    }

    private void DecideHoveredGridMethodOne()
    {
        if (IsValidGridIndex(presentHoveredIndex))
        {
            int x = (int)presentHoveredIndex.x;
            int y = (int)presentHoveredIndex.y;
            if (!InGrid(gridArr[x, y].gridPos, mouseWorldPos))
            {
                for (int i = 0; i < height; i++)
                {
                    for (int j = 0; j < width; j++)
                    {
                        if (InGrid(gridArr[j, i].gridPos, mouseWorldPos))
                        {
                            UpdateSelected(new Vector2(j, i));
                        }
                    }
                }
            }
        }
    }

    private void DecideHoveredGridMethodTwo()
    {
        if (IsValidGridIndex(presentHoveredIndex))
        {
            int x = (int)presentHoveredIndex.x;
            int y = (int)presentHoveredIndex.y;
            if (!InGrid(gridArr[x, y].gridPos, mouseWorldPos))
            {
                Debug.Log($"centre: {gridArr[x, y].gridPos}");
                Debug.Log($"mouse world pos: {mouseWorldPos}");
                for (int i = 0; i < 3; i++)
                {
                    CheckOuterRim(gridArr[x, y].gridPos, i);
                }
            }
        }
    }

    //以当前hovered pos为中心一圈一圈的向外延伸的搜索
    private void CheckOuterRim(Vector2 centre, int n)
    {
        int rotatorArmLength = n + 1;
        int offsetLength = -n;
        int countOfEachRim = (n + 1) * 2;
        Vector2 rotatorArm = new Vector2(1, 0);
        for (int i = 0; i < 4; i++)
        {
            rotatorArm = GlobalMethod.AnticlockwiseOrthogonalRotation(rotatorArm);
            Vector2 rotateDir = GlobalMethod.AnticlockwiseOrthogonalRotation(rotatorArm);
            Vector2 beginPos = centre + (rotatorArm * rotatorArmLength) + offsetLength * rotateDir;
            for (int j = 0; j < countOfEachRim; j++)
            {
                Vector2 gridPos = beginPos + rotateDir * j;
                Debug.Log($"check grid pos: {gridPos}");
                if (InGrid(gridPos, mouseWorldPos))
                {
                    Debug.Log($"new grid pos: {gridPos}");
                    UpdateSelected(GridClass.ConvertPositionToIndex(gridPos));
                }
            }
        }
    }

    private bool InGrid(Vector2 gridPos, Vector2 checkPos)
    {
        float range = 0.5f;
        float xMin = gridPos.x - range;
        float xMax = gridPos.x + range;
        float yMin = gridPos.y - range;
        float yMax = gridPos.y + range;
        bool inHorizontal = checkPos.x >= xMin && checkPos.x < xMax;
        bool inVertical = checkPos.y >= yMin && checkPos.y < yMax;
        return inHorizontal && inVertical;
        //return Vector2.Distance(gridPos, checkPos) < 0.49f;
    }

    private bool IsValidGridIndex(Vector2 index)
    {
        return index.x >= 0 && index.x < width && index.y >= 0 && index.y < height;
    }

    private void UpdateSelected(Vector2 newIndex)
    {
        HighLightGrid(presentHoveredIndex, false);
        presentHoveredIndex = newIndex;
        HighLightGrid(presentHoveredIndex, true);
    }

    private void ShowBrush(bool show)
    {
        presentBrushRenderer.color = show ? Color.white : Color.clear;
    }


    #region Create Object and Chunk

    private bool CheckMouseClick()
    {
        int x = (int)presentHoveredIndex.x;
        int y = (int)presentHoveredIndex.y;

        if (selectedTool.toolID == ToolID.Mouse && !gridArr[x, y].hasBlock)
            return false;

        CreatePropToIndex(selectedTool, presentHoveredIndex);

        UpdateInspector(x, y);

        presentClickedGrid = gridArr[x, y];
        return true;
    }

    private void CreatePropToIndex(BagTool tool, Vector2 index)
    {
        int x = Mathf.RoundToInt(index.x);
        int y = Mathf.RoundToInt(index.y);
        ToolType toolType = ToolDataManager.Instance.ReturnToolType(tool.toolID);
        switch (toolType)
        {
            case ToolType.Player:
                //create player spawn pos
                CreatePlayer(x, y);
                break;
            case ToolType.Character:
                CreateOtherCharacter(x, y, tool);
                break;
            case ToolType.Symbol:
                CreateTool(x, y, tool);
                break;
            case ToolType.Dialogue:
                //create dialogue area
                CreateDialogueArea(x, y);
                break;
        }
    }

    private ChunkClass tempChunk;
    private SpriteRenderer tempSR;

    private void CreateTool(int x, int y, BagTool tool)
    {
        //if there is tool in the grid, then delete it first
        if (gridArr[x, y].hasBlock)
        {
            if (gridArr[x, y].gridTool.toolID == tool.toolID)
                return;

            DeleteGridTool(x, y);
        }


        GameObject block = GlobalParameters.Instance.stickablePool.GetObj();

        block.transform.position = gridArr[x, y].gridPos;
        tempSR = block.GetComponent<SpriteRenderer>();
        tempSR.sprite = ToolDataManager.Instance.ReturnToolSprite(ToolID.Block);
        tempSR.color = blockOriginalColor;
        if (tool.toolID != ToolID.Block)
        {
            GlobalMethod.ShowTool(block, tool);
            tempSR.SetPropertyBlock(currentStickBlockMPB);
        }
        else
        {
            GlobalMethod.HideTool(block);
            tempSR.SetPropertyBlock(currentBlockMPB);
        }

        if (tool.toolID == ToolID.Portal)
            portalConnectionManager.MapEditorAssignPortal(block);

        gridArr[x, y].levelObj = block;
        gridArr[x, y].gridTool = tool;
        gridArr[x, y].hasBlock = true;
    }

    private void CreatePlayer(int x, int y)
    {
        if (GlobalParameters.Instance.playerSpawnObjPool.ReturnActiveCount() >= 8)
        {
            editorInstructionAction?.Invoke(InstructionType.MoreThanEightPlayerInstruction, true, emptyString);
            return;
        }

        if (gridArr[x, y].hasBlock)
        {
            if (gridArr[x, y].gridTool.toolID == ToolID.Player)
                return;

            DeleteGridTool(x, y);
        }

        GameObject player = GlobalParameters.Instance.playerSpawnObjPool.GetObj();


        player.transform.position = gridArr[x, y].gridPos;
        gridArr[x, y].hasBlock = true;
        gridArr[x, y].gridTool = new BagTool(ToolID.Player, ToolDirection.Original);
        gridArr[x, y].levelObj = player;

        //show UI index on the player spawn pos
        playerSpawnIndexManager.PutPlayerSpawnIndex(player.transform);
    }

    private void CreateDialogueArea(int x, int y)
    {
        if (gridArr[x, y].hasBlock)
        {
            if (gridArr[x, y].gridTool.toolID == ToolID.Info)
                return;

            DeleteGridTool(x, y);
        }

        GameObject dialogueArea = GlobalParameters.Instance.dialogAreaPool.GetObj();

        dialogueArea.transform.position = gridArr[x, y].gridPos;

        gridArr[x, y].hasBlock = true;
        gridArr[x, y].gridTool = new BagTool(ToolID.Info, ToolDirection.Original);
        gridArr[x, y].levelObj = dialogueArea;
    }

    private void CreateOtherCharacter(int x, int y, BagTool tool)
    {
        if (tool.toolID == ToolID.Switch)
        {
            if (ContainSwitch())
            {
                editorInstructionAction?.Invoke(InstructionType.MoreThanOneSwitchInstruction, true, emptyString);
                return;
            }
        }

        if (gridArr[x, y].hasBlock)
        {
            if (gridArr[x, y].gridTool.toolID == tool.toolID)
                return;

            DeleteGridTool(x, y);
        }


        GameObject block = GlobalParameters.Instance.otherCharacterPool.GetObj();

        block.transform.position = gridArr[x, y].gridPos;
        block.GetComponent<SpriteRenderer>().sprite = ToolDataManager.Instance.ReturnToolSprite(tool.toolID);
        block.GetComponent<CharacterInfo>().GameStartCreateCharacter(tool);
        gridArr[x, y].levelObj = block;
        gridArr[x, y].gridTool = tool;
        gridArr[x, y].hasBlock = true;
    }

    private bool ContainSwitch()
    {
        for (int i = 0; i < height; i++)
        {
            for (int j = 0; j < width; j++)
            {
                if (gridArr[j, i].gridTool.toolID == ToolID.Switch)
                    return true;
            }
        }

        return false;
    }

    private void SetCurrentBlockMBP()
    {
        currentBlockMPB = new MaterialPropertyBlock();
        currentStickBlockMPB = new MaterialPropertyBlock();
        BlockShaderParam currentBSP = GlobalParameters.Instance.currentBlockShaderParam;
        currentBlockMPB.SetFloat("_Hastool", 1);
        currentBlockMPB.SetFloat("_Density", currentBSP.density);
        currentBlockMPB.SetFloat("_Size", currentBSP.size);
        currentBlockMPB.SetFloat("_Offset", 0.5f * currentBSP.maxOffset);
        currentBlockMPB.SetFloat("_Speed", 0.5f * currentBSP.maxSpeed);

        currentStickBlockMPB.SetFloat("_Hastool", 20);

        blockOriginalColor = colorManager.ReturnColorByType(currentBSP.colorType);
    }

    #endregion

    #region Object Bar

    [Serializable]
    private class TabClass
    {
        public GameObject tabObj;
        public ToolType toolType;
        [HideInInspector] public List<BagTool> tabObjList;
        [HideInInspector] public Button tabButton;
        [HideInInspector] public TMP_Text tabText;
    }

    [SerializeField] private List<TabClass> tabList;

    private int currentSelectedTab = -1;

    private List<BagTool> objectList;

    private bool barHasInitialized = false;

    private void InitTab()
    {
        int index = 0;
        foreach (TabClass tabClass in tabList)
        {
            tabClass.tabButton = tabClass.tabObj.GetComponent<Button>();
            if (tabClass.toolType == ToolType.Character)
            {
                tabClass.tabObjList = ToolDataManager.Instance.ReturnMapEditorCharacterTabToolList();
            }
            else if (tabClass.toolType == ToolType.Symbol)
            {
                tabClass.tabObjList = ToolDataManager.Instance.ReturnMapEditorSymbolTabToolList();
            }

            AssignTabButtonAction(index);
            tabClass.tabText = tabClass.tabObj.GetComponent<TMP_Text>();
            index++;
        }

        currentSelectedTab = -1;
        SelectTab(0);
    }

    private void AssignTabButtonAction(int index)
    {
        tabList[index].tabButton.onClick.AddListener(() => SelectTab(index));
    }

    private void SelectTab(int index)
    {
        if (currentSelectedTab != index)
        {
            UpdateTabAppearance(currentSelectedTab, false);
            UpdateTabAppearance(index, true);
            if (index >= 0 && index < tabList.Count)
            {
                objectList = tabList[index].tabObjList;
                UpdateObjectBar();
                currentSelectedTab = index;
            }
        }
    }

    private void UpdateTabAppearance(int index, bool highlight)
    {
        if (index >= 0 && index < tabList.Count)
        {
            tabList[index].tabText.color = highlight ? gridHighlightColor : Color.white;
        }
    }

    private void UpdateObjectBar()
    {
        if (!barHasInitialized)
        {
            objectBarContent = objectBarObj.transform.Find("ViewPort").Find("Content").gameObject;
            objectBarContentRT = objectBarContent.GetComponent<RectTransform>();
            objectBarContentPage = objectBarContent.transform.Find("Page").gameObject;
            pageCapacity = objectBarContentPage.transform.childCount;
            pageList.Clear();
            pageList.Add(objectBarContentPage);
            barHasInitialized = true;
        }

        pageIndex = 0;
        pageCount = objectList.Count / pageCapacity + 1;

        //create new page if needed
        //hide page if not
        if (pageCount >= pageList.Count)
        {
            for (int i = 0; i < pageCount; i++)
            {
                if (i >= pageList.Count)
                {
                    pageList.Add(Instantiate(objectBarContentPage, objectBarContent.transform));
                }

                pageList[i].gameObject.SetActive(true);
            }
        }
        else
        {
            for (int i = pageCount; i < pageList.Count; i++)
            {
                pageList[i].gameObject.SetActive(false);
            }
        }

        InitPagePosition();
        SetObjectButton();
        HideNotUsedButton();
    }

    private void SetObjectButton()
    {
        int index = 0;
        foreach (BagTool tool in objectList)
        {
            int toolPageIndex = index / pageCapacity;
            int toolIndex = index % pageCapacity;

            GameObject btnObj = pageList[toolPageIndex].transform.GetChild(toolIndex).gameObject;
            //this code require the "SpriteManager" be loaded first
            btnObj.GetComponent<Image>().sprite = ToolDataManager.Instance.ReturnToolSprite(tool.toolID);
            btnObj.GetComponent<Image>().color = tool.toolID == ToolID.Block ? blockOriginalColor : Color.white;


            btnObj.GetComponent<Button>().onClick.RemoveAllListeners();
            btnObj.GetComponent<Button>().onClick
                .AddListener(() => PickUIObjButtonAction(tool));

            index++;
        }
    }

    private void PickUIObjButtonAction(BagTool tool)
    {
        selectedTool = tool;
        //change brush
        presentToolSprite = ToolDataManager.Instance.ReturnToolSprite(tool.toolID);
        presentBrushRenderer.sprite = presentToolSprite;
    }

    private void HideNotUsedButton()
    {
        //hide not used button in last page
        int lastUsedButtonIndex = objectList.Count % pageCapacity;
        for (int i = lastUsedButtonIndex; i < pageCapacity; i++)
        {
            pageList[pageCount - 1].transform.GetChild(i).GetComponent<Image>().color = Color.clear;
            pageList[pageCount - 1].transform.GetChild(i).GetComponent<Button>().onClick.RemoveAllListeners();
        }
    }

    private void InitPagePosition()
    {
        objectBarContentRT.anchoredPosition = new Vector2(0, 0);
    }

    public void LastPageButtonAction()
    {
        if (pageIndex > 0)
        {
            UpdatePage(-1);
        }
    }

    public void NextPageButtonAction()
    {
        if (pageIndex < pageCount - 1)
        {
            UpdatePage(1);
        }
    }

    private void UpdatePage(int change)
    {
        Vector2 presentCentrePos = pageList[pageIndex].GetComponent<RectTransform>().anchoredPosition;
        pageIndex += change;
        Vector2 originalPos = pageList[pageIndex].GetComponent<RectTransform>().anchoredPosition;
        float movement = (presentCentrePos - originalPos).x;

        if (!movePageLock)
            StartCoroutine(MovePage(movement, change));
    }

    private IEnumerator movePage;
    private bool movePageLock = false;

    private float moveSpeed = 50;

    //private WaitForFixedUpdate pageMoveDur = new WaitForFixedUpdate();
    private WaitForSecondsRealtime pageMoveDur = new WaitForSecondsRealtime(0.02f);

    private IEnumerator MovePage(float xmovement, int dir)
    {
        movePageLock = true;
        xmovement *= dir;
        while (xmovement < 0)
        {
            xmovement += moveSpeed;
            objectBarContentRT.anchoredPosition += new Vector2(-dir * moveSpeed, 0);
            yield return pageMoveDur;
        }

        movePageLock = false;
    }

    #endregion

    #region Inpector

    [SerializeField] private GameObject inspectorBar;
    private RectTransform inspectorRT;
    private TMP_Text objectName;
    private TMP_Text objectDescription;
    private GameObject directionUI;
    private Image objectImage;
    private GameObject deleteButtonUI;
    private string emptyString = "";

    private void InitInspector()
    {
        objectName = inspectorBar.transform.Find("Name").GetComponent<TMP_Text>();
        objectDescription = inspectorBar.transform.Find("Description").GetComponent<TMP_Text>();
        directionUI = inspectorBar.transform.Find("Direction").gameObject;
        objectImage = directionUI.transform.Find("ShowDirection").GetComponent<Image>();
        deleteButtonUI = inspectorBar.transform.Find("DeleteButton").gameObject;

        inspectorRT = inspectorBar.GetComponent<RectTransform>();
    }

    private void ClearInspector()
    {
        objectName.text = emptyString;
        objectDescription.text = emptyString;
        directionUI.SetActive(false);
        deleteButtonUI.SetActive(false);
    }

    private void UpdateInspector(int x, int y)
    {
        if (gridArr[x, y].hasBlock)
        {
            directionUI.SetActive(true);
            deleteButtonUI.SetActive(true);

            objectName.text = ToolDataManager.Instance.ReturnToolName(gridArr[x, y].gridTool.toolID);
            objectDescription.text = ToolDataManager.Instance.ReturnToolDescription(gridArr[x, y].gridTool.toolID);
            objectImage.sprite = ToolDataManager.Instance.ReturnToolSprite(gridArr[x, y].gridTool.toolID);
            if (gridArr[x, y].gridTool.toolID == ToolID.Block)
                objectImage.color = blockOriginalColor;
            else
            {
                objectImage.color = Color.white;
            }

            GlobalMethod.OperateUIDirection(objectImage.gameObject, (int)gridArr[x, y].gridTool.toolDirection);
        }
        else
        {
            ClearInspector();
        }
    }

    private void UpdateInspector(BagTool tool)
    {
        if (tool.toolID != ToolID.Block)
        {
            directionUI.SetActive(true);
            deleteButtonUI.SetActive(true);

            objectName.text = ToolDataManager.Instance.ReturnToolName(tool.toolID);
            objectDescription.text = ToolDataManager.Instance.ReturnToolDescription(tool.toolID);
            objectImage.sprite = ToolDataManager.Instance.ReturnToolSprite(tool.toolID);
            if (tool.toolID == ToolID.Block)
                objectImage.color = blockOriginalColor;
            GlobalMethod.OperateUIDirection(objectImage.gameObject, (int)tool.toolDirection);
        }
        else
        {
            objectName.text = emptyString;
            objectDescription.text = emptyString;
            ClearInspector();
        }
    }

    private void UpdateGroupSelectInspector()
    {
        ClearInspector();
        deleteButtonUI.SetActive(true);
        objectName.text = $"Selected {groupSelectedGrid.Count} items";
    }

    private bool MouseOnInspector()
    {
        return RectTransformUtility.RectangleContainsScreenPoint(inspectorRT, Input.mousePosition, mainCamera);
    }

    public void DeleteButtonAction()
    {
        if (bagItem)
        {
            DeleteBagTool();
        }
        else
        {
            if (groupSelectedGrid.Count <= 1)
            {
                DeleteGridTool(presentClickedGrid);
            }
            else
            {
                DeleteGroupSelectedGrids();
            }
        }
    }

    private void DeleteGridTool(int x, int y)
    {
        if (gridArr[x, y].hasBlock)
        {
            if (gridArr[x, y].gridTool.toolID == ToolID.Player)
                DeletePlayerSpawnIndex(gridArr[x, y]);
            if (gridArr[x, y].gridTool.toolID == ToolID.Portal)
                portalConnectionManager.MapEditorDeletePortal(gridArr[x, y].levelObj);
            gridArr[x, y].hasBlock = false;
            gridArr[x, y].gridTool = new BagTool();
            gridArr[x, y].levelObj.SetActive(false);
            gridArr[x, y].levelObj = null;
        }

        UpdateInspector(gridArr[x, y].gridTool);
    }

    private void DeleteGridTool(GridClass grid)
    {
        if (grid != null)
        {
            if (grid.hasBlock)
            {
                HighlightSelectedObj(grid.levelObj, false);
                if (grid.gridTool.toolID == ToolID.Player)
                    DeletePlayerSpawnIndex(grid);
                if (grid.gridTool.toolID == ToolID.Portal)
                    portalConnectionManager.MapEditorDeletePortal(grid.levelObj);
                grid.hasBlock = false;
                grid.gridTool = new BagTool();
                grid.levelObj.SetActive(false);
                grid.levelObj = null;
            }

            UpdateInspector(grid.gridTool);
        }
    }


    private void DeletePlayerSpawnIndex(GridClass grid)
    {
        GlobalParameters.Instance.playerSpawnObjPool.ReturnObj(grid.levelObj);
        playerSpawnIndexManager.UpdatePlayerSpawnIndex();
    }

    private void DeleteBagTool()
    {
        BagManager.Instance.DeleteSelectedTool(clickedBagIndex);
        UpdateInspector(BagManager.Instance.ReturnToolByIndex(clickedBagIndex));
    }

    public void NextDirectionButtonAction()
    {
        if (bagItem)
        {
            ChangeBagItemDirection(1);
        }
        else
        {
            ChangeDirection(1);
        }
    }

    public void LastDirectionButtonAction()
    {
        if (bagItem)
        {
            ChangeBagItemDirection(-1);
        }
        else
        {
            ChangeDirection(-1);
        }
    }

    private void ChangeDirection(int changeAmount)
    {
        // int x = (int)presentClickedIndex.x;
        // int y = (int)presentClickedIndex.y;
        int presentDirection = (int)presentClickedGrid.gridTool.toolDirection;
        if (presentClickedGrid.hasBlock)
        {
            switch (presentClickedGrid.gridTool.toolID)
            {
                case ToolID.Rotate:
                    presentDirection =
                        (presentDirection == 0) ? 4 : 0;
                    break;
                case ToolID.Player:
                    break;
                case ToolID.Info:
                    break;
                default:
                    presentDirection += changeAmount;
                    if (presentDirection > 3)
                        presentDirection = 0;
                    else if (presentDirection < 0)
                        presentDirection = 3;
                    break;
            }

            presentClickedGrid.gridTool.toolDirection = (ToolDirection)presentDirection;

            //update inspector image
            GlobalMethod.OperateUIDirection(objectImage.gameObject, presentDirection);
            //update level obj
            if (presentClickedGrid.levelObj.transform.childCount > 0)
                GlobalMethod.OperateUIDirection(presentClickedGrid.levelObj.transform.Find("tool").gameObject,
                    presentDirection);
        }
    }

    private void ChangeBagItemDirection(int changeAmount)
    {
        BagTool tool = BagManager.Instance.ReturnToolByIndex(clickedBagIndex);
        int presentDirection = (int)tool.toolDirection;
        //if there is no tool there, it will return block tool
        if (tool.toolID != ToolID.Block)
        {
            switch (tool.toolID)
            {
                case ToolID.Rotate:
                    presentDirection =
                        (presentDirection == 0) ? 4 : 0;
                    break;
                default:
                    presentDirection += changeAmount;
                    if (presentDirection > 3)
                        presentDirection = 0;
                    else if (presentDirection < 0)
                        presentDirection = 3;
                    break;
            }

            tool.toolDirection = (ToolDirection)presentDirection;

            GlobalMethod.OperateUIDirection(objectImage.gameObject, presentDirection);
            BagManager.Instance.SetTool(clickedBagIndex, tool);
            BagManager.Instance.UpdateSprite(tool, clickedBagIndex);
        }
    }

    #endregion

    #region Load & Save

    [SerializeField] private GameObject levelObjs;

    private GameObject stickableObjs;

    private GameObject levelDialogueObjs;

    private GameObject playerSpawnObjs;

    private GameObject otherCharacterObjs;

    private void InitLevelObjs()
    {
        stickableObjs = levelObjs.transform.Find("StickableObjs").gameObject;
        playerSpawnObjs = levelObjs.transform.Find("PlayerSpawnObjs").gameObject;
        levelDialogueObjs = levelObjs.transform.Find("LevelDialogueObjs").gameObject;
        otherCharacterObjs = levelObjs.transform.Find("OtherCharacterObjs").gameObject;
    }

    private ChunkClass tempChunkClass;

    //detach all chunk when entered in editor
    private void DetachChunk()
    {
        foreach (Transform chunk in stickableObjs.transform)
        {
            //when detach, we don't care if the chunk is active or not
            if (chunk.CompareTag("Chunk"))
            {
                tempChunkClass = chunk.GetComponent<ChunkClass>();
                foreach (var stickable in tempChunkClass.chunkChildList)
                {
                    stickable.stickableObj.transform.SetParent(stickableObjs.transform);
                    SetGridInfo(stickable.stickableObj, new BagTool(stickable.toolID, stickable.toolDir));
                }

                tempChunkClass.chunkChildList.Clear();
            }
        }

        GlobalParameters.Instance.chunkPool.ReturnAll();
    }

    private void SetPlayerSpawn()
    {
        foreach (GameObject o in GlobalParameters.Instance.playerSpawnObjPool.activePool)
        {
            SetGridInfo(o, new BagTool(ToolID.Player, ToolDirection.Original));
        }
        
        playerSpawnIndexManager.UpdatePlayerSpawnIndex();
    }

    private void SetDialogueArea()
    {
        foreach (Transform area in levelDialogueObjs.transform)
        {
            if (area.gameObject.activeSelf)
                SetGridInfo(area.gameObject, new BagTool(ToolID.Info, ToolDirection.Original));
        }
    }

    private void SetOtherCharacterArea()
    {
        foreach (Transform otherCharacter in otherCharacterObjs.transform)
        {
            if (otherCharacter.gameObject.activeSelf)
                SetGridInfo(otherCharacter.gameObject,
                    otherCharacter.GetComponent<CharacterInfo>().ReturnCharacterInfo());
        }
    }

    private void SetGridInfo(GameObject obj, BagTool tool)
    {
        Vector2 objPos = obj.transform.position;
        Vector2 index = GridClass.ConvertPositionToIndex(objPos);
        int x = Mathf.RoundToInt(index.x);
        int y = Mathf.RoundToInt(index.y);

        gridArr[x, y].hasBlock = true;
        gridArr[x, y].levelObj = obj;
        gridArr[x, y].gridTool = tool;
        gridArr[x, y].hasChunk = false;
    }

    private Action<InstructionType, bool, string> editorInstructionAction;

    public void SubscribeEditorInstructionAction(Action<InstructionType, bool, string> cAction)
    {
        editorInstructionAction = cAction;
    }

    public void SaveMapTemplate()
    {
        MapPreview currentMapPreview = GlobalParameters.Instance.ReturnCurrentMapPreview();
        int currentPlayerSpawnCount = GlobalParameters.Instance.playerSpawnObjPool.ReturnActiveCount();
        if (currentMapPreview.playerCountMax > currentPlayerSpawnCount)
        {
            editorInstructionAction?.Invoke(InstructionType.PlayerCountMaxNotReached, true,
                currentMapPreview.playerCountMax.ToString());
            return;
        }

        try
        {
            //create chunk
            AttachChunk();

            //get present map info
            GlobalParameters.Instance.GetInfo();

            DetachChunk();

            editorInstructionAction?.Invoke(InstructionType.SaveSuccessResultInstruction, true, emptyString);
        }
        catch (Exception e)
        {
            editorInstructionAction?.Invoke(InstructionType.SaveFailResultInstruction, true, emptyString);
            throw;
        }
    }

    //create chunk by beside rule
    private void AttachChunk()
    {
        for (int i = 0; i < height; i++)
        {
            for (int j = 0; j < width; j++)
            {
                if (gridArr[j, i].hasBlock && !gridArr[j, i].hasChunk && gridArr[j, i].levelObj.CompareTag("Stickable"))
                {
                    ChunkClass chunk = GlobalParameters.Instance.chunkPool.GetObj();
                    DetectSide(new Vector2(j, i), chunk.gameObject);
                }
            }
        }
    }

    private void DetectSide(Vector2 index, GameObject chunk)
    {
        //deal with current grid
        GridClass thisGrid = gridArr[(int)index.x, (int)index.y];
        gridArr[(int)index.x, (int)index.y].levelObj.transform.SetParent(chunk.transform);
        chunk.GetComponent<ChunkClass>().chunkChildList.Add(new ChunkClass.StickableClass((int)thisGrid.gridTool.toolID,
            (int)thisGrid.gridTool.toolDirection, gridArr[(int)index.x, (int)index.y].levelObj));
        gridArr[(int)index.x, (int)index.y].hasChunk = true;

        //deal with grid around
        Vector2 startPos = new Vector2(1, 0);

        for (int i = 0; i < 4; i++)
        {
            if (i > 0)
                startPos = GlobalMethod.AnticlockwiseOrthogonalRotation(startPos);

            Vector2 besideIndex = index + startPos;
            if (besideIndex.x >= 0 && besideIndex.x < width && besideIndex.y >= 0 && besideIndex.y < height)
            {
                if (gridArr[(int)besideIndex.x, (int)besideIndex.y].hasBlock &&
                    !gridArr[(int)besideIndex.x, (int)besideIndex.y].hasChunk &&
                    gridArr[(int)besideIndex.x, (int)besideIndex.y].levelObj.CompareTag("Stickable"))
                {
                    DetectSide(besideIndex, chunk);
                }
            }
        }
    }

    #endregion

    #region Bag

    private int clickedBagIndex = -1;
    private bool bagItem = false;

    public void ClickedBag(int index)
    {
        clickedBagIndex = index;
        bagItem = true;
        if (presentClickedGrid != null)
            HighlightSelectedObj(presentClickedGrid.levelObj, false);

        if (ToolDataManager.Instance.ReturnToolType(selectedTool.toolID) == ToolType.Symbol &&
            selectedTool.toolID != ToolID.Block)
        {
            if (BagManager.Instance.GotItem(index))
            {
                BagManager.Instance.SetTool(index, selectedTool);
            }
            else
            {
                BagManager.Instance.AddTool(selectedTool);
            }
        }

        UpdateInspector(BagManager.Instance.ReturnToolByIndex(index));
    }

    private void CancelBagSelection()
    {
        bagItem = false;
        BagManager.Instance.SelectedBagItemHighlight(false, clickedBagIndex);
    }

    #endregion

    #region Group action

    private LineRenderer lineRenderer;
    private Vector2 startDragPos; // Starting position of the drag
    private bool isDragging = false; // Dragging status
    private Vector2 groupSelectStartPos;
    private List<GridClass> groupSelectedGrid = new List<GridClass>();
    private Vector3[] rectangleCorner = new Vector3[5];

    private void InitGroupSelect()
    {
        //line renderer
        if (lineRenderer == null)
            lineRenderer = GetComponent<LineRenderer>();
        lineRenderer.material.color = new Color(1f, 0f, 0f); // Red
        lineRenderer.positionCount = 5;
        lineRenderer.startWidth = 0.05f;
        lineRenderer.endWidth = 0.05f;

        //other settings
        groupSelectedGrid.Clear();
    }

    private void DrawDashedRectangle(Vector2 startPos, Vector2 currentPos)
    {
        rectangleCorner[0] = startPos;
        rectangleCorner[1] = new Vector3(startPos.x, currentPos.y, 0);
        rectangleCorner[2] = currentPos;
        rectangleCorner[3] = new Vector3(currentPos.x, startPos.y, 0);
        rectangleCorner[4] = startPos;

        lineRenderer.SetPositions(rectangleCorner);
    }

    private void GroupSelectRectangle()
    {
        int xIndexMin = Mathf.Min((int)dragStartIndex.x, (int)presentHoveredIndex.x);
        int xIndexMax = Mathf.Max((int)dragStartIndex.x, (int)presentHoveredIndex.x);
        int yIndexMin = Mathf.Min((int)dragStartIndex.y, (int)presentHoveredIndex.y);
        int yIndexMax = Mathf.Max((int)dragStartIndex.y, (int)presentHoveredIndex.y);

        for (int i = yIndexMin; i <= yIndexMax; i++)
        {
            for (int j = xIndexMin; j <= xIndexMax; j++)
            {
                GridClass grid = gridArr[j, i];
                if (grid.hasBlock)
                {
                    if (!groupSelectedGrid.Contains(grid))
                    {
                        groupSelectedGrid.Add(grid);
                        HighlightSelectedObj(grid.levelObj, true);
                    }
                }
            }
        }

        //remove if the item is not in current rectangle
        for (int i = 0; i < groupSelectedGrid.Count; i++)
        {
            Vector2 index = GridClass.ConvertPositionToIndex(groupSelectedGrid[i].gridPos);
            if (index.x > xIndexMax || index.x < xIndexMin || index.y > yIndexMax || index.y < yIndexMin)
            {
                HighlightSelectedObj(groupSelectedGrid[i].levelObj, false);
                groupSelectedGrid.RemoveAt(i);
            }
        }
    }

    public void MoveSelectedTools(Vector3 move)
    {
        if (isDragging)
            return;

        bool valid = true;
        //check if new grids are valid
        foreach (GridClass gridClass in groupSelectedGrid)
        {
            Vector3 newPos = gridClass.levelObj.transform.position + move;
            if (newPos.x < horizontalBound[0] || newPos.x > horizontalBound[1] || newPos.y < verticalBound[0] ||
                newPos.y > verticalBound[1])
                valid = false;
        }

        if (!valid)
            return;

        foreach (GridClass grid in groupSelectedGrid)
        {
            grid.levelObj.transform.position += move;
            if (grid.gridTool.toolID == ToolID.Player)
                playerSpawnIndexManager.UpdatePlayerSpawnIndexPos();
        }
    }

    private void DeleteGroupSelectedGrids()
    {
        //delete all selected tools
        foreach (GridClass gridClass in groupSelectedGrid)
        {
            DeleteGridTool(gridClass);
        }
    }

    private struct MoveToolStruct
    {
        public MoveToolStruct(BagTool t, Vector2 i)
        {
            tool = t;
            index = i;
        }

        public BagTool tool;
        public Vector2 index;
    }

    private List<MoveToolStruct> moveList = new List<MoveToolStruct>();

    private void CancelFormerGroupSelection()
    {
        moveList.Clear();
        foreach (GridClass grid in groupSelectedGrid)
        {
            //check if moved
            if (grid.levelObj != null)
            {
                if (grid.gridPos != grid.levelObj.transform.position)
                {
                    Vector2 oldIndex = GridClass.ConvertPositionToIndex(grid.gridPos);
                    Vector2 newIndex = GridClass.ConvertPositionToIndex(grid.levelObj.transform.position);
                    HighlightSelectedObj(grid.levelObj, false);

                    //copy the grid tool
                    //delete the tool
                    BagTool gridTool = grid.gridTool;
                    DeleteGridTool(Mathf.RoundToInt(oldIndex.x), Mathf.RoundToInt(oldIndex.y));
                    moveList.Add(new MoveToolStruct(gridTool, newIndex));
                }
                else
                {
                    HighlightSelectedObj(grid.levelObj, false);
                }
            }
        }

        if (moveList.Count > 0)
        {
            foreach (var s in moveList)
            {
                CreatePropToIndex(s.tool, s.index);
            }
        }

        groupSelectedGrid.Clear();
    }

    #endregion

    #region Editor Pause Warning

    [SerializeField] private GameObject editorPauseUI;
    private TMP_Text editorPauseWarning;
    private int editorPauseWarningType; //exit: 0, delete: 1

    private void InitEditorPause()
    {
        if (editorPauseWarning == null)
            editorPauseWarning = editorPauseUI.transform.Find("Warning").GetComponent<TMP_Text>();
    }

    public void ExitEditorButtonAction()
    {
        //exit is type 0
        editorPauseWarningType = 0;
        GameManager.Instance.StateButtonAction((int)StateEnum.MapEditorPause);
        editorPauseWarning.text = "Do you want to exit ?";
    }

    public void EditorPauseConfirmButtonAction()
    {
        if (editorPauseWarningType == 0)
        {
            GameManager.Instance.StateButtonAction((int)StateEnum.ChooseEditorMap);
        }
    }

    public void EditorPauseBackButtonAction()
    {
        GameManager.Instance.StateButtonAction((int)StateEnum.MapEditor);
    }

    #endregion

    #region Highlight

    private void HighLightGrid(Vector2 index, bool highlight)
    {
        if (gridArr[(int)index.x, (int)index.y].gridObjSR.color != gridSelectedHighlightColor)
        {
            gridArr[(int)index.x, (int)index.y].gridObjSR.color =
                highlight ? gridHighlightColor : gridOriginalColor;

            if (highlight)
            {
                ShowBrush(!gridArr[(int)index.x, (int)index.y].hasBlock);
            }
        }
    }

    private static string showSelectedHighlightName = "Highlight";
    private GameObject tempSelectHighlightObj;

    private void HighlightSelectedObj(GameObject obj, bool highlight)
    {
        if (obj == null)
            return;

        Transform t = obj.transform.Find(showSelectedHighlightName);
        SpriteRenderer sr;
        //if has a child called tool
        if (t == null)
        {
            //create a new one
            tempSelectHighlightObj = new GameObject(showSelectedHighlightName);
            sr = tempSelectHighlightObj.AddComponent<SpriteRenderer>();
            sr.sprite = selectionHighlightSprite;
            tempSelectHighlightObj.layer = LayerMask.NameToLayer("StickableObj");
            tempSelectHighlightObj.transform.position = obj.transform.position;
            tempSelectHighlightObj.transform.SetParent(obj.transform);
        }
        else
        {
            sr = t.GetComponent<SpriteRenderer>();
        }

        sr.color = highlight ? Color.red : Color.clear;
    }

    #endregion

    #region API

    public BagTool MapEditorReturnBagToolByObj(GameObject obj)
    {
        Vector2 index = GridClass.ConvertPositionToIndex(obj.transform.position);
        int x = Mathf.RoundToInt(index.x);
        int y = Mathf.RoundToInt(index.y);

        return gridArr[x, y].gridTool;
    }

    #endregion
}