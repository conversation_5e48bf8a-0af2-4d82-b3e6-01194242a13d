using System.IO;
using System.Threading.Tasks;
using UnityEngine;

//can update properties of map and collection in mass
public class MapUpdater : MonoBehaviour
{
    
    private LevelPreviewList builtinMapListTemplate;
    private ChapterPreviewList builtinCollectionListTemplate; 

    public async Task RetrieveAllBuiltinMaps()
    {
        builtinMapListTemplate = Resources.Load(Path.Combine("MapData", "MapList")) as LevelPreviewList;
        builtinCollectionListTemplate = Resources.Load(Path.Combine("MapData", "CollectionList")) as ChapterPreviewList;
    }

    public async Task SetAllMapsMaxCount()
    {
        foreach (MapPreview preview in builtinMapListTemplate.previewList)
        {
            preview.playerCountMax = preview.playerCount;
        }

        foreach (CollectionClass collection in builtinCollectionListTemplate.previewList)
        {
            collection.playerCountMax = collection.playerCount;
        }
    }
    
    
}
