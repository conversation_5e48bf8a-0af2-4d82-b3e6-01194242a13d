using System.IO;
using System.Linq;
using System.Runtime.Serialization.Formatters.Binary;
using TMPro;
using UnityEngine;

public static class GlobalMethod
{
    public static void OperateUIDirection(GameObject obj, int toolDir)
    {
        if (toolDir <= 3)
        {
            obj.transform.rotation = Quaternion.AngleAxis(90 * toolDir, Vector3.forward);
        }
        else
        {
            obj.transform.rotation = Quaternion.AngleAxis(180, Vector3.up);
        }
    }
    
    private static GameObject tempObj;
    private static string showToolObjName = "tool";
    public static void ShowTool(GameObject obj, BagTool tool)
    {
        Transform t = obj.transform.Find(showToolObjName);
        
        //if has a child called tool
        if (t == null)
        {
            //create a new one
            tempObj = new GameObject(showToolObjName);
            tempObj.AddComponent<SpriteRenderer>();
            tempObj.layer = LayerMask.NameToLayer("StickableObj");
            tempObj.transform.position = obj.transform.position;
            tempObj.transform.SetParent(obj.transform);
        }
        else
        {
            tempObj = t.gameObject;
        }
        
        tempObj.GetComponent<SpriteRenderer>().sortingOrder = obj.GetComponent<SpriteRenderer>().sortingOrder + 1;
        tempObj.GetComponent<SpriteRenderer>().sprite = ToolDataManager.Instance.ReturnToolSprite(tool.toolID);
        OperateUIDirection(tempObj, (int)tool.toolDirection);
    }
    
    public static void HideTool(GameObject obj)
    {
        Transform t = obj.transform.Find(showToolObjName);
        if (t != null)
        {
            t.GetComponent<SpriteRenderer>().sprite = null;
        }
    }
    

    public static Vector3 ReturnVectorDirByDirectionAndTool(ToolDirection dir, ToolID tool)
    {
        Vector3 indicateDir = new Vector3();
        if (tool == ToolID.Rotate)
        {
            switch (dir)
            {
                case ToolDirection.Flip:
                    indicateDir = Vector3.back;
                    break;
                default:
                    indicateDir = Vector3.forward;
                    break;
            }
        }else if (tool == ToolID.Portal || tool == ToolID.Flip || tool == ToolID.Checkpoint)
        {
            switch (dir)
            {
                case ToolDirection.Original:
                    indicateDir = Vector3.up;
                    break;
                case ToolDirection.Up:
                    indicateDir = Vector3.left;
                    break;
                case ToolDirection.Left:
                    indicateDir = Vector3.down;
                    break;
                case ToolDirection.Down:
                    indicateDir = Vector3.right;
                    break;
            }
        }
        else
        {
            switch (dir)
            {
                case ToolDirection.Original:
                    indicateDir = Vector3.right;
                    break;
                case ToolDirection.Up:
                    indicateDir = Vector3.up;
                    break;
                case ToolDirection.Left:
                    indicateDir = Vector3.left;
                    break;
                case ToolDirection.Down:
                    indicateDir = Vector3.down;
                    break;
            }
        }

        return indicateDir;
    }
    
    public static bool PairNoteStaveDirection(ToolDirection noteDir, ToolDirection staveDir)
    {
        Vector3 noteSpaceDir = ReturnVectorDirByDirectionAndTool(noteDir, ToolID.Note);
        Vector3 staveSpaceDir = ReturnVectorDirByDirectionAndTool(staveDir, ToolID.Stave);
        if (Mathf.Abs(Vector3.Dot(noteSpaceDir, staveSpaceDir)) >= 1)
        {
            return true;
        }

        return false;
    }
    

    public static Vector2 AnticlockwiseOrthogonalRotation(Vector2 dir)
    {
        return new Vector2(-dir.y, dir.x);
    }

    private static bool gotCanvas;
    private static RectTransform canvasRect;
    private static bool gotMainCamera;
    private static Camera mainCamera;
    public static Vector2 WorldPositionToCanvasPosition(Vector3 worldPos)
    {
        if (!gotCanvas)
        {
            canvasRect = GameObject.Find("Canvas").GetComponent<RectTransform>();
            gotCanvas = true;
        }

        if (!gotMainCamera)
        {
            mainCamera = Camera.main;
            gotMainCamera = true;
        }

        Vector2 ViewportPosition = mainCamera.WorldToViewportPoint(worldPos);
        Vector2 WorldObject_ScreenPosition = new Vector2(
            ((ViewportPosition.x * canvasRect.sizeDelta.x) - (canvasRect.sizeDelta.x * 0.5f)),
            ((ViewportPosition.y * canvasRect.sizeDelta.y) - (canvasRect.sizeDelta.y * 0.5f)));

        return WorldObject_ScreenPosition;
    }
    
    public static T DeepClone<T>(T obj)
    {
        using (var ms = new MemoryStream())
        {
            var formatter = new BinaryFormatter();
            formatter.Serialize(ms, obj);
            ms.Position = 0;

            return (T) formatter.Deserialize(ms);
        }
    }

    public static string ReturnChapterLbNameByIndexAndType(int index, LeaderboardValueType type)
    {
        if(index <= 2) return $"Chapter_{index + 1}_" + type;
        else
        {
            return $"Story_" + type;
        }
    }

    public static int ReturnChapterIndexByLbName(string lbName)
    {
        string indexString = lbName.Split("_")[1];
        switch (indexString)
        {
            case "1":
                return 1;
            case "2":
                return 2;
            case "3":
                return 3;
            default:
                return 4;
        }
    }

    public static string ChangeLbNameByType(string name, LeaderboardValueType type)
    {
        string[] strArr = name.Split("_");
        int arrCount = strArr.Length;
        if (strArr[arrCount - 1] == type.ToString())
        {
            return name;
        }
        else
        {
            strArr[arrCount - 1] = type.ToString();
            return string.Join("_", strArr);
        }
    }

    public static bool ContainsChineseCharacters(string s)
    {
        if (string.IsNullOrEmpty(s))
        {
            return false;
        }

        foreach (char c in s)
        {
            // Check if the character falls within the CJK Unified Ideographs range
            if (c >= '\u4E00' && c <= '\u9FFF')
            {
                return true; // Found a Chinese character
            }
        }
        return false; 
    }
    
    
}
