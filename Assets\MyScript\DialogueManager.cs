using System;
using System.Collections;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;
using Vector2 = UnityEngine.Vector2;

public class DialogueManager : MonoBehaviour
{
    public static DialogueManager Instance { get; private set; }

    public GameObject dialogueUI;

    [SerializeField] private float eachCharDur = 0.1f;
    [SerializeField] private float boxSpeed = 1000;

    [SerializeField] private CharacterExpressionTemplate characterExpressionTemplate;


    [Serializable]
    public class LevelDialogue
    {
        public string levelName;
        public string dialogueName;
    }

    [SerializeField] private List<LevelDialogue> levelDialogueList;
    
    private GameObject videoPlayerObj;

    private Image leftSpeakerImage;
    private Image rightSpeakerImage;
    private TMP_Text contentText;
    private Image contentImage;

    private RectTransform contentRect;

    private DialogueTemplate presentDialogue;

    private GameObject nextDialogueButton;

    private int presentDialogueIndex = -1;

    private bool inDialogue = false;

    private int charIndex = 0;

    private float timeStamp;

    private string currentString;

    private Sprite currentSpeakerImage;
    private AudioClip currentSpeakerAudio;

    private Dictionary<CharacterEnum, float> characterAudioPitch = new Dictionary<CharacterEnum, float>()
    {
        { CharacterEnum.Bunny, 1.2f },
        { CharacterEnum.Arthur, 1f },
        { CharacterEnum.Guard, 1f },
        { CharacterEnum.Planter, 1.5f },
        { CharacterEnum.Switch, 1.0f },
        { CharacterEnum.Transmitter, 1.0f},
        { CharacterEnum.TheOne, 1.0f}
    };

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(this);

        inDialogue = false;
        SetUI();
        //must be called after SetUI
        GetBoxInfo();
    }

    private void SetUI()
    {
        videoPlayerObj = dialogueUI.transform.Find("VideoPlayer").gameObject;
        leftSpeakerImage = dialogueUI.transform.Find("LeftSpeakerImage").GetComponent<Image>();
        leftFaceInitPos = leftSpeakerImage.rectTransform.anchoredPosition;
        rightSpeakerImage = dialogueUI.transform.Find("RightSpeakerImage").GetComponent<Image>();
        rightFaceInitPos = rightSpeakerImage.rectTransform.anchoredPosition;
        contentText = dialogueUI.transform.Find("Content").Find("ContentText").GetComponent<TMP_Text>();
        contentImage = dialogueUI.transform.Find("Content").Find("ContentImage").GetComponent<Image>();
        contentRect = dialogueUI.transform.Find("Content").GetComponent<RectTransform>();
        nextDialogueButton = dialogueUI.transform.Find("Content").Find("ButtonList").Find("ContinueButton").gameObject;
    }


    #region API

    public bool GotLevelNameDialogue(string levelName)
    {
        return levelDialogueList.Any(i => i.levelName == levelName);
    }

    public bool GotLevelDialogueName(string dialogueName)
    {
        return levelDialogueList.Any(i => i.dialogueName == dialogueName);
    }

    public string ReturnLevelDialogueName(string levelName)
    {
        return levelDialogueList.Find(i => i.levelName == levelName).dialogueName;
    }

    public bool PrepareDialogue(string dialogueName)
    {
        presentDialogue = Resources.Load("Dialogue/" + dialogueName) as DialogueTemplate;
        presentDialogueIndex = -1;

        if (presentDialogue != null)
        {
            if (presentDialogue.dialogueList.Count > 0)
            {
                ChangeDialogueState(DialogueState.WaitInput);
                inDialogue = true;
                return true;
            }

            Debug.Log("the dialogue called: " + dialogueName + " is empty");
            return false;
        }

        Debug.Log("there is no such dialogue called: " + dialogueName);
        return false;
    }

    public bool DialogueFinished()
    {
        int nextDialogueIndex = presentDialogueIndex + 1;
        if (nextDialogueIndex >= presentDialogue.dialogueList.Count)
        {
            if (currentDialogueState == DialogueState.WaitInput)
            {
                inDialogue = false;
                return true;
            }
            else
            {
                return false;
            }
        }
        else
        {
            return false;
        }
    }

    public void NextDialogueAction()
    {
        switch (currentDialogueState)
        {
            case DialogueState.WaitInput:
                PutNextDialogue();
                break;
            default:
                if (inDialogue && presentDialogueIndex >= 0)
                {
                    LastFrame();
                    ChangeDialogueState(DialogueState.WaitInput);
                }

                break;
        }
    }

    #endregion

    #region Dialogue animation system

    private enum DialogueState
    {
        BoxAnim,
        TextAnim,
        WaitInput,
    }

    private DialogueState currentDialogueState;

    private List<Char> dialogueCharList = new List<char>();
    private string presentString;

    private Vector3 showPosition;
    private Vector3 hidePosition;
    private Vector3 boxMovement;

    private void GetBoxInfo()
    {
        showPosition = contentRect.anchoredPosition;
        hidePosition = showPosition - new Vector3(0, contentRect.sizeDelta.y + 40, 0);
    }

    private void InitBoxAnim()
    {
        contentRect.anchoredPosition = hidePosition;
        boxMovement = showPosition - hidePosition;
        presentString = "";
        contentText.text = presentString;
        nextDialogueButton.SetActive(false);
    }

    private void InitTextAnim()
    {
        if (presentDialogueIndex < presentDialogue.dialogueList.Count && presentDialogueIndex >= 0)
        {
            CharacterEnum thisCharacter = presentDialogue.dialogueList[presentDialogueIndex].characterType;
            AudioManager.Instance.DialogueAudioSourcePlay(currentSpeakerAudio, characterAudioPitch[thisCharacter]);
            timeStamp = Time.realtimeSinceStartup;

            charIndex = 0;

            UpdateStringLanguage();

            dialogueCharList.Clear();
            foreach (char c in currentString)
            {
                dialogueCharList.Add(c);
            }
        }
    }

    private void UpdateStringLanguage()
    {
        switch (MyLocalizationManager.Instance.currentLanguage)
        {
            case LanguageEnum.English:
                currentString = presentDialogue.dialogueList[presentDialogueIndex].contentText;
                break;
            case LanguageEnum.Chinese:
                currentString = presentDialogue.dialogueList[presentDialogueIndex].contentTextChineseSimplified;
                break;
        }
    }

    private void PutNextDialogue()
    {
        presentDialogueIndex++;
        if (presentDialogueIndex < presentDialogue.dialogueList.Count)
        {
            ChangeDialogueState(DialogueState.BoxAnim);
            PrepareSpeakerImageAndVoice();
            UpdateImage();
        }
    }

    private void Update()
    {
        if (inDialogue)
        {
            switch (currentDialogueState)
            {
                case DialogueState.BoxAnim:
                    AnimateBox();
                    break;
                case DialogueState.TextAnim:
                    AnimateText();
                    break;
            }
        }
    }

    private void AnimateBox()
    {
        //animate box
        //if it reaches the condition then change state
        if (boxMovement.y > 0)
        {
            float move = boxSpeed * Time.unscaledDeltaTime;
            contentRect.anchoredPosition += new Vector2(0, move);
            boxMovement -= new Vector3(0, move, 0);
        }
        else
        {
            contentRect.anchoredPosition = showPosition;
            ChangeDialogueState(DialogueState.TextAnim);
        }
    }

    private void AnimateText()
    {
        //animate text
        //if it reaches the condition then change state
        if (Time.realtimeSinceStartup - timeStamp > eachCharDur)
        {
            timeStamp = Time.realtimeSinceStartup;
            if (charIndex < dialogueCharList.Count)
            {
                presentString += dialogueCharList[charIndex];
                contentText.text = presentString;
                charIndex++;
            }
            else
            {
                ChangeDialogueState(DialogueState.WaitInput);
            }
        }
    }

    private void WaitInput()
    {
        //show btn
        nextDialogueButton.SetActive(true);
    }

    private void LastFrame()
    {
        UpdateStringLanguage();
        contentRect.anchoredPosition = showPosition;
        contentText.text = currentString;
    }

    private void ChangeDialogueState(DialogueState newState)
    {
        currentDialogueState = newState;
        switch (newState)
        {
            case DialogueState.TextAnim:
                InitTextAnim();
                break;
            case DialogueState.WaitInput:
                WaitInput();
                break;
            case DialogueState.BoxAnim:
                InitBoxAnim();
                break;
        }
    }

    #endregion

    #region Update image/video/voice

    private void UpdateImage()
    {
        //play dialogue video
        if (!string.IsNullOrEmpty(presentDialogue.dialogueList[presentDialogueIndex].videoName))
        {
            string url = Path.Combine(Application.streamingAssetsPath, "MyVideo", presentDialogue.dialogueList[presentDialogueIndex].videoName);
            
            videoPlayerObj.GetComponent<VideoPlayer>().url = url;
            videoPlayerObj.GetComponent<RawImage>().color = Color.white;
            videoPlayerObj.GetComponent<VideoPlayer>().Play();
        }
        else
        {
            videoPlayerObj.GetComponent<RawImage>().color = Color.clear;
        }

        //show content image
        if (presentDialogue.dialogueList[presentDialogueIndex].contentImage.Length > 0)
        {
            contentImage.color = Color.white;
            SetContentImage();
        }
        else
        {
            contentImage.color = Color.clear;
        }

        StopFaceAnim();
        //show speaker image
        if (presentDialogue.dialogueList[presentDialogueIndex].leftSide)
        {
            faceAnim = FaceAnim(leftSpeakerImage, 1);
            StartCoroutine(faceAnim);
            leftSpeakerImage.sprite = currentSpeakerImage;
            leftSpeakerImage.color = Color.white;
            rightSpeakerImage.color = Color.clear;
        }
        else
        {
            faceAnim = FaceAnim(rightSpeakerImage, -1);
            StartCoroutine(faceAnim);
            rightSpeakerImage.sprite = currentSpeakerImage;
            leftSpeakerImage.color = Color.clear;
            if(presentDialogue.dialogueList[presentDialogueIndex].characterType == CharacterEnum.TheOne)
               rightSpeakerImage.color = Color.red;
            else
            {
                rightSpeakerImage.color = Color.white;
            }
        }
    }

    private void PrepareSpeakerImageAndVoice()
    {
        CharacterEnum characterType = presentDialogue.dialogueList[presentDialogueIndex].characterType;
        ExpressionEnum expressionType = presentDialogue.dialogueList[presentDialogueIndex].expressionType;

        foreach (DialogueCharacter character in characterExpressionTemplate.dialogueCharacterList)
        {
            if (character.characterType == characterType)
            {
                foreach (DialogueExpression expression in character.characterExpressionList)
                {
                    if (expression.expressionType == expressionType)
                    {
                        currentSpeakerImage = expression.expressionImage;
                        currentSpeakerAudio = expression.expressionAudio;
                    }
                }
            }
        }
    }

    private void SetContentImage()
    {
        bool controlButtonImage = (presentDialogue.dialogueName == "0_Chapter(1)Start_0" && presentDialogueIndex == 4) ||
                                  (presentDialogue.dialogueName == "0_Chapter(1)Start_1" && presentDialogueIndex == 2) ||
                                  (presentDialogue.dialogueName == "1_Jump(1)_0" && presentDialogueIndex == 0);
        
        int contentImageIndex = (int)GameManager.Instance.firstPlayerControlType;

        if (controlButtonImage)
            contentImage.sprite = presentDialogue.dialogueList[presentDialogueIndex].contentImage[contentImageIndex];
        else
        {
            contentImage.sprite = presentDialogue.dialogueList[presentDialogueIndex].contentImage[0];
        }
    }

    private float faceAnimDur = 0.5f;
    private float faceMoveAmount = 300;
    private Vector2 rightFaceInitPos;
    private Vector2 leftFaceInitPos;
    private IEnumerator faceAnim;
    private IEnumerator FaceAnim(Image face, float moveDir)
    {
        RectTransform faceRT = face.rectTransform;
        Vector2 targetPos = faceRT.anchoredPosition;
        faceRT.anchoredPosition -= new Vector2(moveDir * faceMoveAmount, 0);
        Vector2 initPos = faceRT.anchoredPosition;
        float timer = faceAnimDur;
        while (timer > 0)
        {
            timer -= Time.unscaledDeltaTime;
            faceRT.anchoredPosition = Vector2.Lerp(initPos, targetPos, 1 - timer/faceAnimDur);
            yield return null;
        }
    }

    private void StopFaceAnim()
    {
        if (faceAnim != null)
        {
            StopCoroutine(faceAnim);
            rightSpeakerImage.rectTransform.anchoredPosition = rightFaceInitPos;
            leftSpeakerImage.rectTransform.anchoredPosition = leftFaceInitPos;
        }
    }

    #endregion
}