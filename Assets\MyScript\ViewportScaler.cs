using UnityEngine;

[ExecuteAlways]
[RequireComponent(typeof(Camera))]
public class ViewportScaler : MonoBehaviour
{
    private Camera _camera;
    private float _targetAspectRatio;
    [SerializeField] private VirtualMouseController vmc;
    private Rect currentRect;


    private void Awake()
    {
        _camera = GetComponent<Camera>();
        _targetAspectRatio = 16/9f;
        
    }

    private void Start()
    {
        ScaleViewport();
        if(vmc)
           vmc.SetCurrentRect(currentRect);
    }

    private void ScaleViewport()
    {
        // determine the game window's current aspect ratio
        var windowaspect = Screen.width / (float) Screen.height;

        // current viewport height should be scaled by this amount
        var scaleheight = windowaspect / _targetAspectRatio;

        
        // if scaled height is less than current height, add letterbox
        if (scaleheight < 1)
        {
            currentRect = _camera.rect;
            currentRect.width = 1;
            currentRect.height =  scaleheight;
            currentRect.x = 0;
            currentRect.y = (1 - scaleheight) / 2;
            _camera.rect = currentRect;
        }
        else // add pillarbox
        {
            var scalewidth = 1 / scaleheight;

            currentRect = _camera.rect;

            currentRect.width = scalewidth;
            currentRect.height = 1;
            currentRect.x = (1 - scalewidth) / 2;
            currentRect.y = 0;
            _camera.rect = currentRect;
        }
        
        
    }
}
