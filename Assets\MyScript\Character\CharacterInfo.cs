using System.Collections.Generic;
using UnityEngine;

public class CharacterInfo : MonoBehaviour, IAlive
{
    private BagTool characterInfo = new BagTool();

    private List<OtherCharacterBase> behaviourList = new List<OtherCharacterBase>();

    public BagTool ReturnCharacterInfo()
    {
        return characterInfo;
    }

    public void GameStartCreateCharacter(BagTool info)
    {
        characterInfo = info;
        
        if (behaviourList.Count == 0)
        {
            behaviourList.Add(gameObject.AddComponent<KillerController>());
            behaviourList.Add(gameObject.AddComponent<PlanterController>());
            behaviourList.Add(gameObject.AddComponent<SwitchController>());
            behaviourList.Add(gameObject.AddComponent<TransmitterController>());
            gameObject.AddComponent<BoxCollider2D>().size = new Vector2(0.9f, 0.9f);
        }
        

        switch (info.toolID)
        {
            case ToolID.Killer:
                ActivateOneBehaviour(0);
                break;
            case ToolID.Planter:
                ActivateOneBehaviour(1);
                break;
            case ToolID.Switch:
                ActivateOneBehaviour(2);
                break;
            case ToolID.Transmitter:
                ActivateOneBehaviour(3);
                break;
        }
    }

    private void ActivateOneBehaviour(int index)
    {
        foreach (OtherCharacterBase behaviour in behaviourList)
        {
            behaviour.enabled = false;
        }

        behaviourList[index].enabled = true;
        behaviourList[index].InitCharacter(characterInfo.toolID);
    }

    public void GotAttacked()
    {
        switch (characterInfo.toolID)
        {
            case ToolID.Killer:
                behaviourList[0].CharacterGotAttacked();
                break;
            case ToolID.Planter:
                behaviourList[1].CharacterGotAttacked();
                break;
            case ToolID.Transmitter:
                behaviourList[3].CharacterGotAttacked();
                break;
        }
    }


    public void DisableAllBehavior()
    {
        foreach (OtherCharacterBase behaviour in behaviourList)
        {
            behaviour.CloseCharacter();
            behaviour.enabled = false;
        }
    }
    
    
}
