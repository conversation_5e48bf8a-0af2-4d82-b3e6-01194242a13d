using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class OptionInteractionUI : InteractionUI
{
    private Color textHighLightColor;
    private Color textNormalColor;

    private Button nextButton;
    private Button lastButton;

    protected override void DecideColor()
    {
        textHighLightColor = ReturnColor(ColorEnum.UIButtonHighLight);
        textNormalColor = ReturnColor(ColorEnum.UIButtonOriginal);
    }
    protected override void HighLight(int index, bool highlight)
    {
        if (index >= 0 && index < buttonList.Count)
        {
            buttonList[index].GetComponentInChildren<TMP_Text>().color = highlight ? textHighLightColor : textNormalColor;
            SetButton("Next", buttonList[index].transform);
            SetButton("Last", buttonList[index].transform);
        }
            
    }

    private void SetButton(string keyWord, Transform parent)
    {
        if (parent.childCount > 0)
        {
            foreach (Transform child in parent)
            {
                if (child.name.Contains(keyWord))
                {
                    if (keyWord == "Next")
                    {
                        nextButton = child.GetComponent<Button>();
                    }else if (keyWord == "Last")
                    {
                        lastButton = child.GetComponent<Button>();
                    }
                    
                }
                else
                {
                    SetButton(keyWord, child);
                }
            }
        }
        
    }
    

    public override void UIRespondPlayerVerticalInput(int changeAmount)
    {
        changeAmount *= -1;
        UpdateSelection(changeAmount);
    }

    public override void UIRespondPlayerHorizontalInput(int changeAmount, int playerIndex)
    {
        if (playerIndex == 0)
        {
            if (changeAmount == 1)
            {
                if(nextButton != null)
                    nextButton.onClick.Invoke();
            }
            else if(changeAmount == -1)
            {
                if(lastButton != null)
                    lastButton.onClick.Invoke();
            }
        }
    }
}
