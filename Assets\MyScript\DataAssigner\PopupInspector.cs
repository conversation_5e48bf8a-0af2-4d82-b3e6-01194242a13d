using TMPro;
using UnityEngine;

public class PopupInspector : InspectorDataAssigner
{
    [Serialize<PERSON>ield] protected GameObject mapTextureCam;
    [SerializeField] private GameObject imgObj;

    [SerializeField] private GameObject textContent;
    [SerializeField] private TMP_Text nameTMP;
    [SerializeField] private TMP_Text descriptionTMP;
    
    [SerializeField] private CollectionEditorMapDataAssigner collectionEditorMapDataAssigner;
    
    private MapPreview currentMapPreview;

    private string emptyText = "";
    
    private void OnEnable()
    {
        ClearInspector();
    }
    

    public override void ShowMapInspector(MapPreview map, MapOrCollectionItemDataAssigner itemDataAssigner, int mapSequence)
    {
        mapTextureCam.SetActive(true);
        currentInspectType = InspectType.Map;
        currentMapPreview = map;
        textContent.SetActive(true);
        imgObj.SetActive(true);
        AssignData();
    }
    
    protected override void AssignData()
    {
        nameTMP.text = currentMapPreview.mapName;
        descriptionTMP.text = currentMapPreview.mapDescription;
    }

    public override void ClearInspector()
    {
        currentMapPreview = null;
        imgObj.SetActive(false);
        textContent.SetActive(false);
        mapTextureCam.SetActive(false);
    }

    public void AddMapButtonAction()
    {
        if(currentMapPreview != null){
            collectionEditorMapDataAssigner.AddMapByPopup(currentMapPreview.mapID);
        }
    }
    
}
