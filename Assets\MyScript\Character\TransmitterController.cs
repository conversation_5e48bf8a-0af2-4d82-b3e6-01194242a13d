using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

public class TransmitterController : OtherCharacterBase
{
    //TODO:
    //if this is above a "Trash", then destroy it
    private Animator transmitterAnimator;

    private int transmitStateBoolID = Animator.StringToHash("Transmit");

    private class CheckGroundPointClass
    {
        public CheckGroundPointClass(Vector3 pos, float size)
        {
            checkPoint = pos - new Vector3(0, size + 0.1f, 0);
            checkDistance = size;
        }

        public float checkDistance;
        public Vector3 checkPoint;
        public Collider2D collider;
    }

    private CheckGroundPointClass[] checkGroundArr = new[]
    {
        new CheckGroundPointClass(new Vector3(-0.4f, -0.5f, 0), 0.08f),
        new CheckGroundPointClass(new Vector3(0.4f, -0.5f, 0), 0.08f)
    };

    private enum FaceEnum
    {
        Left,
        Right,
    }

    private class DetectZone
    {
        public DetectZone(Vector3 pos, Vector2 scale, FaceEnum type)
        {
            switch (type)
            {
                case FaceEnum.Left:
                    pos -= new Vector3(.5f * scale.x + 0.05f, 0, 0);
                    break;
                case FaceEnum.Right:
                    pos += new Vector3(.5f * scale.x + 0.05f, 0, 0);
                    break;
            }

            origin = pos;
            boxScale = scale;
        }

        public Vector3 origin;
        public Vector2 boxScale;
        public Collider2D collider;
    }

    private Dictionary<FaceEnum, DetectZone> detectZoneDict = new Dictionary<FaceEnum, DetectZone>
    {
        { FaceEnum.Left, new DetectZone(new Vector3(-0.5f, 0, 0), new Vector2(0.1f, 0.6f), FaceEnum.Left) },
        { FaceEnum.Right, new DetectZone(new Vector3(0.5f, 0, 0), new Vector2(0.1f, 0.6f), FaceEnum.Right) },
    };


    private float horVelocity = 1;

    private RaycastHit2D killHit;
    private Vector3 killOrigin;
    private ChunkClass candidateChunkClass;
    private ChunkClass formerCandidateChunkClass;
    private bool chunkRotate;

    private bool leftGrounded = true;
    private bool rightGrounded = true;

    private bool rightFaceTouch = false;
    private bool leftFaceTouch = false;


    public override void InitCharacter(ToolID toolID)
    {
        base.InitCharacter(toolID);
        if (transmitterAnimator == null)
            transmitterAnimator = GetComponent<Animator>();
        transmitterAnimator.runtimeAnimatorController = GlobalParameters.Instance.ReturnTransmitterController();

        //prevent the attack at restart
        detectZoneDict[FaceEnum.Right].collider = null;
        detectZoneDict[FaceEnum.Left].collider = null;
    }

    public override void CloseCharacter()
    {
        base.CloseCharacter();
        ClearSubscribe();
        gameObject.SetActive(false);
    }

    // Update is called once per frame
    void FixedUpdate()
    {
        leftGrounded = OnGround(0);
        rightGrounded = OnGround(1);
        SetCandidate();
        SetChunkRotate();

        leftFaceTouch = OnFace(FaceEnum.Left);
        rightFaceTouch = OnFace(FaceEnum.Right);

        if (!leftGrounded)
        {
            horVelocity = 1;
            Flip();
        }
        else if (!rightGrounded)
        {
            horVelocity = -1;
            Flip();
        }
        else if (leftFaceTouch)
        {
            horVelocity = 1;
            Flip();
        }
        else if (rightFaceTouch)
        {
            horVelocity = -1;
            Flip();
        }

        if ((!leftGrounded && !rightGrounded) || chunkRotate)
            ApplyGravity();
        else
        {
            ApplyVelocity();

            //"ApplyGround should always be after SetCandidate"
            ApplyGround();
        }

        ApplyBlock();
        CloseOutOfScreenCharacter();
    }

    private void Flip()
    {
        Vector3 moveDir = new Vector3(horVelocity > 0 ? 1 : -1, 0, 0);
        characterTransform.right = moveDir;
    }

    private GameObject candidate;
    private bool hasCandidate = false;
    private float candidatePosY = 0;

    private bool OnGround(int index)
    {
        Vector3 detectOrigin = characterTransform.position + checkGroundArr[index].checkPoint;

        checkGroundArr[index].collider =
            Physics2D.OverlapCircle(detectOrigin, checkGroundArr[index].checkDistance);

        if (checkGroundArr[index].collider != null)
        {
            return true;
        }

        return false;
    }

    private void SetCandidate()
    {
        hasCandidate = false;

        if (checkGroundArr[0].collider == checkGroundArr[1].collider && checkGroundArr[0].collider != null)
        {
            if (candidate != checkGroundArr[0].collider.gameObject &&
                checkGroundArr[0].collider.CompareTag("Stickable"))
            {
                candidate = checkGroundArr[0].collider.gameObject;
                candidateChunkClass = candidate.GetComponentInParent<ChunkClass>();

                if (formerCandidateChunkClass != candidateChunkClass)
                {
                    if (formerCandidateChunkClass != null)
                        formerCandidateChunkClass.UnsubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);

                    candidateChunkClass.SubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
                    formerCandidateChunkClass = candidateChunkClass;
                }

                //transmit desire
                if (candidateChunkClass.TransmitDesire(candidate))
                    TransmitStateAnim();
            }

            candidatePosY = candidate.transform.position.y;
            hasCandidate = true;
        }
    }

    private void SetChunkRotate()
    {
        if (candidateChunkClass != null)
        {
            chunkRotate = candidateChunkClass.inRotateProcedure;
        }
        else
        {
            chunkRotate = false;
        }
    }


    private DetectZone tempDetectZone;

    private bool OnFace(FaceEnum face)
    {
        tempDetectZone = detectZoneDict[face];
        Vector3 detectOrigin = characterTransform.position + tempDetectZone.origin;
        tempDetectZone.collider = Physics2D.OverlapBox(detectOrigin, tempDetectZone.boxScale, 0);
        if (tempDetectZone.collider != null)
        {
            if (tempDetectZone.collider.CompareTag("Stickable"))
            {
                tempDetectZone.collider = null;
                return true;
            }

            tempDetectZone.collider = null;
        }

        return false;
    }

    private void ApplyVelocity()
    {
        Vector3 offset = new Vector3(horVelocity * Time.fixedDeltaTime, 0, 0);

        if (candidateChunkClass != null)
            offset += candidateChunkClass.accumulatedMove * Time.fixedDeltaTime;

        characterTransform.position += offset;
    }

    private void ApplyGround()
    {
        if (hasCandidate)
            characterTransform.position = new Vector3(characterTransform.position.x, candidatePosY + 1, 0);
    }

    private void ApplyGravity()
    {
        characterTransform.position += new Vector3(0, -6 * Time.fixedDeltaTime, 0);
    }

    private void ApplyBlock()
    {
        if (candidateChunkClass != null)
        {
            if (candidateChunkClass.GotChildInRange(characterTransform.position, 0.3f))
            {
                CharacterDie();
            }
        }
    }

    private Task transmitAnimDone;

    private async Task TransmitStateAnim()
    {
        float tempV = horVelocity;
        horVelocity = 0;
        transmitterAnimator.SetBool(transmitStateBoolID, true);
        await Task.Delay(800);
        horVelocity = tempV;
        transmitterAnimator.SetBool(transmitStateBoolID, false);
    }

    public override void CharacterGotAttacked()
    {
        CharacterDie();
    }

    private void MoveByAttach(Vector3 offset)
    {
        characterTransform.position += offset;
    }

    private void ChunkChangeByAttach()
    {
        if (candidate != null)
        {
            candidateChunkClass = candidate.GetComponentInParent<ChunkClass>();
            if (formerCandidateChunkClass != candidateChunkClass)
            {
                if (formerCandidateChunkClass != null)
                    formerCandidateChunkClass.UnsubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
                if (candidateChunkClass != null)
                    candidateChunkClass.SubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
            }
        }
    }

    private void ClearSubscribe()
    {
        if (formerCandidateChunkClass != null)
            formerCandidateChunkClass.UnsubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
        if (candidateChunkClass != null)
            candidateChunkClass.SubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);

        formerCandidateChunkClass = null;
        candidateChunkClass = null;
    }
}