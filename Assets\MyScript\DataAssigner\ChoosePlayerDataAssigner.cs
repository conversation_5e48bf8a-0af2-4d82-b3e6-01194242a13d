using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ChoosePlayerDataAssigner : MonoBehaviour
{
    //get the current player list and show the picker
    //listen for new player to enter, then show their relevant picker
    //one character cannot be selected twice
    //when all player selected their character
    //show continue button

    [SerializeField] private Transform content;
    [SerializeField] private GameObject itemTemplate;
    [SerializeField] private GameObject continueButtonObj;

    private ColorManager colorManager;
    private int playerColorStartIndex;
    private int playerMaxCount = 8;

    private bool firstTime = true;
    
    

    private class PlayerShowcase
    {
        public PlayerShowcase(GameObject obj)
        {
            UIObj = obj;
            playerImage = obj.transform.Find("Image").GetComponent<Image>();
            pointerImage = obj.transform.Find("Pointer").GetComponent<Image>();
            playerName = obj.transform.Find("Name").GetComponent<TMP_Text>();
            playerButton = obj.GetComponent<Button>();
        }

        public GameObject UIObj;
        public Image playerImage;
        public Image pointerImage;
        public TMP_Text playerName;
        public Button playerButton;
    }

    private List<PlayerShowcase> playerShowcaseList = new List<PlayerShowcase>();

    private List<int> playerPicker = new List<int>();

    public void ShowPlayer()
    {
        //set parameters
        SetParameters();

        //init selection
        PlayerChooseCharacter(0, 0);
    }

    private void SetParameters()
    {
        if (colorManager == null)
            colorManager = GameManager.Instance.ReturnColorManager();
        continueButtonObj.GetComponent<TMP_Text>().color = ReturnColorByEnum(ColorEnum.UIButtonHighLight);
        for (int i = 0; i < playerMaxCount; i++)
        {
            if (firstTime)
            {
                //init player show case list
                if (i == 0)
                {
                    playerShowcaseList.Add(new PlayerShowcase(itemTemplate));
                    playerShowcaseList[0].playerImage.color = ReturnColorByPlayerCount(0);
                    playerShowcaseList[0].pointerImage.color = ReturnColorByPlayerCount(0);
                    playerShowcaseList[0].playerName.color = ReturnColorByPlayerCount(0);
                }
                else
                {
                    playerShowcaseList.Add(new PlayerShowcase(Instantiate(itemTemplate, content)));
                    playerShowcaseList[i].playerImage.color = ReturnColorByPlayerCount(i);
                    playerShowcaseList[i].pointerImage.color = ReturnColorByPlayerCount(i);
                    playerShowcaseList[i].playerName.color = ReturnColorByPlayerCount(i);
                }
            }
            
            UpdateAppearance(0, i, false);
        }


        //init player picker
        for (int i = 0; i < playerMaxCount; i++)
        {
            if (i >= playerPicker.Count)
            {
                playerPicker.Add(-1);
            }
            else
            {
                playerPicker[i] = -1;
            }
        }


        firstTime = false;
    }
    
    private void PlayerChooseCharacter(int pIndex, int cIndex)
    {
        if (!playerPicker.Contains(cIndex))
        {
            UpdateAppearance(pIndex, playerPicker[pIndex], false);
            playerPicker[pIndex] = cIndex;
            UpdateAppearance(pIndex, cIndex, true);
            GameManager.Instance.SetPlayerColor(pIndex,ReturnColorEnumByPlayerCount(cIndex), ReturnColorByPlayerCount(cIndex));
        }
    }

    private string emptyString = "";

    private void UpdateAppearance(int pIndex, int cIndex, bool show)
    {
        if (show)
        {
            playerShowcaseList[cIndex].playerName.text = "P" + (pIndex + 1);
            playerShowcaseList[cIndex].pointerImage.color = ReturnColorByPlayerCount(cIndex);
        }
        else
        {
            if (cIndex >= 0)
            {
                playerShowcaseList[cIndex].playerName.text = emptyString;
                playerShowcaseList[cIndex].pointerImage.color = Color.clear;
            }
        }
    }
    

    private int FindAvailableCharacterGivenDirection(int cIndex, int dir)
    {
        //int nextIndex = cIndex + dir;
        if (cIndex >= 0 && cIndex < playerMaxCount)
        {
            if (!playerPicker.Contains(cIndex))
                return cIndex;

            return FindAvailableCharacterGivenDirection(cIndex + dir, dir);
        }

        return -1;
    }


    #region API

    public void AddPlayer(int pIndex)
    {
        int cIndex = FindAvailableCharacterGivenDirection(0, 1);
        if (cIndex >= 0)
            PlayerChooseCharacter(pIndex, cIndex);
    }
    

    public void UIRespondPlayerHorizontalInput(int pIndex, int changeAmount)
    {
        int cIndex = FindAvailableCharacterGivenDirection(playerPicker[pIndex], changeAmount);

        if (cIndex >= 0)
            PlayerChooseCharacter(pIndex, cIndex);
    }

    public void UIRespondPlayerSubmitInput(int pIndex)
    {
        if (pIndex == 0)
        {
            continueButtonObj.GetComponent<Button>().onClick?.Invoke();
        }
    }

    #endregion

    private Color ReturnColorByPlayerCount(int count) => colorManager.PlayerColorByIndex(count);

    private ColorEnum ReturnColorEnumByPlayerCount(int count)
    {
        return (ColorEnum)((int)ColorEnum.PlayerColor1 + count);
    }

    private Color ReturnColorByEnum(ColorEnum colorEnum) => colorManager.ReturnColorByType(colorEnum);

}