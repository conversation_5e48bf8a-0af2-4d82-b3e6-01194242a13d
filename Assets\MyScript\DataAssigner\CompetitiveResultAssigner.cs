using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CompetitiveResultAssigner : MonoBehaviour
{
    //player rank

    #region Player rank

    [SerializeField] private Transform rankContent;

    [SerializeField] private GameObject rankItemTemplate;

    private int maxPlayerCount = 8;

    private class RankItem
    {
        public RankItem(GameObject obj)
        {
            UIObj = obj;
            playerImage = obj.transform.Find("Image").GetComponent<Image>();
            scoreTMP = obj.transform.Find("Score").GetComponent<TMP_Text>();
        }

        public GameObject UIObj;
        public TMP_Text scoreTMP;
        public Image playerImage;
    }

    private List<RankItem> rankItemList = new List<RankItem>();

    private List<int> rankList = new List<int>();
    
    #endregion
    
    //each map data

    #region Map data

    [SerializeField] private Transform mapContent;
    [SerializeField] private GameObject mapItemTemplate;
    [SerializeField] private RectTransform upperBound;
    [SerializeField] private RectTransform lowerBound;

    private int maxMapItemCount = 13;

    private class MapItem
    {
        public MapItem(GameObject obj, int index)
        {
            UIObj = obj;
            rectTransform = obj.GetComponent<RectTransform>();
            mapIndex = index;
            mapNameTMP = obj.transform.Find("MapName").GetComponent<TMP_Text>();
            playerIndexTMP = obj.transform.Find("Winner").GetComponent<TMP_Text>();
        }

        public GameObject UIObj;
        public RectTransform rectTransform;
        public int mapIndex;
        public TMP_Text mapNameTMP;
        public TMP_Text playerIndexTMP;
    }

    private List<MapItem> mapItemList = new List<MapItem>();
    

    #endregion

    private List<LevelResult> collectionResultList;

    private CollectionClass currentCollection;

    private ColorManager colorManager;

    private bool firstTime = true;

    public void ShowResult(List<LevelResult> resultList, CollectionClass collection)
    {
        collectionResultList = resultList;
        currentCollection = collection;

        if (firstTime)
        {
            InitParameters();
            firstTime = false;
        }
        
        posDiff = 0;
        
        //show player rank
        ShowPlayerRank();
        //show each map
        ShowMap();
    }

    private void InitParameters()
    {
        rankContentRect = rankContent.GetComponent<RectTransform>();
        mapRectTransform = mapContent.GetComponent<RectTransform>();
        mapVerticalLayoutGroup = mapContent.GetComponent<VerticalLayoutGroup>();
        colorManager = GameManager.Instance.ReturnColorManager();
        for (int i = 0; i < maxPlayerCount; i++)
        {
            if (i == 0)
            {
                rankItemList.Add(new RankItem(rankItemTemplate));
            }
            else
            {
                rankItemList.Add(new RankItem(Instantiate(rankItemTemplate, rankContent)));
            }
        }
        
        for (int i = 0; i < maxMapItemCount; i++)
        {
            if (i == 0)
            {
                mapItemList.Add(new MapItem(mapItemTemplate, i));
            }
            else
            {
                mapItemList.Add(new MapItem(Instantiate(mapItemTemplate, mapContent), i));
            }
        }
    }

    #region Player Rank

    private int playerCount;
    private float rankItemHeight = 180;
    private RectTransform rankContentRect;

    private void ShowPlayerRank()
    {
        playerCount = currentCollection.playerCount;
        rankContentRect.sizeDelta = new Vector2(rankContentRect.sizeDelta.x,playerCount*rankItemHeight);
        UpdateRankList();
        for (int j = 0; j < rankItemList.Count; j++)
        {
            if (j < rankList.Count)
            {
                int playerIndex = rankList[j];
                rankItemList[j].UIObj.SetActive(true);
                rankItemList[j].playerImage.color = ReturnPlayerColorByIndex(playerIndex);
                rankItemList[j].scoreTMP.text = collectionResultList.Count(i => i.playerIndex == playerIndex).ToString();
            }
            else
            {
                rankItemList[j].UIObj.SetActive(false);
            }
        }
    }
    
    private void UpdateRankList()
    {
        rankList = collectionResultList.Where(i => i.playerIndex != -1).GroupBy(q => q.playerIndex)
            .OrderByDescending(gp => gp.Count())
            .Select(g => g.Key).ToList();

        
        for (int i = 0; i < playerCount; i++)
        {
            if(!rankList.Contains(i))
                rankList.Add(i);
        }
    }


    private Color ReturnPlayerColorByIndex(int playerIndex)
    {
        ColorEnum colorType = GameManager.Instance.playerList[playerIndex].playerColor;
        return colorManager.ReturnColorByType(colorType);
    }

    #endregion

    #region Map data
    private int mapCount;
    private RectTransform mapRectTransform;
    private float mapItemHeight = 60;
    
    private int headPointer;
    private int rearPointer;

    private bool mapCanScroll = false;
    

    private void ShowMap()
    {
        SetMapParameters();
        for (int i = 0; i < maxMapItemCount; i++)
        {
            AssignOneMap(i, i);
        }
    }

    private void SetMapParameters()
    {
        mapCount = currentCollection.mapIDList.Count;
        mapRectTransform.sizeDelta = new Vector2(mapRectTransform.sizeDelta.x, mapCount*mapItemHeight);
        mapRectTransform.anchoredPosition = new Vector2(0, 0);
        headPointer = 0;
        rearPointer = mapCount >= maxMapItemCount ? maxMapItemCount - 1 : mapCount - 1;
    }

    private void AssignOneMap(int objIndex, int sequence)
    {
        if (sequence >= 0 && sequence < currentCollection.mapIDList.Count)
        {
            int mapID = currentCollection.mapIDList[sequence];
            int wonPlayerIndex = collectionResultList[sequence].playerIndex;
            mapItemList[objIndex].UIObj.SetActive(true);
            mapItemList[objIndex].mapIndex = sequence;
            mapItemList[objIndex].mapNameTMP.text = GlobalParameters.Instance
                .ReturnMapPreviewByID(mapID).mapName;
            
            mapItemList[objIndex].playerIndexTMP.text = wonPlayerIndex >= 0 ? "P" + (wonPlayerIndex + 1) : "/";
        }
        else
        {
            mapItemList[objIndex].UIObj.SetActive(false);
        }
    }


    #region Scroll move && arrange
    private float changeStep = 60;

    private VerticalLayoutGroup mapVerticalLayoutGroup;

    private float contentPosY;
    private float posDiff;


    private void Update()
    {
        if (mapCanScroll)
        {
            CheckStep();
        }
    }
    
    private void CheckStep()
    {
        float diff = mapRectTransform.anchoredPosition.y - contentPosY;
        contentPosY = mapRectTransform.anchoredPosition.y;
        
        if (Mathf.Abs(diff) > 0.5f)
            posDiff = diff;
        

        if (upperBound == null || lowerBound == null)
            return;
        
        if (posDiff > 0 && mapItemList[headPointer].rectTransform.position.y > upperBound.position.y)
        {
            MoveItem(1);
        }else if (posDiff < 0 && mapItemList[rearPointer].rectTransform.position.y < lowerBound.position.y)
        {
            MoveItem(-1);
        }
    }

    private void MoveItem(int dir)
    {
        if (dir > 0)
        {
            //check if there is new data behind rear
            //the head button goes to rear, and take the new data
            int newDataIndex = mapItemList[rearPointer].mapIndex + 1;
            if (newDataIndex < currentCollection.mapIDList.Count)
            {
                mapItemList[headPointer].UIObj.transform.SetSiblingIndex(maxMapItemCount - 1);
                AssignOneMap(headPointer, newDataIndex);
                rearPointer = headPointer;
                headPointer = headPointer + 1 >= maxMapItemCount ? 0 : headPointer + 1;
                mapVerticalLayoutGroup.padding.top += (int)changeStep;
            }
        }
        else
        {
            //check if there is new data before head
            //the rear goes to head, and take the new data
            int newDataIndex = mapItemList[headPointer].mapIndex - 1;
            if (newDataIndex >= 0)
            {
                mapItemList[rearPointer].UIObj.transform.SetSiblingIndex(0);
                AssignOneMap(rearPointer, newDataIndex);
                headPointer = rearPointer;
                rearPointer = rearPointer - 1 < 0 ? maxMapItemCount - 1 : rearPointer - 1;
                mapVerticalLayoutGroup.padding.top -= (int)changeStep;
            }
        }
    }
    #endregion

    #endregion
}
