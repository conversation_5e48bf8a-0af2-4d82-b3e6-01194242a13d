using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using Random = UnityEngine.Random;

public class CustomParticleEffect : MonoBehaviour
{
    [SerializeField] private int particleCount = 20;
    [SerializeField] private GameObject particleTemplate;
    [SerializeField] private float initialRadius = 1;
    [SerializeField] private float moveSpeed;

    private Transform parent;

    private Color particleColor = Color.clear;

    private class ParticleItem
    {
        public ParticleItem(GameObject obj)
        {
            obj.SetActive(true);
            itemT = obj.transform;
            sr = obj.GetComponent<SpriteRenderer>();
        }
        
        public Transform itemT;
        public SpriteRenderer sr;
        public Vector3 interpolateStartPos;
    }

    private List<ParticleItem> particleList = new List<ParticleItem>();

    private bool hasInitialized = false;
    
    
    // Start is called before the first frame update
    void Start()
    {
        if (!hasInitialized)
        {
            parent = transform;
            InitParticleList();
            ShowParticle(false);
            hasInitialized = true;
        }
        
    }
    
    

    private void InitParticleList()
    {
        for (int i = 0; i < particleCount; i++)
        {
            particleList.Add(new ParticleItem(Instantiate(particleTemplate, parent.position, Quaternion.identity ,parent)));
        }
    }

    public void ShowParticle(bool show)
    {
        foreach (ParticleItem item in particleList)
        {
            item.sr.color = show ? particleColor : Color.clear;
        }
    }
    
    
    public void SetColor(Color color)
    {
        particleColor = color;
    }
    

    public async Task PlayExplosion(Vector3 explodePos, float explodeDur)
    {
        //show particle
        ShowParticle(true);

        //set particles in random position
        foreach (var p in particleList)
        {
            p.interpolateStartPos = explodePos +  initialRadius * 4 * Random.insideUnitSphere ;
        }
        
        float timer = 0;
        
        while (timer <= explodeDur)
        {
            timer += GameConst.animUpdateInterval;
            foreach (var p in particleList)
            {
                p.itemT.position = Vector3.Slerp(explodePos, p.interpolateStartPos, timer/explodeDur);
            }
            
            await Task.Delay(20);
        }
        ShowParticle(false);
    }

    public async Task PlayRevive(Transform targetTransform, float reviveDur, Action ctx)
    {
        ShowParticle(true);
        SetInterpolateStartPos();
        
        float timer = reviveDur;
        
        while (timer > 0)
        {
            float deltaDur = Time.unscaledDeltaTime;
            timer -= deltaDur;
            foreach (ParticleItem p in particleList)
            {
                p.itemT.position = Vector3.Slerp(p.interpolateStartPos, targetTransform.position, 1 - timer/reviveDur);
            }
            await Task.Yield();
        }
        
        ctx?.Invoke();
        ShowParticle(false);
    }

    private void SetInterpolateStartPos()
    {
        foreach (var p in particleList)
        {
            p.interpolateStartPos = p.itemT.position;
        }
    }
    


}
