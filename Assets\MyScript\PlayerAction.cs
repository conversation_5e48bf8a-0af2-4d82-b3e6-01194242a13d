using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerAction : MonoBehaviour
{
    public PlayerController playerController;

    public float playerSpeed;

    public float fullJumpPressDur = 0.15f;

    private float horizontalMove = 0f;

    private bool jump;

    private PlayerInput playerInput;

    private int playerIndex;


    private Vector2 selectUIMove = Vector2.zero;
    

    //if this new added player using keyboard control 2
    private bool keyBoard2;

    private float noInputTimer = 0;
    private float noInputMaxTimer = 8;
    
    private InstructionController instructionController;


    public void SetPlayerInput(int pIndex, bool _keyBoard2 = false)
    {
        playerInput = GetComponent<PlayerInput>();
        playerIndex = pIndex;
        keyBoard2 = _keyBoard2;
    }

    public void PlayerMove(InputAction.CallbackContext ctx)
    {
        if (!keyBoard2)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Performed:
                    horizontalMove = ctx.ReadValue<float>() * playerSpeed;
                    break;
                case InputActionPhase.Canceled:
                    horizontalMove = 0;
                    break;
            }
            noInputTimer = 0;
        }

    }

    public void PlayerJump(InputAction.CallbackContext ctx)
    {
        if (!keyBoard2)
        {
            if (!GameManager.Instance.IfGamePaused())
            {
                switch (ctx.phase)
                {
                    case InputActionPhase.Started:
                        jump = true;
                        playerController.HalfJump(false);
                        break;
                    case InputActionPhase.Canceled:
                        if (ctx.duration < fullJumpPressDur)
                            playerController.HalfJump(true);
                        break;
                }
                noInputTimer = 0;
            }
        }

    }

    public void PlayerPut(InputAction.CallbackContext ctx)
    {
        if (!keyBoard2)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Performed:
                    playerController.StampCandidate();
                    return;
            }
            noInputTimer = 0;
        }
    }

    public void PlayerPause(InputAction.CallbackContext ctx)
    {
        switch (ctx.phase)
        {
            case InputActionPhase.Canceled:
                if(playerIndex == 0)
                   GameManager.Instance.EscapeFunction();
                break;
        }
    }

    public void PlayerStartDialogue(InputAction.CallbackContext ctx)
    {
        switch (ctx.phase)
        {
            //state change in cancel phase,
            //because state change would change the input map
            //if it is in start phase, then cancel phase will not happen in "player" input map
            //it will stay registered
            //when we call the same action next time in "player" input map
            //only the registered cancel phase will be called
            case InputActionPhase.Canceled:
                GameManager.Instance.PlayerRequestGamePlayDialogue();
                break;
        }
    }

    public void PlayerMove1(InputAction.CallbackContext ctx)
    {
        if (keyBoard2)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Performed:
                    horizontalMove = ctx.ReadValue<float>() * playerSpeed;
                    break;
                case InputActionPhase.Canceled:
                    horizontalMove = 0;
                    break;
            }
            noInputTimer = 0;
        }
    }

    public void PlayerJump1(InputAction.CallbackContext ctx)
    {
        if (keyBoard2)
        {
            if (!GameManager.Instance.IfGamePaused())
            {
                switch (ctx.phase)
                {
                    case InputActionPhase.Started:
                        jump = true;
                        playerController.HalfJump(false);
                        break;
                    case InputActionPhase.Canceled:
                        if (ctx.duration < fullJumpPressDur)
                            playerController.HalfJump(true);
                        break;
                }
                noInputTimer = 0;
            }
        }
    }

    public void PlayerPut1(InputAction.CallbackContext ctx)
    {
        if (keyBoard2)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Performed:
                    playerController.StampCandidate();
                    return;
            }
            noInputTimer = 0;
        }
    }

    public void UIVerticalMove(InputAction.CallbackContext ctx)
    {
        switch (ctx.phase)
        {
            case InputActionPhase.Performed:
                selectUIMove = new Vector2(0, ctx.ReadValue<float>());
                ActivateMove();
                break;
            case InputActionPhase.Canceled:
                selectUIMove = Vector2.zero;
                UIMoveTimer = 0;
                break;
        }
    }

    public void UIHorizontalMove(InputAction.CallbackContext ctx)
    {
        if (!keyBoard2)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Performed:
                    selectUIMove = new Vector2(ctx.ReadValue<float>(), 0);
                    ActivateMove();
                    break;
                case InputActionPhase.Canceled:
                    selectUIMove = Vector2.zero;
                    UIMoveTimer = 0;
                    break;
            }
        }
    }

    public void UIHorizontalMove1(InputAction.CallbackContext ctx)
    {
        if (keyBoard2)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Performed:
                    selectUIMove = new Vector2(ctx.ReadValue<float>(), 0);
                    ActivateMove();
                    break;
                case InputActionPhase.Canceled:
                    selectUIMove = Vector2.zero;
                    UIMoveTimer = 0;
                    break;
            }
        }
    }

    public void UIChoose(InputAction.CallbackContext ctx)
    {
        switch (ctx.phase)
        {
            case InputActionPhase.Performed:
                if (ctx.control.name == "space" && GameManager.Instance.ReturnPresentState() != StateEnum.Dialogue)
                {
                    break;
                }
                    
                GameManager.Instance.HandlePlayerSubmitInput_UIMode(playerIndex);
                break;
        }
    }

    public void UIEscape(InputAction.CallbackContext ctx)
    {
        switch (ctx.phase)
        {
            case InputActionPhase.Performed:
                if(playerIndex == 0)
                    GameManager.Instance.EscapeFunction();
                break;
        }
    }


    public void UIDelete(InputAction.CallbackContext ctx)
    {
        if (playerIndex == 0)
        {
            switch (ctx.phase)
            {
                case InputActionPhase.Started:
                    GameManager.Instance.PlayerDeleteInput_UIMode_Started();
                    break;
                case InputActionPhase.Performed:
                    GameManager.Instance.PlayerDeleteInput_UIMode_Performed();
                    break;
                case InputActionPhase.Canceled:
                    GameManager.Instance.PlayerDeleteInput_UIMode_Canceled();
                    break;
            }
        }
        
    }


    private float UIMoveDur = 1f;
    private float UIMoveTimer = 0;

    private void FixedUpdate()
    {
        playerController.Move(horizontalMove * Time.fixedDeltaTime, jump);
        jump = false;

    }

    private void Update()
    {
        if (Mathf.Abs(selectUIMove.x) > 0.2f || Mathf.Abs(selectUIMove.y) > 0.2f)
        {
            KeepUIMoving();
        }
        
    }

    private void KeepUIMoving()
    {
        if (UIMoveTimer >= UIMoveDur)
        {
            UIMoveTimer = 0;
            ActivateMove();
        }
        else
        {
            UIMoveTimer += Time.unscaledDeltaTime;
        }
    }

    private void ActivateMove()
    {
        int verticalMoveDir = 0;
        int horizontalMoveDir = 0;

        if (selectUIMove.x > 0)
            horizontalMoveDir = 1;
        else if (selectUIMove.x < 0)
            horizontalMoveDir = -1;

        if (selectUIMove.y > 0)
            verticalMoveDir = 1;
        else if (selectUIMove.y < 0)
            verticalMoveDir = -1;
        
        if(horizontalMoveDir != 0)
            GameManager.Instance.HandlePlayerHorizontalInput_UIMode(horizontalMoveDir, playerIndex);
        if (verticalMoveDir != 0)
        {
            GameManager.Instance.HandlePlayerVerticalInput_UIMode(verticalMoveDir, playerIndex);
        }
    }
    

    public void SwitchActionMap(string mapName)
    {
        switch (mapName)
        {
            case "Player":
                playerInput.SwitchCurrentActionMap("Player");
                break;
            case "UI":
                playerInput.SwitchCurrentActionMap("UI");
                break;
            default:
                playerInput.SwitchCurrentActionMap("Player");
                break;
        }
    }
}