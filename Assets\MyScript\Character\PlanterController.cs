using System.Collections.ObjectModel;
using UnityEngine;
using Random = UnityEngine.Random;

public class PlanterController : OtherCharacterBase
{
    //randomly choose a empty block to land
    //randomly choose a tool to plant (only "trash" in chapter one)
    //can be attacked to die
    //highlight the block it is going
    
    //when planting, consider it is attached to the block

    //when in "idle air" and "idle ground", we calculate next target
    //when a new target is found, highlight it, then enter "reaching target"
    //when landed on the target, enter "planting"
    //when planting finished, enter "idle ground"

    private float idleDur = 2;
    private float planterSpeed = 3;
    private float plantDur = 3;

    private float stateTimer = 0;
    
    private enum PlanterState
    {
        IdleAir,
        ReachingTarget,
        Planting
    }

    private PlanterState currentState;
    
    private GameObject nextBlock;

    private Animator planterAnimator;
    private int plantStateBoolID = Animator.StringToHash("Plant");
    

    private GameObject stickableObjs;

    private Vector3 initPos;

    private ReadOnlyCollection<ChunkClass> chunkClassList;

    private BagTool nextTool;
    

    public override void InitCharacter(ToolID toolID)
    {
        base.InitCharacter(toolID);
        initPos = characterTransform.position;
        if (planterAnimator == null)
            planterAnimator = GetComponent<Animator>();
        planterAnimator.runtimeAnimatorController = GlobalParameters.Instance.ReturnPlanterController();
        if(stickableObjs == null)
            stickableObjs = GlobalParameters.Instance.stickableObjs;
        playerIndexThatChangedToolUnder = -1;
        nextTool = new BagTool(ToolID.Trash, ToolDirection.Original);
        chunkClassList = GlobalParameters.Instance.chunkPool.activePool;
        UpdateState(PlanterState.IdleAir);
    }
    
    public override void CharacterGotAttacked()
    {
        //die affect
        CharacterDie();
        
    }

    protected override void CharacterDie()
    {
        //clear highlight
        if (currentState == PlanterState.Planting || currentState == PlanterState.ReachingTarget)
        {
            HighlightTargetBlock(false);
        }
        
        base.CharacterDie();
    }

    public override void CloseCharacter()
    {
        base.CloseCharacter();
        gameObject.SetActive(false);
    }

    private void FixedUpdate()
    {
        PlanterBehavior();
        CloseOutOfScreenCharacter();
    }

    private void PlanterBehavior()
    {
        switch (currentState)
        {
            case PlanterState.IdleAir:
                IdleAction();
                break;
            case PlanterState.ReachingTarget:
                ReachingTargetAction();
                break;
            case PlanterState.Planting:
                PlantingAction();
                break;
        }
    }
    
    private void IdleAction()
    {
        //update idle countdown
        stateTimer += Time.fixedDeltaTime;
        
        //fly to init pos
        

        if (Vector3.Distance(characterTransform.position, initPos) > 0.3f)
        {
            Vector3 movement = (initPos - characterTransform.position).normalized * planterSpeed;
            characterTransform.position += movement * Time.fixedDeltaTime;
            
        }
        else
        {
            characterTransform.position = initPos;
            UpdateState(PlanterState.ReachingTarget);
        }
        
        if (stateTimer > idleDur)
        {
            UpdateState(PlanterState.ReachingTarget);
        }
    }

    private void ReachingTargetAction()
    {
        if (nextBlock != null)
        {
            Vector3 movement = (nextBlock.transform.position - characterTransform.position).normalized*planterSpeed;
            characterTransform.position += movement*Time.fixedDeltaTime;
            
            (int chunkIndex, int objIndex) = GlobalParameters.Instance.ReturnChunkStickableIndexByObj(nextBlock);

            int highlightBy = chunkClassList[chunkIndex].chunkChildList[objIndex].highlightBy;
            bool highlightByPlayer = highlightBy == (int)ToolID.Player;
            bool stickedNotByStave = chunkClassList[chunkIndex].chunkChildList[objIndex].sticked &&
                                     chunkClassList[chunkIndex].chunkChildList[objIndex].toolID !=
                                     ToolID.Stave;
            if(highlightByPlayer || stickedNotByStave)
                UpdateState(PlanterState.IdleAir);
            if (Vector3.Distance(characterTransform.position, nextBlock.transform.position) < 0.2f)
            {
                characterTransform.position = nextBlock.transform.position;
                UpdateState(PlanterState.Planting);
            }
        }
        
    }

    private void PlantingAction()
    {
        stateTimer += Time.fixedDeltaTime;
        characterTransform.position = nextBlock.transform.position;
        if (stateTimer > plantDur)
        {
            UpdateState(PlanterState.IdleAir);
        }
    }

    private void UpdateState(PlanterState state)
    {
        currentState = state;
        stateTimer = 0;
        planterAnimator.SetBool(plantStateBoolID, false);
        switch (state)
        {
            case PlanterState.IdleAir:
                InitAirIdle();
                break;
            case PlanterState.Planting:
                InitPlanting();
                break;
            case PlanterState.ReachingTarget:
                InitReachingTarget();
                break;
        }
    }

    private void InitAirIdle()
    {
        (int chunkIndex, int objIndex) = GlobalParameters.Instance.ReturnChunkStickableIndexByObj(nextBlock);
        if (chunkIndex != -1 && objIndex != -1)
        {
            if(chunkClassList[chunkIndex].chunkChildList[objIndex].highlightBy == (int)ToolID.Planter)
                chunkClassList[chunkIndex].RemoveHighLight(objIndex);
        }
    }
    
    private void InitPlanting()
    {
        (int chunkIndex, int objIndex) = GlobalParameters.Instance.ReturnChunkStickableIndexByObj(nextBlock);
        //play planting animation
        if (!chunkClassList[chunkIndex].PlanterStickTool(playerIndexThatChangedToolUnder,objIndex, nextTool))
        {
            UpdateState(PlanterState.IdleAir);
        }
        else
        {
            planterAnimator.SetBool(plantStateBoolID, true);
        }
            
    }

    private void InitReachingTarget()
    {
        //find and highlight next target
        FindTarget();
    }

    private void FindTarget()
    {
        int chunkCount = chunkClassList.Count;
        int newChunkIndex = Random.Range(0, chunkCount);
        if (newChunkIndex > chunkCount - 1)
            newChunkIndex = chunkCount - 1;

        if (chunkClassList[newChunkIndex].gameObject.activeSelf)
        {
            if (chunkClassList[newChunkIndex].GotStave(out int index) && nextTool.toolID == ToolID.Note)
            {
                int nextTargetObjIndex = index;
                nextBlock = chunkClassList[newChunkIndex].chunkChildList[nextTargetObjIndex].stickableObj;
                chunkClassList[newChunkIndex].AddHighLight(index, ColorEnum.PlanterHighLightBlock);
                return;
            }
            
            if (chunkClassList[newChunkIndex].GotEmptySpace())
            {
                int nextTargetObjIndex = chunkClassList[newChunkIndex].ReturnEmptyRandomIndex();
                if (nextTargetObjIndex >= 0) 
                {
                    nextBlock = chunkClassList[newChunkIndex].chunkChildList[nextTargetObjIndex].stickableObj;
                
                    //high light target with 2D sprite light
                    chunkClassList[newChunkIndex].AddHighLight(nextTargetObjIndex, ColorEnum.PlanterHighLightBlock);
                }

                return;
            }
        }
        
        UpdateState(PlanterState.IdleAir);
    }

    private void HighlightTargetBlock(bool highlight)
    {
        (int chunkIndex, int objIndex) = GlobalParameters.Instance.ReturnChunkStickableIndexByObj(nextBlock);
        if(highlight)
            chunkClassList[chunkIndex].AddHighLight(objIndex, ColorEnum.PlanterHighLightBlock);
        else
        {
            chunkClassList[chunkIndex].RemoveHighLight(objIndex);
        }
            
    }

    private int playerIndexThatChangedToolUnder = -1;
    public void SetNextTool(BagTool tool, int playerIndex)
    {
        nextTool = tool;
        playerIndexThatChangedToolUnder = playerIndex;
    }
}
