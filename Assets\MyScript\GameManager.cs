using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using UnityEngine;
using TMPro;
using UnityEngine.InputSystem;
using UnityEngine.InputSystem.Users;

public class GameManager : MonoBehaviour
{
    [SerializeField] private InputAction joinAction;

    [SerializeField] private InputAction firstPlayerControllerAction;


    [HideInInspector] public Progress presentProgress;
    public GameObject playerObjs;
    [SerializeField] private MyColorTemplate myColorTemplate;

    #region Map Result

    public float levelTime;
    private LevelResult currentLevelResult;
    private List<LevelResult> collectionResultList = new List<LevelResult>();

    #endregion


    [SerializeField] private GameObject playerPrefab;

    [SerializeField] private InputInsctruction inputInstruction;

    [SerializeField] private VirtualMouseController virtualMouseController;

    [SerializeField] private CutSceneController cutSceneController;

    public class PlayerClass
    {
        public PlayerClass(GameObject obj, int index)
        {
            playerObj = obj;
            playerInput = obj.GetComponent<PlayerInput>();
            playerAction = obj.GetComponent<PlayerAction>();
            playerAction.SetPlayerInput(index);
            playerController = obj.GetComponent<PlayerController>();
            playerController.SetPlayer(index);
            playerAudioSource = obj.GetComponent<AudioSource>();
        }

        public GameObject playerObj;
        public ColorEnum playerColor;
        public PlayerInput playerInput;
        public PlayerAction playerAction;
        public PlayerController playerController;
        public AudioSource playerAudioSource;
    }

    public List<PlayerClass> playerList = new List<PlayerClass>();

    private int activePlayerCount = 1;

    private struct MyJoinedInputControl
    {
        public MyJoinedInputControl(InputDevice device, string name)
        {
            iDevice = device;
            controlName = name;
        }

        public InputDevice iDevice;
        public string controlName;

        public static bool operator ==(MyJoinedInputControl myJoinedInputControl1,
            MyJoinedInputControl myJoinedInputControl2)
        {
            return myJoinedInputControl1.controlName == myJoinedInputControl2.controlName &&
                   myJoinedInputControl1.iDevice == myJoinedInputControl2.iDevice;
        }

        public static bool operator !=(MyJoinedInputControl myJoinedInputControl1,
            MyJoinedInputControl myJoinedInputControl2)
        {
            return !(myJoinedInputControl1 == myJoinedInputControl2);
        }

        public override bool Equals(object obj)
        {
            if (obj is MyJoinedInputControl)
            {
                return this == (MyJoinedInputControl)obj;
            }

            return false;
        }

        public override int GetHashCode()
        {
            return HashCode.Combine(iDevice, controlName);
        }
    }

    private List<MyJoinedInputControl> myJoinedInputControlList = new List<MyJoinedInputControl>();

    private Camera mainCamera;

    public delegate void StateChangeAction();

    [Serializable]
    public class StateClass
    {
        public StateEnum ThisState;
        public StateChangeAction stateAction;
        public GameObject UIObj;
        public InteractionUI interactionUI;
    }

    [SerializeField] public List<StateClass> StateList;


    private bool gotPaused = false;

    private int homeIndex = 0;
    private int presentStateIndex = 0;
    private int previousStateIndex = 0;

    #region Supplementary UI

    private GameObject GamePlayPauseUI;
    private GameObject EditorPauseUI;
    private TMP_Text editorPauseWarning;

    private GameObject collectionEditorCollectionView;

    private GameObject chooseEditorMapView;

    private GameObject chooseMapPopupUI;

    private GameObject competitiveResultUI;
    private GameObject cooperativeResultUI;

    private TMP_Text gamePauseMapNameText;

    private GameObject homeButtonObjs;
    private WaitFirstInputController waitFirstInputController;

    #endregion


    private CollectionClass currentCollection;
    private int selectedMapIndex = 0;
    private int formerSelectedMapIndex = 0;

    private InteractionUI currentInteractionUI;

    private ChoosePlayerDataAssigner choosePlayerDataAssigner;

    private SlotDataAssigner slotDataAssigner;

    private LevelDataAssigner levelDataAssigner;

    private ColorManager colorManager;

    private bool story = false;

    public ControlType firstPlayerControlType { private set; get; }

    public SpecificController firstPlayerSpecificController { private set; get; }

    private bool gamepadCursorActive;

    private bool cannotPause;

    private bool firstPlayerControllerSelected;

    public static GameManager Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != null)
        {
            Destroy(this);
            return;
        }

        Instance = this;

        DontDestroyOnLoad(Instance);
        CheckState();
        InitGame();
    }

    // Update is called once per frame
    void Update()
    {
        if (!gotPaused)
            levelTime += Time.deltaTime;
    }
    
    #region Init

    private void CheckState()
    {
        for (int i = 0; i < StateList.Count; i++)
        {
            AssignObj(i);
            AssignAction(i);
            if ((StateEnum)i != StateList[i].ThisState)
            {
                Debug.Log("State " + StateList[i].ThisState + "is not in right position");
            }
        }
    }

    private void AssignObj(int index)
    {
        switch (StateList[index].ThisState)
        {
            case StateEnum.Home:
                homeButtonObjs = StateList[index].UIObj.transform.Find("ButtonList").gameObject;
                waitFirstInputController = StateList[index].UIObj.transform.Find("WaitFirstInputController").GetComponent<WaitFirstInputController>();
                return;
            case StateEnum.GamePlayPause:
                AssignGamePlayPauseUI(index);
                return;
            case StateEnum.MapEditorPause:
                EditorPauseUI = StateList[index].UIObj.transform.Find("PauseUI").gameObject;
                EditorPauseUI.SetActive(false);
                return;
            case StateEnum.CollectionEditor:
                chooseMapPopupUI = StateList[index].UIObj.transform.Find("ChooseMapPopupUI").gameObject;
                collectionEditorCollectionView = StateList[index].UIObj.transform.Find("Collections").gameObject;
                return;
            case StateEnum.ChooseEditorMap:
                chooseEditorMapView = StateList[index].UIObj.transform.Find("Maps").gameObject;
                return;
            case StateEnum.CollectionResult:
                competitiveResultUI = StateList[index].UIObj.transform.Find("CompetitiveResult").gameObject;
                cooperativeResultUI = StateList[index].UIObj.transform.Find("CooperativeResult").gameObject;
                return;
            case StateEnum.ChoosePlayer:
                choosePlayerDataAssigner = StateList[index].UIObj.GetComponent<ChoosePlayerDataAssigner>();
                return;
            case StateEnum.ChooseSlot:
                slotDataAssigner = StateList[index].UIObj.GetComponent<SlotDataAssigner>();
                return;
            case StateEnum.ChooseStoryLevel:
                levelDataAssigner = StateList[index].UIObj.GetComponent<LevelDataAssigner>();
                return;
        }
    }

    private void AssignAction(int index)
    {
        switch (StateList[index].ThisState)
        {
            case StateEnum.Home:
                StateList[index].stateAction += GotoHomeAction;
                return;
            case StateEnum.ChooseStoryLevel:
                StateList[index].stateAction += GotoChooseLevelAction;
                return;
            case StateEnum.ChooseSlot:
                StateList[index].stateAction += GotoChooseSlotAction;
                return;
            case StateEnum.GamePlay:
                StateList[index].stateAction += GotoGamePlayAction;
                return;
            case StateEnum.GamePlayPause:
                StateList[index].stateAction += GotoGamePlayPauseAction;
                return;
            case StateEnum.ChooseEditorMap:
                StateList[index].stateAction += GotoChooseEditorMapAction;
                return;
            case StateEnum.MapEditor:
                StateList[index].stateAction += GotoMapEditorAction;
                return;
            case StateEnum.MapEditorPause:
                StateList[index].stateAction += GotoMapEditorPauseAction;
                return;
            case StateEnum.Dialogue:
                StateList[index].stateAction += GotoDialogueAction;
                return;
            case StateEnum.ChoosePlayer:
                StateList[index].stateAction += GotoChoosePlayerAction;
                return;
            case StateEnum.ChooseCollection:
                StateList[index].stateAction += GotoChooseCollectionAction;
                return;
            case StateEnum.CollectionResult:
                StateList[index].stateAction += GotoCollectionResultAction;
                return;
            case StateEnum.Editor:
                StateList[index].stateAction += GotoEditorAction;
                return;
            case StateEnum.CollectionEditor:
                StateList[index].stateAction += GotoCollectionEditor;
                return;
            case StateEnum.ChooseMapPopup:
                StateList[index].stateAction += GotoChooseMapPopup;
                return;
            case StateEnum.ChooseChapter:
                StateList[index].stateAction += GotoChooseChapter;
                return;
            case StateEnum.Option:
                StateList[index].stateAction += GotoOption;
                return;
            case StateEnum.Leaderboard:
                StateList[index].stateAction += GotoLeaderboard;
                return;
            case StateEnum.Play:
                StateList[index].stateAction += GotoPlay;
                return;
        }
    }

    private void AssignGamePlayPauseUI(int index)
    {
        GamePlayPauseUI = StateList[index].UIObj.transform.Find("PauseUI").gameObject;
        gamePauseMapNameText = GamePlayPauseUI.transform.Find("GamePauseMapNameText").GetComponent<TMP_Text>();
        GamePlayPauseUI.SetActive(false);
    }

    private void InitGame()
    {
        Time.timeScale = 0;
        editorChooseLevel = false;
        selectedMapIndex = 0;
        formerSelectedMapIndex = -1;
        gamepadCursorActive = false;
        firstPlayerControllerSelected = true;
        mainCamera = Camera.main;
        colorManager = new ColorManager(myColorTemplate);
        InitUI();
        InitPlayer();
        ToggleFirstPlayerPickedState();
        ShowMapInMainCamera(false);
        currentInteractionUI = StateList[(int)StateEnum.Home].interactionUI;
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();

        ActivateGamePadCursor();
    }

    private async void ToggleFirstPlayerPickedState()
    {
        bool newSelectedState = !firstPlayerControllerSelected;
        
        homeButtonObjs.SetActive(newSelectedState);
        
        if (newSelectedState)
        {
            firstPlayerControllerAction.Disable();
        }
        else
        {
            firstPlayerControllerAction.Enable();
        }
        
        await waitFirstInputController.BeginWaitFirstInputAnim(newSelectedState);
        
        firstPlayerControllerSelected = newSelectedState;
    }

    private void InitPlayer()
    {
        playerList.Clear();
        for (int i = 0; i < 8; i++)
        {
            playerList.Add(new PlayerClass(Instantiate(playerPrefab, playerObjs.transform), i));
            playerList[i].playerObj.SetActive(false);
        }

        firstPlayerControllerAction.performed += FirstPlayerJoined;


        joinAction.started += PresentHold;
        joinAction.performed += OtherPlayerJoined;
        joinAction.canceled += CancelHold;
    }

    private void FirstPlayerJoined(InputAction.CallbackContext ctx)
    {
        if (presentStateIndex == (int)StateEnum.Home && !firstPlayerControllerSelected)
        {
            InputDevice pDevice = ctx.control.device;
            //first player hit any key to join, therefore control name should be adjusted
            string controlName = pDevice is Keyboard ? "space" : "rightStickPress";
            MyJoinedInputControl myJoinedInputControl = new MyJoinedInputControl(pDevice, controlName);
            playerList[0].playerObj.SetActive(true);
            activePlayerCount = 1;
            MyPairDevice(playerList[0].playerInput, pDevice);
            if (myJoinedInputControlList.Count > 0)
                myJoinedInputControlList.RemoveAt(0);
            myJoinedInputControlList.Add(myJoinedInputControl);

            //only in home page, we can update the first player control type
            SetFirstPlayerControlType(pDevice);
            SetFirstPlayerSpecificController(pDevice);
            
            ToggleFirstPlayerPickedState();
        }
    }

    private void OtherPlayerJoined(InputAction.CallbackContext ctx)
    {
        InputDevice pDevice = ctx.control.device;
        MyJoinedInputControl myJoinedInputControl = new MyJoinedInputControl(pDevice, ctx.control.name);
        if (myJoinedInputControlList.All(i => i != myJoinedInputControl))
        {
            if (presentStateIndex == (int)StateEnum.ChoosePlayer)
            {
                //pair this new device the player input
                if (activePlayerCount < playerList.Count)
                {
                    int newIndex = activePlayerCount;
                    bool keyBoard2 = pDevice is Keyboard && ctx.control.name == "rightShift";
                    playerList[newIndex].playerObj.SetActive(true);
                    playerList[newIndex].playerAction.SetPlayerInput(newIndex, keyBoard2);
                    MyPairDevice(playerList[newIndex].playerInput, pDevice);
                    choosePlayerDataAssigner.AddPlayer(newIndex);
                    myJoinedInputControlList.Add(myJoinedInputControl);
                    activePlayerCount++;
                    inputInstruction.UpdateChoosePlayerUI();
                }
            }
        }
    }

    private void PresentHold(InputAction.CallbackContext ctx)
    {
        //start a count down and present UI
        if (presentStateIndex == (int)StateEnum.ChoosePlayer)
        {
            MyJoinedInputControl myJoinedInputControl = new MyJoinedInputControl(ctx.control.device, ctx.control.name);
            SpecificController currentSC = ReturnSpecificController(ctx.control.device, ctx.control.name);
            if (GetNextJoinPlayerSpecificController() == currentSC &&
                myJoinedInputControlList.All(i => i != myJoinedInputControl))
            {
                inputInstruction.BeginHoldPresenterAnim();
            }
        }
    }

    private void CancelHold(InputAction.CallbackContext ctx)
    {
        inputInstruction.CancelHoldPresenterAnim();
    }

    private void MyPairDevice(PlayerInput pInput, InputDevice iDevice)
    {
        pInput.user.UnpairDevices();

        if (iDevice is Gamepad)
        {
            pInput.SwitchCurrentControlScheme("Gamepad");
        }
        else if (iDevice is Keyboard)
        {
            pInput.SwitchCurrentControlScheme("Keyboard");
        }


        InputUser.PerformPairingWithDevice(iDevice,
            pInput.user);
    }

    private void SetFirstPlayerControlType(InputDevice iDevice)
    {
        if (iDevice is Keyboard)
        {
            firstPlayerControlType = ControlType.Keyboard;
        }
        else if (iDevice is Gamepad)
        {
            firstPlayerControlType = ControlType.Gamepad;
        }
    }

    private void SetFirstPlayerSpecificController(InputDevice iDevice)
    {
        firstPlayerSpecificController = SpecificController.None;
        if (iDevice is Keyboard)
        {
            firstPlayerSpecificController = SpecificController.Keyboard1;
        }
        else if (iDevice is UnityEngine.InputSystem.XInput.XInputController)
        {
            firstPlayerSpecificController = SpecificController.Xbox;
        }
        else if (iDevice is UnityEngine.InputSystem.DualShock.DualShockGamepad)
        {
            firstPlayerSpecificController = SpecificController.PS;
        }
    }

    private void InitUI()
    {
        int i = 0;
        foreach (var item in StateList)
        {
            item.UIObj.SetActive(false);

            i++;
        }

        ShowUI(homeIndex);
    }

    #endregion

    #region Change state

    //根据玩家的输入跳转到对应的游戏状态
    //never call a StateButtonAction within state update functions
    public void StateButtonAction(int actionIndex)
    {
        presentStateIndex = actionIndex;
        if (presentStateIndex != previousStateIndex || presentStateIndex == 3)
        {
            UpdateState();
            previousStateIndex = presentStateIndex;
        }
    }

    //更新状态
    private void UpdateState()
    {
        if (StateList[presentStateIndex].UIObj != StateList[previousStateIndex].UIObj)
        {
            if (presentStateIndex != (int)StateEnum.Dialogue)
            {
                HideUI(previousStateIndex);
            }

            ShowUI(presentStateIndex);
        }

        //set up interaction UI before invoking state action
        currentInteractionUI = StateList[presentStateIndex].interactionUI;
        //invoke state action
        StateList[presentStateIndex].stateAction?.Invoke();

        ActionAfterEveryStateChange();
    }

    private void ActionAfterEveryStateChange()
    {
        UpdateOtherPlayerAddable(presentStateIndex == (int)StateEnum.ChoosePlayer);

        inputInstruction.ShowInputInstruction((StateEnum)presentStateIndex, firstPlayerSpecificController);

        DecideInputMap();

        ActivateGamePadCursor();

        ClearMapWhenExitMap();

        DecideGCCollect();

        AudioManager.Instance.VolumeSetupInDialogue(presentStateIndex == (int)StateEnum.Dialogue ||
                                                    presentStateIndex == (int)StateEnum.GamePlayPause);
    }


    //隐藏对应UI
    private void HideUI(int _index)
    {
        StateList[_index].UIObj.SetActive(false);
    }

    //显示对应的UI
    private void ShowUI(int _index)
    {
        StateList[_index].UIObj.SetActive(true);
    }

    private void DecideInputMap()
    {
        if (presentStateIndex == (int)StateEnum.GamePlay)
        {
            SetInputMap("Player");
        }
        else
        {
            SetInputMap("UI");
        }
    }

    private void ClearMapWhenExitMap()
    {
        bool formerStateCondition =
            previousStateIndex == (int)StateEnum.GamePlay || previousStateIndex == (int)StateEnum.GamePlayPause;

        bool currentStateCondition = presentStateIndex != (int)StateEnum.GamePlay &&
                                     presentStateIndex != (int)StateEnum.GamePlayPause &&
                                     presentStateIndex != (int)StateEnum.Dialogue;

        if (formerStateCondition && currentStateCondition)
            GlobalParameters.Instance.ClearFormerMap();
    }

    private void DecideGCCollect()
    {
        bool collect = presentStateIndex == (int)StateEnum.ChooseEditorMap ||
                       presentStateIndex == (int)StateEnum.CollectionEditor;

        if (collect)
            GC.Collect();
    }

    #endregion

    #region State Actions

    private void GotoHomeAction()
    {
        //if previous state is "Choose Player", then 
        //only P1 should remain
        if (previousStateIndex == (int)StateEnum.ChoosePlayer)
            ClearPlayerExceptOne();

        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
    }

    private void GotoChooseSlotAction()
    {
        story = true;

        slotDataAssigner.ShowSlot();

        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
    }

    private void GotoChooseLevelAction()
    {
        Time.timeScale = 0;

        playerList[0].playerColor = ColorEnum.PlayerColor1;
        playerList[0].playerController.SetPlayerColor(colorManager.PlayerColor1);

        if (previousStateIndex == (int)StateEnum.GamePlayPause || previousStateIndex == (int)StateEnum.CollectionResult)
        {
            AudioManager.Instance.PlayBGM(BGMType.Menu);
        }

        //when in choose level, hide level objs
        ShowMapInMainCamera(false);

        selectedMapIndex = 0;
        formerSelectedMapIndex = -1;


        //load local level data
        levelDataAssigner.ShowLevel(currentCollection);

        //always activate interaction UI after assigner
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();

        ChooseLevelStateRequestLevelDialogue();
    }

    private void GotoGamePlayAction()
    {
        //set parameters
        gotPaused = false;

        //set UI
        GamePlayPauseUI.SetActive(false);

        ShowMapInMainCamera(true);
        
        StartPlayerComponents();

        BagManager.Instance.BagItemColorTint(false);

        if (previousStateIndex == (int)StateEnum.ChooseStoryLevel ||
            previousStateIndex == (int)StateEnum.ChooseCollection)
        {
            int currentBackgroundArtIndex = PlayerPrefs.GetInt(GameConst.backgroundPrefKey);
            BGMType type = BGMType.Menu;
            switch (currentBackgroundArtIndex)
            {
                case 0:
                    type = BGMType.Chapter1;
                    break;
                case 1:
                    type = BGMType.Chapter2;
                    break;
                case 2:
                    type = BGMType.Chapter3;
                    break;
            }

            AudioManager.Instance.PlayBGM(type);
        }

        //do not need to start game if last state is "Pause" and "Dialogue"
        bool previousStateCondition = StateList[previousStateIndex].ThisState != StateEnum.GamePlayPause &&
                                      StateList[previousStateIndex].ThisState != StateEnum.Dialogue;

        //if map changed, must start game
        bool mustNewGameSetup = formerSelectedMapIndex != selectedMapIndex;

        bool mustSetTimeScale = previousStateIndex == (int)StateEnum.GamePlayPause;

        if (previousStateCondition || mustNewGameSetup)
        {
            NewGameSetup();
        }
        else if (mustSetTimeScale)
        {
            Time.timeScale = 1;
        }
    }

    private void GotoGamePlayPauseAction()
    {
        Time.timeScale = 0;

        GamePlayPauseUI.SetActive(true);

        gamePauseMapNameText.text = $"Level {selectedMapIndex + 1}";

        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();

        gotPaused = true;
    }

    private void GotoChooseEditorMapAction()
    {
        MapEditor.Instance.ActivateEditor(false);

        //this will make sure the player and level objs does not show in main camera
        ShowMapInMainCamera(false);

        chooseEditorMapView.GetComponent<ChooseEditorMapDataAssigner>().ShowMap(null);

        //when in choose level, no longer in edit mode
        GlobalParameters.Instance.EditMode(false);

        //set edit mode
        editorChooseLevel = true;
    }

    private void GotoMapEditorAction()
    {
        EditorPauseUI.SetActive(false);

        BagManager.Instance.BagItemColorTint(true);

        //main camera can see map objs
        ShowMapInMainCamera(true);

        //don't show player obj
        ShowPlayerInMainCamera(false);

        switch (StateList[previousStateIndex].ThisState)
        {
            case StateEnum.ChooseEditorMap:
                StartEditor();
                return;
        }
    }

    private void GotoMapEditorPauseAction()
    {
        if (EditorPauseUI == null)
        {
            EditorPauseUI = StateList[presentStateIndex].UIObj.transform.Find("PauseUI").gameObject;
        }

        EditorPauseUI.SetActive(true);
    }

    private void GotoDialogueAction()
    {
        Time.timeScale = 0;
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
        NextDialogueButtonAction();
    }

    private void GotoChoosePlayerAction()
    {
        story = false;
        //init player list
        //int player list view

        //data assigner
        choosePlayerDataAssigner.ShowPlayer();

        ClearPlayerExceptOne();

        //activate interaction UI after data assigner
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
    }

    private void GotoChooseCollectionAction()
    {
        Time.timeScale = 0;

        selectedMapIndex = 0;
        formerSelectedMapIndex = -1;

        if (previousStateIndex == (int)StateEnum.GamePlayPause || previousStateIndex == (int)StateEnum.CollectionResult)
        {
            AudioManager.Instance.PlayBGM(BGMType.Menu);
        }

        StateList[presentStateIndex].UIObj.GetComponent<ChooseCollectionDataAssigner>()
            .ShowCollection(new MapFilterCondition(activePlayerCount,activePlayerCount,-1));

        //clear collection result here
        collectionResultList.Clear();


        //when we get back from the game
        //hide the player and map objs
        ShowMapInMainCamera(false);
    }

    private void GotoCollectionResultAction()
    {
        Time.timeScale = 0;

        //decide the type of result page: "cooperative" or "competitive"
        //show data accordingly
        ShowMapInMainCamera(false);
        switch (currentCollection.multiplayerMode)
        {
            case MultiplayerMode.Competitive:
                competitiveResultUI.SetActive(true);
                cooperativeResultUI.SetActive(false);
                competitiveResultUI.GetComponent<CompetitiveResultAssigner>()
                    .ShowResult(collectionResultList, currentCollection);
                break;
            case MultiplayerMode.Cooperative:
                competitiveResultUI.SetActive(false);
                cooperativeResultUI.SetActive(true);
                cooperativeResultUI.GetComponent<CooperativeResultAssigner>()
                    .ShowResult(collectionResultList, currentCollection);
                break;
        }
    }

    private void GotoEditorAction()
    {
        //hide the map objs
        //reset the main camera
        ShowMapInMainCamera(false);
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();

        //save all collection data when coming back from collection editor
        if (previousStateIndex == (int)StateEnum.CollectionEditor)
            GlobalParameters.Instance.SaveCollectionList();

        //save all map preview data when coming back from choose map editor
        if (previousStateIndex == (int)StateEnum.ChooseEditorMap)
            GlobalParameters.Instance.SaveMapPreviewList();
    }

    private void GotoCollectionEditor()
    {
        //init collection list
        //init map list
        //init inspector
        chooseMapPopupUI.SetActive(false);

        ShowMapInMainCamera(false);

        //don't renew collection editor view, when return from "ChooseMapPopup"
        if (previousStateIndex != (int)StateEnum.ChooseMapPopup)
            collectionEditorCollectionView.GetComponent<CollectionEditorDataAssigner>().ShowCollection();
    }

    private void GotoChooseMapPopup()
    {
        //show UI
        //init map list
        //init inspector
        chooseMapPopupUI.SetActive(true);
    }

    private void GotoChooseChapter()
    {
        //data assigner
        StateList[presentStateIndex].UIObj.GetComponent<ChapterDataAssigner>().ShowChapter(presentProgress);

        //activate interaction UI after data assigner
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
    }

    private void GotoOption()
    {
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
    }

    private void GotoLeaderboard()
    {
        MapFilterCondition condition = new MapFilterCondition(1, 1 ,1);
        StateList[presentStateIndex].UIObj.GetComponentInChildren<LeaderboardCollectionAssigner>()
            .ShowCollection(condition);
    }

    private void GotoPlay()
    {
        editorChooseLevel = false;
        if (currentInteractionUI != null)
            currentInteractionUI.ActivateInteractionUI();
    }

    #endregion

    #region Supplementary state change action

    private void NewGameSetup()
    {
        //start the game from the beginning
        //init dialogue name
        presentDialogueName = "";
        inDialogueArea = false;

        //init time
        levelTime = 0;

        //init this level result
        currentLevelResult = new LevelResult();

        //before generating new texture, unload older ones
        Resources.UnloadUnusedAssets();

        BagManager.Instance.BagUI.transform.SetParent(StateList[(int)StateEnum.GamePlay].UIObj.transform);

        if (presentProgress.levelResultList.Count <= ChapterLevelIndexToProgressCount(selectedMapIndex) &&
            presentProgress.levelResultList.Count < 96 && story)
        {
            presentProgress.levelResultList.Add(new LevelResult());  
        }
            

        presentProgress.lastPlayDate = DateTime.Now.Date.ToString("MM/dd/yyyy HH:mm");

        formerSelectedMapIndex = selectedMapIndex;
    }

    private void StartPlayerComponents()
    {
        for (int i = 0; i < activePlayerCount; i++)
        {
            playerList[i].playerController.PlayerAlive();
        }
    }

    private void StartEditor()
    {
        //update selected level id before this
        Time.timeScale = 0;

        GlobalParameters.Instance.EditMode(true);
        GlobalParameters.Instance.ResetLevel();

        MapEditor.Instance.ActivateEditor(true);

        BagManager.Instance.BagUI.transform.SetParent(StateList[(int)StateEnum.MapEditor].UIObj.transform);
    }

    public void QuitGame()
    {
        SaveSystem.SetProgress(presentProgress.slot);
        Application.Quit();
    }

    private void CollectionFinishedAction(int nextLevelIndex)
    {
        if (story)
        {
            if (nextLevelIndex >= currentCollection.mapIDList.Count)
                ChapterFinishedAction();
            StateButtonAction((int)StateEnum.ChooseStoryLevel);
        }
        else
        {
            StateButtonAction((int)StateEnum.CollectionResult);
        }
    }

    private void SetSuccessProgress()
    {
        int selectedMapResultIndex = CurrentCollectionLevelIndexToProgressCount(selectedMapIndex);
        if (selectedMapResultIndex >= 0 && selectedMapResultIndex < presentProgress.levelResultList.Count && story)
        {
            presentProgress.lastPlayDate = DateTime.Now.Date.ToString("d");
            presentProgress.levelResultList[selectedMapResultIndex].hasPassed = true;
            presentProgress.levelResultList[selectedMapResultIndex].timeDur = levelTime;
            presentProgress.levelResultList[selectedMapResultIndex].operationCount = currentLevelResult.operationCount;
            SaveSystem.SetProgress(presentProgress.slot);
        }
    }

    private void UpdateChapterLeaderboard()
    {
        int hasPassedCount = 0;

        foreach (var result in presentProgress.levelResultList)
        {
            if (result.hasPassed)
                hasPassedCount++;
        }

        int updateChapterCount = hasPassedCount / 32;

        for (int i = 0; i < updateChapterCount; i++)
        {
            SteamIntegration.Instance.SubmitCollectionTime(
                GlobalMethod.ReturnChapterLbNameByIndexAndType(i, LeaderboardValueType.Time), ReturnChapterTime(i));
            SteamIntegration.Instance.SubmitCollectionOperation(
                GlobalMethod.ReturnChapterLbNameByIndexAndType(i, LeaderboardValueType.Operation),
                ReturnChapterOperation(i));
        }

        if (updateChapterCount == 3)
        {
            SteamIntegration.Instance.SubmitCollectionTime($"Story_Time",
                presentProgress.levelResultList.Sum(i => i.timeDur));
            SteamIntegration.Instance.SubmitCollectionOperation($"Story_Operation",
                presentProgress.levelResultList.Sum(i => i.operationCount));
        }
    }

    private void ShowMapInMainCamera(bool show)
    {
        if (show)
        {
            mainCamera.cullingMask = -1;
        }
        else
        {
            mainCamera.cullingMask = (1 << LayerMask.NameToLayer("Default")) | (1 << LayerMask.NameToLayer("UI")) |
                                     (1 << LayerMask.NameToLayer("Background"));
        }
    }

    private void ShowPlayerInMainCamera(bool show)
    {
        if (show)
        {
            mainCamera.cullingMask |= 1 << LayerMask.NameToLayer("Player");
        }
        else
        {
            mainCamera.cullingMask &= ~(1 << LayerMask.NameToLayer("Player"));
        }
    }

    private void ChooseLevelStateRequestLevelDialogue()
    {
        int level = ShouldPlayLevel();

        //dialogue should be called after level 31 finish
        if (currentCollection.chapterIndex == presentProgress.chapter && level == 31)
            return;

        string checkLevelName = ReturnLevelName(level);
        bool gotLevelDialogue = DialogueManager.Instance.GotLevelNameDialogue(checkLevelName);
        if (gotLevelDialogue)
        {
            presentDialogueName = DialogueManager.Instance.ReturnLevelDialogueName(checkLevelName);
            //check if the level dialogue has been played before
            if (!presentProgress.dialogueProgress.Contains(presentDialogueName))
            {
                if (DialogueManager.Instance.PrepareDialogue(presentDialogueName))
                {
                    StateButtonAction((int)StateEnum.Dialogue);
                    levelDataAssigner.DialogueShowLevel();
                }
            }
        }
    }

    private void UpdateOtherPlayerAddable(bool addable)
    {
        if (addable)
        {
            joinAction.Enable();
        }
        else
        {
            joinAction.Disable();
        }
    }

    private void ChapterFinishedAction()
    {
        //increment chapter
        if (presentProgress.chapter == currentCollection.chapterIndex)
        {
            if (SteamIntegration.Instance)
            {
                SteamIntegration.Instance.UnlockAchievement(presentProgress.chapter.ToString());
            }

            if (presentProgress.chapter < 2)
            {
                presentProgress.chapter++;
                SaveSystem.SetProgress(presentProgress.slot);
            }
        }
    }

    #endregion

    #region API

    //die and restart
    public void RestartButtonAction()
    {
        CutSceneRequest req = new CutSceneRequest()
        {
            hasCurtain = true,
            hasCountdown = false,
            showWinner = currentCollection.multiplayerMode == MultiplayerMode.Competitive &&
                         currentCollection.playerCount > 1,
            nextMapID = currentCollection.mapIDList[selectedMapIndex],
            targetObject = null,
            winnerPlayerIndex = -1,
            activePlayerCount = 1,
            playerNextPositions = null,
            time = -1,
            operation = -1,
            timeNewRecord = false,
            operationNewRecord = false
        };

        StartCoroutine(cutSceneController.RequestCutScene(req));
    }

    public void PickLevelStartAction()
    {
        int mapID = currentCollection.mapIDList[selectedMapIndex];
        CutSceneRequest req = new CutSceneRequest()
        {
            hasCurtain = true,
            hasCountdown = GlobalParameters.Instance.ReturnMapPreviewByID(mapID).hasCountdown,
            showWinner = currentCollection.multiplayerMode == MultiplayerMode.Competitive &&
                         currentCollection.playerCount > 1,
            moveAllPlayers = currentCollection.multiplayerMode == MultiplayerMode.Cooperative &&
                             currentCollection.playerCount > 1,
            nextMapID = currentCollection.mapIDList[selectedMapIndex],
            targetObject = null,
            winnerPlayerIndex = -1,
            activePlayerCount = 1,
            playerNextPositions = null,
            time = -1,
            operation = -1,
            timeNewRecord = false,
            operationNewRecord = false
        };

        StartCoroutine(cutSceneController.RequestCutScene(req));
    }

    public float ReturnLevelResultTime(int levelIndex)
    {
        int resultListIndex = CurrentCollectionLevelIndexToProgressCount(levelIndex);
        if (resultListIndex >= presentProgress.levelResultList.Count)
            return 0;
        return presentProgress.levelResultList[resultListIndex].timeDur;
    }

    public int ReturnLevelResultOperation(int levelIndex)
    {
        int resultListIndex = CurrentCollectionLevelIndexToProgressCount(levelIndex);
        if (resultListIndex >= presentProgress.levelResultList.Count)
            return 0;
        return presentProgress.levelResultList[resultListIndex].operationCount;
    }

    public void EscapeFunction()
    {
        switch ((StateEnum)presentStateIndex)
        {
            case StateEnum.Home:
                AnewFirstPlayerController();
                break;
            case StateEnum.GamePlay:
                if (cutSceneController.cutSceneFinished)
                    StateButtonAction((int)StateEnum.GamePlayPause);
                break;
            case StateEnum.MapEditor:
                StateButtonAction((int)StateEnum.MapEditorPause);
                break;
            case StateEnum.Option:
                StateButtonAction((int)StateEnum.Home);
                break;
            case StateEnum.ChooseStoryLevel:
                StateButtonAction((int)StateEnum.ChooseChapter);
                break;
            case StateEnum.ChooseSlot:
                StateButtonAction((int)StateEnum.Play);
                break;
            case StateEnum.ChooseChapter:
                StateButtonAction((int)StateEnum.ChooseSlot);
                break;
            case StateEnum.Dialogue:
                if (DialogueManager.Instance.GotLevelDialogueName(presentDialogueName))
                {
                    StateButtonAction((int)StateEnum.ChooseSlot);
                    HideUI((int)StateEnum.ChooseStoryLevel);
                }
                else
                {
                    StateButtonAction((int)StateEnum.GamePlayPause);
                }

                break;
            case StateEnum.ChooseEditorMap:
                StateButtonAction((int)StateEnum.Editor);
                break;
            case StateEnum.GamePlayPause:
                StateButtonAction((int)StateEnum.GamePlay);
                break;
            case StateEnum.MapEditorPause:
                StateButtonAction((int)StateEnum.MapEditor);
                break;
            case StateEnum.ChoosePlayer:
                StateButtonAction((int)StateEnum.Play);
                break;
            case StateEnum.ChooseCollection:
                StateButtonAction((int)StateEnum.ChoosePlayer);
                break;
            case StateEnum.CollectionResult:
                StateButtonAction((int)StateEnum.ChooseCollection);
                break;
            case StateEnum.Editor:
                StateButtonAction((int)StateEnum.Play);
                break;
            case StateEnum.CollectionEditor:
                StateButtonAction((int)StateEnum.Editor);
                break;
            case StateEnum.ChooseMapPopup:
                StateButtonAction((int)StateEnum.CollectionEditor);
                break;
            case StateEnum.Leaderboard:
                StateButtonAction((int)StateEnum.Play);
                break;
            case StateEnum.Credits:
                StateButtonAction((int)StateEnum.Home);
                break;
            case StateEnum.Play:
                StateButtonAction((int)StateEnum.Home);
                break;
        }
    }

    public bool IfGamePaused()
    {
        return gotPaused;
    }

    public void IncrementPlayerOperationCount()
    {
        currentLevelResult.operationCount++;
    }

    public void SetResult(int playerIndex, bool success)
    {
        if (success)
        {
            if (!story)
            {
                //save to collection result for result view
                SaveCollectionResult(playerIndex);
            }
            else
            {
                //save the result to progress
                SetSuccessProgress();

                //update chapter result that meets condition
                UpdateChapterLeaderboard();
            }
        }
    }

    public bool SelectLevel(int index)
    {
        if (index >= 0 && index <= ShouldPlayLevel())
        {
            selectedMapIndex = index;
            return true;
        }

        return false;
    }

    public int ProgressCountToChooseLevelFillAmount()
    {
        int levelIndex = 0;
        if (currentCollection.chapterIndex < presentProgress.chapter)
        {
            levelIndex = 32;
        }
        else
        {
            int count = 0;
            foreach (var result in presentProgress.levelResultList)
            {
                if (result.hasPassed)
                    count++;
            }

            levelIndex = count - (presentProgress.chapter * 32);
        }

        return levelIndex;
    }

    public void NextDialogueButtonAction()
    {
        if (DialogueManager.Instance.DialogueFinished())
        {
            ActionAfterDialogue();
        }
        else
        {
            DialogueManager.Instance.NextDialogueAction();
        }
    }

    public void SelectProgress(Progress thisProgress)
    {
        presentProgress = thisProgress;
    }

    public void SelectCollection(CollectionClass collection)
    {
        selectedMapIndex = 0;
        currentCollection = collection;
    }

    public void RetryCurrentCollection()
    {
        selectedMapIndex = 0;
        formerSelectedMapIndex = -1;
        collectionResultList.Clear();
        StateButtonAction((int)StateEnum.GamePlay);
    }

    public void CompletedLevel(int pIndex, GameObject target)
    {
        //this check should be called before set result
        bool[] newRecord = NewRecordTimeAndOperation(selectedMapIndex);
        
        SetResult(pIndex, true);

        int nextLevelIndex = selectedMapIndex + 1;
        if (SelectLevel(nextLevelIndex))
        {
            int mapID = currentCollection.mapIDList[selectedMapIndex];
            MapPreview mapPreview = GlobalParameters.Instance.ReturnMapPreviewByID(mapID);
            GlobalParameters.Instance.LoadMap(mapID);

            CutSceneRequest req = new CutSceneRequest()
            {
                hasCurtain = false,
                hasCountdown = mapPreview.hasCountdown,
                showWinner = currentCollection.multiplayerMode == MultiplayerMode.Competitive &&
                             currentCollection.playerCount > 1,
                moveAllPlayers = currentCollection.multiplayerMode == MultiplayerMode.Cooperative &&
                                 currentCollection.playerCount > 1,
                nextMapID = mapID,
                targetObject = target,
                activePlayerCount = activePlayerCount,
                winnerPlayerIndex = pIndex,
                playerNextPositions = GlobalParameters.Instance.GetCurrentMapPlayerPosArr(),
                time = levelTime,
                operation = currentLevelResult.operationCount,
                timeNewRecord = newRecord[0],
                operationNewRecord = newRecord[1]
            };

            StartCoroutine(cutSceneController.RequestCutScene(req));
        }
        else
        {
            CollectionFinishedAction(nextLevelIndex);
        }
    }

    private string presentDialogueName;
    private bool inDialogueArea = false;
    private Action<InstructionType, Vector3, bool> gameInstructionAction;

    public void SubscribeGameInstructionAction(Action<InstructionType, Vector3, bool> cAction)
    {
        gameInstructionAction = cAction;
    }

    public void ShowStartDialogueInstruction(bool show, string dialogueName = "", Vector3 worldPos = default)
    {
        if (show)
        {
            inDialogueArea = true;
            presentDialogueName = dialogueName;
        }
        else
        {
            inDialogueArea = false;
            presentDialogueName = "";
        }

        ShowInGameInstruction(InstructionType.StartDialogueInstruction, worldPos, show);
    }

    public void ShowInGameInstruction(InstructionType type, Vector3 pos, bool show)
    {
        gameInstructionAction?.Invoke(type, pos, show);
    }

    public void PlayerRequestGamePlayDialogue()
    {
        if (inDialogueArea && DialogueManager.Instance.PrepareDialogue(presentDialogueName))
        {
            StateButtonAction((int)StateEnum.Dialogue);
        }
    }
    

    public StateEnum ReturnPresentState()
    {
        return (StateEnum)presentStateIndex;
    }

    private bool editorChooseLevel = false;

    public bool ChooseLevelEditMode()
    {
        return editorChooseLevel;
    }

    public void HandlePlayerVerticalInput_UIMode(int changeAmount, int playerIndex)
    {
        //UI vertical selection can only be controlled by player 0
        if (currentInteractionUI != null && playerIndex == 0)
        {
            currentInteractionUI.UIRespondPlayerVerticalInput(changeAmount);
        }

        if (presentStateIndex == (int)StateEnum.MapEditor && playerIndex == 0)
        {
            MapEditor.Instance.MoveSelectedTools(new Vector2(0, changeAmount));
        }
    }

    public void HandlePlayerHorizontalInput_UIMode(int changeAmount, int playerIndex)
    {
        //player 0 can always control horizontal selection in UI
        //other players can use horizontal selection in choose player state
        if (presentStateIndex == (int)StateEnum.ChoosePlayer)
        {
            choosePlayerDataAssigner.UIRespondPlayerHorizontalInput(playerIndex, changeAmount);
        }
        else
        {
            //it there is interaction UI, then don't need game pad cursor
            if (currentInteractionUI)
            {
                currentInteractionUI.UIRespondPlayerHorizontalInput(changeAmount);
            }

            if (presentStateIndex == (int)StateEnum.MapEditor && playerIndex == 0)
            {
                MapEditor.Instance.MoveSelectedTools(new Vector2(changeAmount, 0));
            }
        }
    }

    public void HandlePlayerSubmitInput_UIMode(int index)
    {
        if (presentStateIndex == (int)StateEnum.ChoosePlayer)
        {
            choosePlayerDataAssigner.UIRespondPlayerSubmitInput(index);
        }
        else
        {
            //only player 0 can control submit
            if (currentInteractionUI != null && index == 0)
            {
                currentInteractionUI.UIRespondPlayerSubmitInput();
            }
        }
    }

    public void PlayerDeleteInput_UIMode_Started()
    {
        if (presentStateIndex == (int)StateEnum.ChooseSlot)
            inputInstruction.BeginHoldPresenterAnim();
    }

    public void PlayerDeleteInput_UIMode_Performed()
    {
        if (presentStateIndex == (int)StateEnum.ChooseSlot)
            slotDataAssigner.DeleteHighlightedSlot();
    }

    public void PlayerDeleteInput_UIMode_Canceled()
    {
        if (presentStateIndex == (int)StateEnum.ChooseSlot)
            inputInstruction.CancelHoldPresenterAnim();
    }

    public ColorManager ReturnColorManager() => colorManager;

    public void NextLevelButtonAction()
    {
        if (!story)
            SaveCollectionResult(-1);

        int nextLevelIndex = selectedMapIndex + 1;
        if (SelectLevel(nextLevelIndex))
        {
            int mapID = currentCollection.mapIDList[selectedMapIndex];
            MapPreview mapPreview = GlobalParameters.Instance.ReturnMapPreviewByID(mapID);
            GlobalParameters.Instance.LoadMap(mapID);

            CutSceneRequest req = new CutSceneRequest()
            {
                hasCurtain = true,
                hasCountdown = mapPreview.hasCountdown,
                showWinner = false,
                nextMapID = mapID,
                targetObject = null,
                activePlayerCount = 1,
                winnerPlayerIndex = -1,
                playerNextPositions = null,
                time = levelTime,
                operation = currentLevelResult.operationCount,
                timeNewRecord = false,
                operationNewRecord = false
            };

            StartCoroutine(cutSceneController.RequestCutScene(req));
        }
        else
        {
            CollectionFinishedAction(nextLevelIndex);
        }
    }

    public void ExitGamePlayButtonAction()
    {
        if (story)
        {
            StateButtonAction((int)StateEnum.ChooseStoryLevel);
        }
        else
        {
            StateButtonAction((int)StateEnum.ChooseCollection);
        }
    }

    private void ClearPlayerExceptOne()
    {
        for (int i = 1; i < playerList.Count; i++)
        {
            playerList[i].playerObj.SetActive(false);
        }

        if (activePlayerCount >= 2)
            myJoinedInputControlList.RemoveRange(1, myJoinedInputControlList.Count - 1);

        activePlayerCount = 1;
    }

    public void SetPlayerColor(int playerIndex, ColorEnum playerColor, Color color)
    {
        playerList[playerIndex].playerColor = playerColor;
        playerList[playerIndex].playerController.SetPlayerColor(color);
    }

    public void SpawnPlayer(int index, Vector3 pos, int orderInLayer)
    {
        if (index < playerList.Count)
        {
            playerList[index].playerObj.transform.position = pos;
            playerList[index].playerController.SetPlayerOrderInLayer(orderInLayer);
        }
    }

    public Color ReturnInGamePlayerColorByIndex(int playerIndex)
    {
        if (playerIndex >= 0 && playerIndex < playerList.Count)
        {
            return colorManager.ReturnColorByType(playerList[playerIndex].playerColor);
        }

        return Color.clear;
    }

    public void RewindMapIfRequiredDead()
    {
        bool deadCondition = true;
        for (int i = 0; i < activePlayerCount; i++)
        {
            if (playerList[i].playerController.playerAlive || !playerList[i].playerController.dieTaskDone)
                deadCondition = false;
        }

        if (currentCollection.multiplayerMode == MultiplayerMode.Cooperative || story)
            deadCondition = true;

        if (deadCondition)
            RestartButtonAction();
    }

    public SpecificController GetNextJoinPlayerSpecificController()
    {
        //if there are any gamepad not connected
        //suggest adding gamepad
        //else
        //if keyboard1 is added
        //suggest kayboard2
        //else
        //suggest keyboard1

        if (activePlayerCount == 2 && story)
            return SpecificController.None;


        InputDevice gamePad = ReturnNotConnectedGamepad();
        if (gamePad != null)
        {
            return ReturnSpecificController(gamePad);
        }

        if (CurrentDevices().Any(i => i.device is Keyboard))
        {
            if (myJoinedInputControlList.All(i => i.controlName != "space"))
            {
                return SpecificController.Keyboard1;
            }
            else if (myJoinedInputControlList.All(i => i.controlName != "rightShift"))
            {
                return SpecificController.Keyboard2;
            }
        }

        return SpecificController.None;
    }

    public void SetInputMap(string map)
    {
        foreach (var player in playerList)
        {
            if (player.playerAction.isActiveAndEnabled)
                player.playerAction.SwitchActionMap(map);
        }
    }

    #endregion

    #region Helper

    private void AnewFirstPlayerController()
    {
        if (firstPlayerControllerSelected)
        {
            playerList[0].playerObj.SetActive(false);
            activePlayerCount = 0;
            if (myJoinedInputControlList.Count > 0)
                myJoinedInputControlList.RemoveAt(0);
        
            ToggleFirstPlayerPickedState();
        }
    }


    private string ReturnLevelName(int index)
    {
        if (index >= 0 && index < currentCollection.mapIDList.Count)
        {
            int mapID = currentCollection.mapIDList[index];
            return GlobalParameters.Instance.ReturnMapPreviewByID(mapID).mapName;
        }
        else
        {
            return "";
        }
    }

    //this only give suggestion
    //it does not select level
    private int ShouldPlayLevel()
    {
        int level = 0;
        if (story)
        {
            foreach (var result in presentProgress.levelResultList)
            {
                if (result.hasPassed)
                    level++;
            }

            level = ProgressCountToChapterLevelIndex(level);
        }
        else
        {
            level = collectionResultList.Count == currentCollection.mapIDList.Count
                ? currentCollection.mapIDList.Count - 1
                : collectionResultList.Count;
        }

        return level;
    }

    private int ProgressCountToChapterLevelIndex(int count)
    {
        int levelIndex = 0;
        if (currentCollection.chapterIndex < presentProgress.chapter)
        {
            levelIndex = 31;
        }
        else
        {
            levelIndex = count - (presentProgress.chapter * 32);

            //when all of current collection is played
            if (levelIndex > currentCollection.mapIDList.Count - 1)
                levelIndex = currentCollection.mapIDList.Count - 1;
        }

        return levelIndex;
    }

    private int ChapterLevelIndexToProgressCount(int index)
    {
        return index + presentProgress.chapter * 32;
    }

    private int CurrentCollectionLevelIndexToProgressCount(int index)
    {
        if (currentCollection.chapterIndex >= 0)
        {
            return index + currentCollection.chapterIndex * 32;
        }

        return index;
    }

    private void ActionAfterDialogue()
    {
        presentProgress.dialogueProgress.Add(presentDialogueName);
        if (DialogueManager.Instance.GotLevelDialogueName(presentDialogueName))
        {
            if (ShouldPlayLevel() < 10)
            {
                ActionAfterChapterStartDialogue();
            }
            else
            {
                ActionAfterChapterEndDialogue();
            }
        }
        else
        {
            ActionAfterGamePlayDialogue();
        }
    }

    private void ActionAfterChapterStartDialogue()
    {
        StateButtonAction((int)StateEnum.ChooseStoryLevel);
        SelectLevel(0);
        PickLevelStartAction();
    }

    private void ActionAfterChapterEndDialogue()
    {
        //after chapter end dialogue
        //first return to choose level UI to set state right
        //then go to choose chapter UI
        //show an animation of new chapter being enabled
        StateButtonAction((int)StateEnum.ChooseStoryLevel);
        StateButtonAction((int)StateEnum.ChooseChapter);
    }

    private void ActionAfterGamePlayDialogue()
    {
        GlobalParameters.Instance.UpdateDialogueAreaState(presentDialogueName);
        StateButtonAction((int)StateEnum.GamePlay);
        Time.timeScale = 1;
    }

    private void SaveCollectionResult(int playerIndex)
    {
        currentLevelResult.playerIndex = playerIndex;
        currentLevelResult.timeDur = levelTime;
        currentLevelResult.hasPassed = true;
        collectionResultList.Add(currentLevelResult);
    }

    private void ActivateGamePadCursor()
    {
        Cursor.visible = presentStateIndex != (int)StateEnum.GamePlay;

        gamepadCursorActive = (presentStateIndex == (int)StateEnum.ChooseCollection ||
                               presentStateIndex == (int)StateEnum.CollectionResult ||
                               presentStateIndex == (int)StateEnum.MapEditor ||
                               presentStateIndex == (int)StateEnum.MapEditorPause ||
                               presentStateIndex == (int)StateEnum.CollectionEditor ||
                               presentStateIndex == (int)StateEnum.ChooseEditorMap ||
                               presentStateIndex == (int)StateEnum.ChooseMapPopup ||
                               presentStateIndex == (int)StateEnum.Leaderboard
                              ) &&
                              firstPlayerControlType == ControlType.Gamepad;

        virtualMouseController.ShowVirtualMouse(gamepadCursorActive);
        Cursor.visible = !gamepadCursorActive;
    }

    private SpecificController ReturnSpecificController(InputDevice iDevice, string controlName = "space")
    {
        SpecificController specificController = SpecificController.None;
        if (iDevice is Keyboard)
        {
            specificController = controlName == "space" ? SpecificController.Keyboard1 : SpecificController.Keyboard2;
        }
        else if (iDevice is UnityEngine.InputSystem.XInput.XInputController)
        {
            specificController = SpecificController.Xbox;
        }
        else if (iDevice is UnityEngine.InputSystem.DualShock.DualShockGamepad)
        {
            specificController = SpecificController.PS;
        }

        return specificController;
    }

    private InputDevice ReturnNotConnectedGamepad()
    {
        List<InputDevice> currentDeviceList = CurrentDevices();
        foreach (var d in currentDeviceList)
        {
            if (d is Gamepad)
            {
                if (myJoinedInputControlList.All(i => i.iDevice != d))
                    return d;
            }
        }

        return null;
    }

    private List<InputDevice> CurrentDevices()
    {
        List<InputDevice> newList = new List<InputDevice>();
        foreach (var device in InputSystem.devices)
        {
            newList.Add(device);
        }

        return newList;
    }

    private float ReturnChapterTime(int index)
    {
        Debug.Log($"chapter: {index}");
        int startIndex = index * 32;
        int endIndex = (index + 1) * 32;
        float time = 0;
        for (int i = startIndex; i < endIndex; i++)
        {
            if (presentProgress.levelResultList[i].hasPassed)
            {
                Debug.Log($"level: {i}; dur: {presentProgress.levelResultList[i].timeDur}");
                time += presentProgress.levelResultList[i].timeDur;
            }
                
        }

        return time;
    }

    private int ReturnChapterOperation(int index)
    {
        int startIndex = index * 32;
        int endIndex = (index + 1) * 32;
        int op = 0;
        for (int i = startIndex; i < endIndex; i++)
        {
            if (presentProgress.levelResultList[i].hasPassed)
                op += presentProgress.levelResultList[i].operationCount;
        }

        return op;
    }

    //this should be called before set result
    private bool[] NewRecordTimeAndOperation(int index)
    {
        bool[] timeAndOperation = new bool[] { false, false };
        int selectedMapResultIndex = CurrentCollectionLevelIndexToProgressCount(index);
        if (selectedMapResultIndex >= 0 && selectedMapResultIndex < presentProgress.levelResultList.Count && story)
        {
            timeAndOperation[0] = levelTime < presentProgress.levelResultList[selectedMapResultIndex].timeDur;
            timeAndOperation[1] = currentLevelResult.operationCount <
                                  presentProgress.levelResultList[selectedMapResultIndex].operationCount;
        }

        return timeAndOperation;
    }

    #endregion
}