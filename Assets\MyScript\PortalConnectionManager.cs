using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;

public class PortalConnectionManager : MonoBehaviour
{
    private List<GameObject> notConnectedPortalList = new List<GameObject>();

    private StateEnum currentGameState;

    public void InitPortalConnection(List<MyVector3> portalDataList)
    {
        currentGameState = GameManager.Instance.ReturnPresentState();
        if (currentGameState == StateEnum.GamePlay)
        {
            ClearPortal();
            if (portalDataList != null)
            {
                foreach (MyVector3 p in portalDataList)
                {
                    GameObject o = GlobalParameters.Instance.ReturnStickableByPos(p.ConvertToVector3());
                    (int chunkIndex, int objIndex) = GlobalParameters.Instance.ReturnChunkStickableIndexByObj(o);
                    if (chunkIndex != -1 && objIndex != -1)
                    {
                        GlobalParameters.Instance.chunkPool.activePool[chunkIndex].chunkChildList[objIndex]
                            .closeToolAction = AssignPortal(o);
                    }
                }
            }
        }
    }
    
    public Action AssignPortal(GameObject portalObj, GameObject player = null)
    {
        //assign portal will be retrieved by
        //1. player collect
        //2. chunk got out of screen
        //3. restart or load a new level
        notConnectedPortalList.Add(portalObj);
        //if the current portal list count is even number
        //then the newly added item can make connection with former one
        if (notConnectedPortalList.Count == 2)
        {
            GameObject targetPortal = notConnectedPortalList[0];

            if (player != null)
            {
                ChunkClass targetChunk = targetPortal.GetComponentInParent<ChunkClass>();
                int objIndex = targetChunk.ReturnIndexByObj(targetPortal);
                ToolDirection targetPortalToolDir = targetChunk.chunkChildList[objIndex].toolDir;
                Vector3 vectorDir = GlobalMethod.ReturnVectorDirByDirectionAndTool(targetPortalToolDir, ToolID.Portal);
                Vector3 targetPos = targetPortal.transform.position + vectorDir;
                player.transform.position = targetPos;
                //play portal effect
                AudioManager.Instance.PlayerAudioSourcePlay(player.GetComponent<PlayerController>().playerIndex, PlayerAudioEnum.PlayerPortal);
            }
            
            
            CreatePortalConnectionPresenter(portalObj, targetPortal);
            
            notConnectedPortalList.Clear();
        }

        return () => RetrievePortal(portalObj);
    }

    public void MapEditorInitPortalConnection()
    {
        currentGameState = GameManager.Instance.ReturnPresentState();
        ClearPortal();
        List<MyVector3> portalDataList = GlobalParameters.Instance.ReturnPresentMap().portalSequence;
        if (portalDataList != null)
        {
            foreach (MyVector3 p in portalDataList)
            {
                GameObject o = GlobalParameters.Instance.ReturnStickableByPos(p.ConvertToVector3());
                MapEditorAssignPortal(o);
            }
        }
    }

    public void MapEditorAssignPortal(GameObject portalObj)
    {
        notConnectedPortalList.Add(portalObj);

        if (notConnectedPortalList.Count == 2)
        {
            GameObject targetPortal = notConnectedPortalList[0];
            
            CreatePortalConnectionPresenter(portalObj, targetPortal);
            
            notConnectedPortalList.Clear();
        }
    }

    public void MapEditorDeletePortal(GameObject portalObj)
    {
        DeletePortalConnectionPresenter(portalObj);
        notConnectedPortalList.Remove(portalObj);
    }

    public void ClearPortal()
    {
        notConnectedPortalList.Clear();
        
        for (int i = 0; i < portalConList.Count; i++)
        {
            Destroy(portalConList[i].endPortalText.gameObject);
            Destroy(portalConList[i].startPortalText.gameObject);
        }
        portalConList.Clear();
    }

    private void RetrievePortal(GameObject portalObj)
    {
        if (GameManager.Instance.ReturnPresentState() == StateEnum.MapEditor) return;
        
        DeletePortalConnectionPresenter(portalObj);
        notConnectedPortalList.Remove(portalObj);
    }


    #region Portal connection presenter

    [SerializeField] private TMP_Text portalTextPrefab;
    [SerializeField] private Vector2 gamePlayTextOffset;
    [SerializeField] private Vector2 editorTextOffset;
    [SerializeField] private int gamePlayTextSize;
    [SerializeField] private int editorTextSize;
    [SerializeField] private Color textHighlightColor;

    private Color textOriginalColor;

    private Vector2 textOffset;

    private void Start()
    {
        portalTextPrefab.gameObject.SetActive(false);
        textOriginalColor = portalTextPrefab.color;
    }

    private struct PortalConnection
    {
        public PortalConnection(int index, GameObject startP, GameObject endP, TMP_Text textPrefab, Transform parent)
        {
            connectionIndex = index;
            startPortal = startP;
            endPortal = endP;
            
            startPortalText = Instantiate(textPrefab, parent);
            startPortalText.gameObject.SetActive(true);
            startPortalText.text = index.ToString();
            
            endPortalText = Instantiate(textPrefab, parent);
            endPortalText.gameObject.SetActive(true);
            endPortalText.text = index.ToString();
            
            startPortalTextRT = startPortalText.GetComponent<RectTransform>();
            endPortalTextRT = endPortalText.GetComponent<RectTransform>();
        }
        

        public int connectionIndex;
        public GameObject startPortal;
        public GameObject endPortal;

        public TMP_Text startPortalText;
        public TMP_Text endPortalText;
        public RectTransform startPortalTextRT;
        public RectTransform endPortalTextRT;
    }

    private List<PortalConnection> portalConList = new List<PortalConnection>();

    private void CreatePortalConnectionPresenter(GameObject startP, GameObject endP)
    {
        PortalConnection pCon = new PortalConnection(GenerateNewConnectionIndex(0), startP, endP, portalTextPrefab ,transform);
        int currentTextSize = currentGameState == StateEnum.GamePlay ? gamePlayTextSize : editorTextSize;
        
        pCon.startPortalText.fontSize = currentTextSize;
        pCon.endPortalText.fontSize = currentTextSize;

        StartCoroutine(PortalConnectAnim(pCon));

        textOffset = currentGameState == StateEnum.GamePlay ? gamePlayTextOffset : editorTextOffset;
        
        portalConList.Add(pCon);
    }

    private int GenerateNewConnectionIndex(int tryIndex)
    {
        if (portalConList.Any(i => i.connectionIndex == tryIndex))
        {
            return GenerateNewConnectionIndex(tryIndex + 1);
        }

        return tryIndex;
    }

    private void DeletePortalConnectionPresenter(GameObject p)
    {
        for (int i = 0; i < portalConList.Count; i++)
        {
            bool isStartPortal = portalConList[i].startPortal == p;
            bool isEndPortal = portalConList[i].endPortal == p;
            if (isStartPortal || isEndPortal)
            {
                Destroy(portalConList[i].endPortalText.gameObject);
                Destroy(portalConList[i].startPortalText.gameObject);
                GameObject assignObj = isStartPortal ? portalConList[i].endPortal : portalConList[i].startPortal;
                portalConList.RemoveAt(i);
                
                
                AssignPortal(assignObj);
            }
        }
    }

    // Update is called once per frame
    void Update()
    {
        UpdateTextPosition();
    }

    private void UpdateTextPosition()
    {
        for (int i = 0; i < portalConList.Count; i++)
        {
            if (portalConList[i].startPortal != null && portalConList[i].endPortal != null)
            {
                portalConList[i].startPortalTextRT.anchoredPosition = GlobalMethod.WorldPositionToCanvasPosition(portalConList[i].startPortal.transform.position) + ReturnTextOffset(portalConList[i].startPortal);
                portalConList[i].endPortalTextRT.anchoredPosition = GlobalMethod.WorldPositionToCanvasPosition(portalConList[i].endPortal.transform.position) + ReturnTextOffset(portalConList[i].endPortal);
            }
        }
    }

    private Vector2 ReturnTextOffset(GameObject obj)
    {
        ToolDirection portalDir = GlobalParameters.Instance.ReturnBagToolByObj(obj).toolDirection;
        
        int rotateTimes = (int)portalDir;
        Vector2 offset = textOffset;
        for (int i = 0; i < rotateTimes; i++)
        {
            offset = GlobalMethod.AnticlockwiseOrthogonalRotation(offset);
        }

        return offset;
    }

    public List<MyVector3> ReturnCurrentMapPortalDataList()
    {
        //this should be called after chunk attach is completed
        List<MyVector3> newPortalDataList = new List<MyVector3>();
        var ascendingPortal = portalConList.OrderBy(i => i.connectionIndex);
        foreach (PortalConnection connection in ascendingPortal)
        {
            if (connection.startPortal != null && connection.endPortal != null)
            {
                newPortalDataList.Add(new MyVector3(connection.startPortal.transform.position));
                newPortalDataList.Add(new MyVector3(connection.endPortal.transform.position));
            }
            
        }
        
        if(notConnectedPortalList.Count == 1)
            newPortalDataList.Add(new MyVector3(notConnectedPortalList[0].transform.position));

        return newPortalDataList;
    }

    private WaitForSeconds portalConnectAnimDur = new WaitForSeconds(0.5f);
    private IEnumerator PortalConnectAnim(PortalConnection pCon)
    {
        pCon.endPortalText.color = textHighlightColor;
        pCon.startPortalText.color = textHighlightColor;
        yield return portalConnectAnimDur;
        if (pCon.endPortalText != null)
        {
            pCon.endPortalText.color = textOriginalColor;
            pCon.startPortalText.color = textOriginalColor;
        }
        
    }
    
    #endregion
    
    
}
