using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Transform = UnityEngine.Transform;
using Vector2 = UnityEngine.Vector2;

public class ChapterDataAssigner : MonoBehaviour
{
    [SerializeField] private Transform content;
    [SerializeField] private ImageButtonInteractionUI chapterInteractionUI;
    [SerializeField] private ChapterPreviewList chapterPreviewList;
    [SerializeField] private GameObject unlockChapterAnimObj;

    private RectTransform unlockChapterAnimRT;
    private class ChapterPresenter
    {
        public ChapterPresenter(GameObject obj)
        {
            UIObj = obj;
            chapterTMP = UIObj.transform.Find("ChapterName").GetComponent<TMP_Text>();
            levelTMP = UIObj.transform.Find("ChapterState").Find("Level").Find("LevelValue").GetComponent<TMP_Text>();
            timeTMP = UIObj.transform.Find("ChapterState").Find("Time").Find("TimeValue").GetComponent<TMP_Text>();
            operationTMP = UIObj.transform.Find("ChapterState").Find("Operation").Find("OperationValue").GetComponent<TMP_Text>();
            dateTMP = UIObj.transform.Find("ChapterState").Find("Date").GetComponent<TMP_Text>();
        }

        public GameObject UIObj;
        public TMP_Text chapterTMP;
        public TMP_Text levelTMP;
        public TMP_Text timeTMP;
        public TMP_Text operationTMP;
        public TMP_Text dateTMP;
    }

    private List<ChapterPresenter> chapterPresenterList = new List<ChapterPresenter>();

    private Progress currentProgress;
    private Progress formerProgress;

    private int formerChapter = -1;

    public void ShowChapter(Progress progress)
    {
        InitAssignment();
        unlockChapterAnimObj.SetActive(false);
        
        currentProgress = progress;
        
        for (int i = 0; i < chapterPresenterList.Count; i++)
        {
            AssignOneData(i);
            if (i <= currentProgress.chapter)
            {
                UpdateOneState(i);
                chapterInteractionUI.RemoveDisabledButton(i);
            }
            else
            {
                chapterPresenterList[i].UIObj.GetComponent<Image>().color = Color.gray;
                chapterPresenterList[i].chapterTMP.color = Color.gray;
                chapterInteractionUI.AddDisabledButton(i);
            }
        }
        
        if (formerProgress == currentProgress && formerChapter < currentProgress.chapter)
        {
            //new chapter unlocked
            int newUnlockedChapter = currentProgress.chapter;
            
            //set selection to this new chapter
            chapterInteractionUI.RemoveDisabledButton(newUnlockedChapter);
            
            chapterInteractionUI.UpdateInitSelection(newUnlockedChapter);
            
            //then play animation
            PlayUnlockChapterAnimation();
        }
        formerChapter = currentProgress.chapter;
        formerProgress = currentProgress;

    }

    private void InitAssignment()
    {
        if (content.childCount != chapterPresenterList.Count)
        {
            chapterPresenterList.Clear();
            int index = 0;
            foreach (Transform item in content)
            {
                chapterPresenterList.Add(new ChapterPresenter(item.gameObject));
                AssignOneButtonEvent(index);
                index++;
            }
        }
    }

    private void AssignOneData(int index)
    {
        int realIndex = QueryCollectionIndex(index);
        int chapterIndex = chapterPreviewList.previewList[realIndex].chapterIndex;
        LanguageEnum currentLanguage = MyLocalizationManager.Instance.currentLanguage;
        string chapterName = currentLanguage == LanguageEnum.English ? "Chapter " : "章节";
        chapterName += (chapterIndex + 1);
        chapterPresenterList[index].chapterTMP.text = chapterName;
    }

    private void UpdateOneState(int index)
    {
        chapterPresenterList[index].UIObj.GetComponent<Image>().color = Color.white;
        chapterPresenterList[index].chapterTMP.color = Color.white;
        chapterPresenterList[index].timeTMP.text = WholeTime(index);
        chapterPresenterList[index].operationTMP.text = WholeOperation(index);
        
        
        if (currentProgress.chapter > index)
        {
            chapterPresenterList[index].levelTMP.text = "32";
        }
        else
        {
            chapterPresenterList[index].levelTMP.text = ShouldPlayLevelIndex(index).ToString();
        }
    }
    
    private int QueryCollectionIndex(int sequence)
    {
        int count = 0;
        int index = -1;
        for (int i = 0; i < chapterPreviewList.previewList.Count; i++)
        {
            if (chapterPreviewList.previewList[i].isChapter)
            {
                if (count == sequence)
                    index = i;
                count++;
            }
        }

        return index;
    }

    private void AssignOneButtonEvent(int index)
    {
        chapterPresenterList[index].UIObj.GetComponent<Button>().onClick.AddListener(()=>PickChapterButtonAction(index));
    }

    private void PickChapterButtonAction(int index)
    {
        if (currentProgress.chapter >= index)
        {
            int realIndex = QueryCollectionIndex(index);
            FindObjectOfType<ChapterArtController>()?.UpdateBackgroundWithChapterIndex(index);
            GameManager.Instance.SelectCollection(chapterPreviewList.previewList[realIndex]);
            GameManager.Instance.StateButtonAction((int)StateEnum.ChooseStoryLevel);
        }
    }
    
    private string WholeTime(int chapterIndex)
    {
        float time = 0;
        if (currentProgress.levelResultList.Count > 0)
        {
            int baseIndex = chapterIndex * 32;
            int ceilIndex = (chapterIndex + 1)* 32;
            for (int i = baseIndex; i < ceilIndex; i++)
            {
                if (i >= currentProgress.levelResultList.Count)
                    break;
                time += currentProgress.levelResultList[i].timeDur;
            }
        }

        return TimeSpan.FromSeconds(time).ToString("hh':'mm':'ss");;
    }
    
    private string WholeOperation(int chapterIndex)
    {
        int op = 0;
        if (currentProgress.levelResultList.Count > 0)
        {
            int baseIndex = chapterIndex * 32;
            int ceilIndex = (chapterIndex + 1)* 32;
            for (int i = baseIndex; i < ceilIndex; i++)
            {
                if (i >= currentProgress.levelResultList.Count)
                    break;
                op += currentProgress.levelResultList[i].operationCount;
            }
        }
        
        return op.ToString();
    }

    private int ShouldPlayLevelIndex(int chapterIndex)
    {
        int levelIndex = 0;
        if (chapterIndex < currentProgress.chapter)
        {
            levelIndex = 31;
        }
        else if(chapterIndex == currentProgress.chapter)
        {
            int count = 0;
            foreach (var result in currentProgress.levelResultList)
            {
                if (result.hasPassed)
                    count++;
            }
            levelIndex = count - (currentProgress.chapter * 32);
        }else if (chapterIndex > currentProgress.chapter)
        {
            levelIndex = 0;
        }
        return levelIndex;
    }

    private void PlayUnlockChapterAnimation()
    {
        StopChapterUnlockAnimation();
        newChapterUnlockedAnimation = NewChapterUnlockedAnimation();
        StartCoroutine(newChapterUnlockedAnimation);
    }
    
    private void StopChapterUnlockAnimation()
    {
        if (newChapterUnlockedAnimation != null)
        {
            StopCoroutine(newChapterUnlockedAnimation);
            unlockChapterAnimObj.SetActive(false);
        }
    }

    private float chapterUnlockAnimDur = 2;
    private IEnumerator newChapterUnlockedAnimation;
    private IEnumerator NewChapterUnlockedAnimation()
    {
        float timer = chapterUnlockAnimDur;
        unlockChapterAnimObj.SetActive(true);
        if (unlockChapterAnimRT == null)
            unlockChapterAnimRT = unlockChapterAnimObj.GetComponent<RectTransform>();
        unlockChapterAnimObj.transform.SetParent(chapterPresenterList[currentProgress.chapter].UIObj.transform);
        unlockChapterAnimRT.anchoredPosition = new Vector2(0, 0);
        unlockChapterAnimRT.localScale = new Vector3(1, 1, 1);
        while (timer > 0)
        {
            timer -= Time.unscaledDeltaTime;
            float fill = 1 - timer / chapterUnlockAnimDur;
            unlockChapterAnimRT.localScale = new Vector3(1, 1, 1) + new Vector3(0.5f, 0.5f, 0) * Mathf.Abs(Mathf.Sin(fill*4*Mathf.PI));
            yield return null;
        }
        unlockChapterAnimObj.SetActive(false);
    }
}
