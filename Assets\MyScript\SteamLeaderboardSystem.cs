using System;
using System.Threading.Tasks;
using Steamworks;
using Steamworks.Data;
using UnityEngine;

public static class SteamLeaderBoardSystem
{
    /// <summary>
    ///     This function will only replace your last score if the new one is better.
    /// </summary>
    /// <param name="leaderboard"></param>
    /// <param name="value"></param>
    /// <param name="details"></param>
    public static async Task SubmitLeaderboard(Leaderboard leaderboard, int value,
        int[] details = null)
    {
        var leaderboardUpdate = await leaderboard.SubmitScoreAsync(value, details ?? Array.Empty<int>());
        if (!leaderboardUpdate.HasValue)
        {
            Debug.LogError("leaderboardUpdate is null");
            return;
        }
        
    }


    public static async Task<Leaderboard?> GetLeaderBoards(string lbName, LeaderboardDisplay display)
    {
        try
        {
            return await FindOrCreateLeaderboardAsync(lbName, display);
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }

        return null;
    }

    private static async Task<Leaderboard?> FindOrCreateLeaderboardAsync(string leaderboardName, LeaderboardDisplay display)
    {
        try
        {
            return await SteamUserStats.FindOrCreateLeaderboardAsync(leaderboardName,
                LeaderboardSort.Ascending,
                display);
        }
        catch (Exception e)
        {
            Debug.LogError(e);
        }

        return null;
    }
}