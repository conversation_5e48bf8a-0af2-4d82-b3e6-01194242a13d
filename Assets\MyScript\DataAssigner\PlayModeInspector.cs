using TMPro;
using UnityEngine;

public class PlayModeInspector : MonoBehaviour
{
    [SerializeField] private GameObject editableModeObj;
    [SerializeField] private SelectStateComponent modeSelector;

    [SerializeField] private GameObject notEditableModeObj;
    [SerializeField] private TMP_Text notEditableModeTMP;
    
    private MapPreview currentMapPreview;
    private CollectionClass currentCollection;

    
    public void SetMapPlayModeInspector(MapPreview map, bool editable)
    {
        editableModeObj.SetActive(editable);
        notEditableModeObj.SetActive(!editable);
        currentMapPreview = map;
        

        if (currentMapPreview.playerCount == 1 && currentMapPreview.playerCountMax == 1)
        {
            notEditableModeObj.SetActive(false);
            editableModeObj.SetActive(false);
        }
        else
        {
            notEditableModeTMP.text = map.multiplayerMode.ToString();
            modeSelector.SetSelection(map.multiplayerMode.ToString(), MapPlayModeChangeFunc);
        }
        
    }


    public void SetCollectionPlayModeInspector(CollectionClass collection,  bool editable)
    {
        editableModeObj.SetActive(editable);
        notEditableModeObj.SetActive(!editable);
        currentCollection = collection;
        
        if (currentCollection.playerCount == 1 && currentCollection.playerCountMax == 1)
        {
            notEditableModeObj.SetActive(false);
            editableModeObj.SetActive(false);
        }
        else
        {
            notEditableModeTMP.text = currentCollection.multiplayerMode.ToString();
            modeSelector.SetSelection(currentCollection.multiplayerMode.ToString(), CollectionPlayModeChangeFunc);
        }
    }
    
    private bool MapPlayModeChangeFunc(string mode)
    {
        switch (mode)
        {
            case "Competitive":
                currentMapPreview.multiplayerMode = MultiplayerMode.Competitive;
                return true;
            case "Cooperative":
                currentMapPreview.multiplayerMode = MultiplayerMode.Cooperative;
                return true;
            default:
                return false;
        }
    }
    
    private bool CollectionPlayModeChangeFunc(string mode)
    {
        switch (mode)
        {
            case "Competitive":
                currentCollection.multiplayerMode = MultiplayerMode.Competitive;
                return true;
            case "Cooperative":
                currentCollection.multiplayerMode = MultiplayerMode.Cooperative;
                return true;
            default:
                return false;
        }
    }
    
    
}
