using UnityEngine;

public class DialogueTrigger : MonoBehaviour
{
    private Transform thisTransform;

    private Transform playerTransform;
    // Start is called before the first frame update
    void Start()
    {
        thisTransform = transform;
        playerTransform = GameManager.Instance.playerList[0].playerObj.transform;
    }

    // Update is called once per frame
    void Update()
    {
        PlayerInRange();
    }

    private bool playerEntered = false;
    private void PlayerInRange()
    {
        if (Vector3.Distance(playerTransform.position, thisTransform.position) < range && !playerEntered)
        {
            playerEntered = true;
            GameManager.Instance.ShowStartDialogueInstruction(true, thisTransform.name, thisTransform.position + 1.5f*Vector3.up);
        }
        
        if (Vector3.Distance(playerTransform.position, thisTransform.position) > range && playerEntered)
        {
            GameManager.Instance.ShowStartDialogueInstruction(false, thisTransform.name, thisTransform.position + 1.5f*Vector3.up);
            playerEntered = false;
        }
    }

    private float range;

    public void InitTrigger(float _range)
    {
        range = _range;
    }

    public void UpdateState()
    {
        Color color;
        if (GameManager.Instance.presentProgress.dialogueProgress.Contains(gameObject.name))
        {
            color = Color.white;
        }
        else
        {
            color = Color.yellow;
        }

        GetComponent<SpriteRenderer>().color = color;
    }
}
