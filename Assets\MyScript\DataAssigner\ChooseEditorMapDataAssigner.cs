using UnityEngine;
using UnityEditor;
using System.Text;
using System.Runtime.Serialization.Formatters.Binary;
using System.IO;

public class ChooseEditorMapDataAssigner : MapDataAssigner
{
    public override void ShowMap(CollectionClass collection, MapFilterCondition condition = null)
    {
        base.ShowMap(collection, condition);
        inspectorDataAssigner.ClearInspector();
    }
    
    public void CreateMapButtonAction()
    {
        CreateMapInLocal("new map", null);
    }

    public void DuplicateButtonAction()
    {
        GlobalParameters.Instance.LoadMap(selectedMapID);
        CreateMapInLocal(allMapList[selectedMapIndex].mapName, GlobalParameters.Instance.ReturnPresentMap(), allMapList[selectedMapIndex].playerCount);
    }
    
    
    private StringBuilder mapNameSB = new StringBuilder();
    
    private void CreateMapInLocal(string name, MapTemplate mapTemplate, int playerCount = 1, int playerCountMax = 1)
    {
        int newMapID = GlobalParameters.Instance.NewMapID();
        if (newMapID >= 0)
        {
            mapNameSB.Clear();
            mapNameSB.Append(name);
            GlobalParameters.Instance.GetNewMapName(mapNameSB, 0);
            MapPreview newMapPreview = new MapPreview(newMapID, mapNameSB.ToString(), playerCount, playerCountMax);
            newMapPreview.isBuiltin = false;
        
            allMapList.Insert(0, newMapPreview);
            GlobalParameters.Instance.SaveMapPreviewList();
        
        
            MapTemplate newMap = mapTemplate != null ? DeepClone(mapTemplate) : new MapTemplate(null);
        
            GlobalParameters.Instance.SetPresentMap(newMap);
        
            SaveSystem.SetMap(newMapID.ToString(), newMap);
        
            ShowMap(null);
        }
    }

    public void DeleteButtonAction()
    {
        if (allMapList[selectedMapIndex].isBuiltin)
        {
            DeleteMapAsset(allMapList[selectedMapIndex].mapID.ToString());
        }
        else
        {
            SaveSystem.DeleteMap(allMapList[selectedMapIndex].mapID.ToString());
        }
        
        //delete item in list
        allMapList.RemoveAt(selectedMapIndex);
        GlobalParameters.Instance.SaveMapPreviewList();
        ShowMap(null, currentMapFilterCondition);
    }

    private void DeleteMapAsset(string mapID)
    {
#if UNITY_EDITOR
        //delete map asset
        string assetPath = "Assets/Resources/MapData/Maps/" + mapID + ".asset";

        // Check if the asset exists before deleting
        if (AssetDatabase.LoadAssetAtPath<LevelTemplate>(assetPath) != null)
        {
            AssetDatabase.DeleteAsset(assetPath);
            AssetDatabase.SaveAssets();
        }
        else
        {
            Debug.LogError("No ScriptableObject found at the path: " + assetPath);
        }
#endif
    }

    private void CreateMapAsset(string mapID, MapTemplate mapTemplate)
    {
#if UNITY_EDITOR
        LevelTemplate asset = ScriptableObject.CreateInstance<LevelTemplate>();

        asset.bagToolList = mapTemplate.bagToolList;
        asset.dialogueAreaList = mapTemplate.dialogueAreaList;
        asset.otherCharacterList = mapTemplate.otherCharacterList;
        asset.playerPosList = mapTemplate.playerPosList;
        asset.sceneChunkList = mapTemplate.sceneChunkList;

        AssetDatabase.CreateAsset(asset, "Assets/Resources/MapData/Maps/" + mapID + ".asset");
        AssetDatabase.SaveAssets();
        EditorUtility.FocusProjectWindow();
        Selection.activeObject = asset;
#endif
    }
    

    private MapTemplate tempMapTemplate;
    public void BuiltinStateChangeAction(bool builtin)
    {
        //update map data
        GlobalParameters.Instance.LoadMap(selectedMapID);
        tempMapTemplate = GlobalParameters.Instance.ReturnPresentMap();
        if (builtin)
        {
            //create the map asset in builtin folder
            CreateMapAsset(selectedMapID.ToString(), tempMapTemplate);
            
            //delete the map file
            SaveSystem.DeleteMap(selectedMapID.ToString());
        }
        else
        {
            //create the map data in local folder
            SaveSystem.SetMap(selectedMapID.ToString(), tempMapTemplate);
            
            //delete the map asset
            DeleteMapAsset(selectedMapID.ToString());
        }
        GlobalParameters.Instance.LoadMap(selectedMapID);
    }
    
    private T DeepClone<T>(T obj)
    {
        using (var ms = new MemoryStream())
        {
            var formatter = new BinaryFormatter();
            formatter.Serialize(ms, obj);
            ms.Position = 0;

            return (T) formatter.Deserialize(ms);
        }
    }
    
}
