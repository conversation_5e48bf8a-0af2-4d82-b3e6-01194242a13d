using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

//Yimilan
public class SelectStateComponent : MonoBehaviour
{
    //选择器组件，目前只能单选
    private Color selectedColor;
    private Color unselectedColor;

    private ColorManager colorManager;

    [Serializable]
    private class SelectButtonClass
    {
        [Tooltip("必须是enum中的值")] public string name;
        [Tooltip("必须是localization中的key")] public string localizationKey;
        public Button selectButton;
        public Image selectButtonImage;
    }

    [SerializeField] private List<SelectButtonClass> selectButtonList = new List<SelectButtonClass>();

    private int currentSelectedState = 0;

    private bool hasInitialized = false;

    private void InitButtonAction()
    {
        if (!hasInitialized)
        {
            colorManager = GameManager.Instance.ReturnColorManager();
            selectedColor = colorManager.ReturnColorByType(ColorEnum.UIButtonHighLight);
            unselectedColor = colorManager.ReturnColorByType(ColorEnum.UIButtonOriginal);
            for (int i = 0; i < selectButtonList.Count; i++)
            {
                int buttonIndex = i;
                selectButtonList[buttonIndex].selectButtonImage.color = unselectedColor;
                selectButtonList[buttonIndex].selectButton.GetComponentInChildren<TMP_Text>().text = MyLocalizationManager.Instance.RetrieveStringByKey(selectButtonList[buttonIndex].localizationKey);
                selectButtonList[buttonIndex].selectButton.onClick
                    .AddListener(delegate { SelectButtonAction(buttonIndex); });
            }

            hasInitialized = true;
        }
        
    }

    private void SelectButtonAction(int index)
    {
        UpdateButtonAppearance(currentSelectedState, false);
        
        if (index < 0)
        {
            Debug.LogError("does not contain the name");
            return;
        }
        
        bool success = sFunc(selectButtonList[index].name);
        if (success)
        {
            UpdateButtonAppearance(index, true);
            currentSelectedState = index;
        }
            

        
    }

    private void UpdateButtonAppearance(int index, bool isSelected)
    {
        if (index >= 0 && index < selectButtonList.Count)
            selectButtonList[index].selectButtonImage.color = isSelected ? selectedColor : unselectedColor;
    }


    #region API
    private Func<string, bool> sFunc;
    
    public void SetSelection(string selectionName, Func<string, bool> cFunc)
    {
        int index = 0;
        for (int i = 0; i < selectButtonList.Count; i++)
        {
            if (selectButtonList[i].name == selectionName)
                index = i;
        }
        

        InitButtonAction();
        sFunc = cFunc;
        SelectButtonAction(index);
    }
    

    #endregion
}