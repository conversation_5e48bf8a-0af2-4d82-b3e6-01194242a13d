using System.Collections.Generic;
using UnityEngine;

public class SwitchController : OtherCharacterBase
{
    private Transform otherCharacterParent;
    private List<PlanterController> planterInLevel = new List<PlanterController>();
    private bool beginFirstDetect = false;
    private ChunkClass currentChunkClass;
    private int currentBlockIndex;
    private Transform currentAttachedTransform;
    private BagTool currentBagTool;
    private Animator switchAnimator;

    private bool gotCurrentLevelPlanter = false;

    private int playerIndexThatChangedToolUnder = -1;

    private int commandStateBoolID = Animator.StringToHash("Command");
    

    public override void InitCharacter(ToolID toolID)
    {
        base.InitCharacter(toolID);
        otherCharacterParent = characterTransform.parent;
        beginFirstDetect = true;
        
        planterInLevel.Clear();
        gotCurrentLevelPlanter = false;

        playerIndexThatChangedToolUnder = -1;
        
        characterMat.SetColor("_NewColor", originalColor);

        if (switchAnimator == null)
            switchAnimator = GetComponent<Animator>();
        switchAnimator.runtimeAnimatorController = GlobalParameters.Instance.ReturnSwitchController();
    }

    public override void CloseCharacter()
    {
        base.CloseCharacter();
        if(currentChunkClass != null)
           currentChunkClass.RemoveListenerForChunkToolUpdate(WhichPlayerChangedToolUnder);

        gameObject.SetActive(false);
    }

    private void FixedUpdate()
    {
        if (!gotCurrentLevelPlanter)
        {
            GetCurrentLevelPlanter();
        }
        
        if (beginFirstDetect)
        {
            switchAnimator.SetBool(commandStateBoolID, false);
            ApplyGravity();
            DetectBlockUnder();
        }
        else
        {
            MoveWithAttachedTransform();
            DetectToolChange();
        }
        
        CloseOutOfScreenCharacter();
    }

    private void ApplyGravity()
    {
        characterTransform.position += new Vector3(0,-6*Time.fixedDeltaTime, 0);
    }

    private void DetectBlockUnder()
    {
        Vector3 detectOrigin = characterTransform.position - characterTransform.up;

        Collider2D collider =
            Physics2D.OverlapCircle(detectOrigin, 0.1f);

        if (collider != null)
        {
            if (collider.CompareTag("Stickable"))
            {
                WorkOnStickable(collider.gameObject);
                beginFirstDetect = false;
            }
            
        }

    }
    
    private void WorkOnStickable(GameObject obj)
    {
        currentAttachedTransform = obj.transform;
        currentChunkClass = obj.GetComponentInParent<ChunkClass>();
        currentBlockIndex = currentChunkClass.ReturnIndexByObj(obj);
        DetectToolChange();
        
        //add a listener to this chunk
        //when a player tries to change it
        //inform switch of the player index
        currentChunkClass.AddListenerForChunkToolUpdate(WhichPlayerChangedToolUnder);
    }
    
    private void InformPlanterTool(BagTool bagTool)
    {
        foreach (PlanterController planterController in planterInLevel)
        {
            if (planterController.gameObject.activeSelf)
            {
                planterController.SetNextTool(bagTool, playerIndexThatChangedToolUnder);
            }
               
        }
        currentBagTool = bagTool;
    }

    private void GetCurrentLevelPlanter()
    {
        foreach (Transform t in otherCharacterParent)
        {
            if (t.gameObject.activeSelf)
            {
                if(t.GetComponent<CharacterInfo>().ReturnCharacterInfo().toolID == ToolID.Planter)
                    planterInLevel.Add(t.GetComponent<PlanterController>());
            }
        }

        gotCurrentLevelPlanter = true;
    }

    private void DetectToolChange()
    {
        if (currentBlockIndex >= 0 && currentBlockIndex < currentChunkClass.chunkChildList.Count)
        {
            ToolID toolID = currentChunkClass.chunkChildList[currentBlockIndex].toolID;
            ToolDirection toolDir = currentChunkClass.chunkChildList[currentBlockIndex].toolDir;
            BagTool toolUnder = new BagTool(toolID, toolDir);
            if (toolUnder != currentBagTool)
            {
                InformPlanterTool(toolUnder);
            }

            if (toolUnder.toolID == ToolID.Block)
            {
                switchAnimator.SetBool(commandStateBoolID, false);
            }
            else
            {
                switchAnimator.SetBool(commandStateBoolID, true);
            }
        }
    }

    private void MoveWithAttachedTransform()
    {
        characterTransform.position = currentAttachedTransform.TransformPoint(Vector3.up);
        characterTransform.rotation = currentAttachedTransform.rotation;
    }
    

    private Color originalColor = Color.white;

    private void WhichPlayerChangedToolUnder(int playerIndex, int index)
    {
        if (currentBlockIndex == index)
        {
            if (playerIndexThatChangedToolUnder != playerIndex)
            {
                Color newColor = GameManager.Instance.ReturnInGamePlayerColorByIndex(playerIndex);
                characterMat.SetColor("_NewColor", newColor);
                playerIndexThatChangedToolUnder = playerIndex;
            }
            
        }
          
    }
}
