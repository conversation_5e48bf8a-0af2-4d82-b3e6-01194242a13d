using System;
using System.Collections;
using UnityEngine;
using UnityEngine.Localization.Settings;
using UnityEngine.Localization.Tables;

public class MyLocalizationManager : MonoBehaviour
{
    [SerializeField] private LanguageSelector languageSelector;
    public LanguageEnum currentLanguage { get; private set; }

    private bool changingLanguage;

    private readonly string tableName = "UITextTable";
    private StringTable currentTable;

    public static MyLocalizationManager Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance == this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(Instance);
    }

    private void Start()
    {
        InitPlayerPreferenceLanguage();
    }

    private void InitPlayerPreferenceLanguage()
    {
        if (PlayerPrefs.HasKey("Language"))
        {
            UpdateLanguage(PlayerPrefs.GetInt("Language"));
        }
        else
        {
            UpdateLanguage(0);
        }
    }
    
    public void UpdateLanguage(int index)
    {
        if (!changingLanguage)
            StartCoroutine(SetLanguage(index));
    }

    private IEnumerator SetLanguage(int index)
    {
        changingLanguage = true;
        yield return LocalizationSettings.InitializationOperation;
        LocalizationSettings.SelectedLocale = LocalizationSettings.AvailableLocales.Locales[index];
        
        currentLanguage = (LanguageEnum)index;
        currentTable = LocalizationSettings.StringDatabase.GetTable(tableName);
        
        PlayerPrefs.SetInt("Language", index);
        languageSelector.UpdateSelectorLanguage(index);
        languageChangeAction?.Invoke();
        changingLanguage = false;
    }

    public string RetrieveStringByKey(string key)
    {
        if (currentTable != null)
        {
            StringTableEntry entry = currentTable.GetEntry(key);

            if (entry != null)
            {
                return entry.LocalizedValue;
            }
            
            Debug.LogError("Did not find the entry");
            return String.Empty;
        }
        
        Debug.LogError("Did not find the localization table");
        return String.Empty;
    }

    private Action languageChangeAction;
    public void SubscribeLanguageChangeAction(Action cAction)
    {
        languageChangeAction += cAction;
    }
}
