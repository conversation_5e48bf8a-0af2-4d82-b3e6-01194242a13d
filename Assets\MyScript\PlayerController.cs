using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;
using Random = UnityEngine.Random;
using SpriteRenderer = UnityEngine.SpriteRenderer;
using Vector2 = UnityEngine.Vector2;

public class PlayerController : MonoBehaviour, IAlive
{
    [Range(0, .3f)] [SerializeField] private float m_MovementSmoothing = .05f; // How much to smooth out the movement
    [SerializeField] private bool m_AirControl = false; // Whether or not a player can steer while jumping;
    [SerializeField] private LayerMask m_WhatIsGround; // A mask determining what is ground to the character;

    [SerializeField] public List<FaceDetector> faceDetectorList;

    [Serializable]
    public class FaceDetector
    {
        public DetectorEnum face;
        public bool collided;
        public bool hasCandidate;
        public PointDetector[] pointDetector;
    }

    [Serializable]
    public class PointDetector
    {
        public Vector2 detectPos;
        public GameObject stickableObj;
    }


    public Transform squashCentre;

    [Range(0, 1f)] [SerializeField] private float squashRadius = 0.5f;

    [SerializeField] private CustomParticleEffect customParticleEffect;

    private ColorManager colorManager;

    const float k_DetectorRadius = .1f; // Radius of the overlap circle to determine if grounded
    private bool m_Grounded; // Whether or not the player is grounded.
    private bool m_Walled;
    private Rigidbody2D m_Rigidbody2D;
    private BoxCollider2D m_BoxCollider2D;
    private PlayerAction m_PlayerAction;
    private bool m_FacingRight = true; // For determining which way the player is currently facing.
    private Vector3 m_Velocity = Vector3.zero;
    private bool applyedJump = false;
    private bool jumpReachPeak = false;

    public float standardGravity;
    private float gravity;

    private float playerSpeed = 10;

    public AnimationClip jumpAnimClip;
    private float jumpAnimDur;

    [HideInInspector] public int playerIndex = 0;
    

    private float jumpRadiance = Mathf.PI / 3;

    private GameObject candidateObj;
    private GameObject previousCandidateObj;

    private Animator playerAnimator;

    private Transform playerTransform;

    private Vector2 jumpDirection;
    private float jumpHeight = 3;
    private bool canJumpOnWall;
    private float jumpBufferTime = 0.15f;
    private float jumpTimer = 0;

    private float edgeJumpBufferTime = 0.1f;
    private float edgeJumpTimer;
    private bool jumpCooling = false;
    
    

    private float presentVerticalMove;

    public bool playerAlive { private set; get; }

    private ParticleSystem jumpParticle;
    private ParticleSystem landParticle;
    
    private ParticleSystem.MainModule jumpParticleMain;
    private ParticleSystem.MainModule landParticleMain;

    private SpriteRenderer spriteRenderer;

    private CameraBehavior cameraBehavior;

    private void Awake()
    {
        m_Rigidbody2D = GetComponent<Rigidbody2D>();
        playerAnimator = GetComponent<Animator>();
        spriteRenderer = GetComponent<SpriteRenderer>();
        m_BoxCollider2D = GetComponent<BoxCollider2D>();
        m_PlayerAction = GetComponent<PlayerAction>();
        
        playerTransform = transform;
        
        jumpParticle = playerTransform.Find("JumpParticle").GetComponent<ParticleSystem>();
        landParticle = playerTransform.Find("LandParticle").GetComponent<ParticleSystem>();

        jumpParticleMain = jumpParticle.main;
        landParticleMain = landParticle.main;
        
        jumpAnimDur = jumpAnimClip.length;
        gravity = standardGravity;

        colorManager = GameManager.Instance.ReturnColorManager();
        cameraBehavior = (CameraBehavior)FindObjectOfType(typeof(CameraBehavior));
    }

    public void SetPlayer(int _playerIndex)
    {
        playerIndex = _playerIndex;
    }

    public void SetPlayerColor(Color color)
    {
        spriteRenderer.color = color;
        jumpParticleMain.startColor = color;
        landParticleMain.startColor = color;
        customParticleEffect.SetColor(color);
    }

    public void SetPlayerOrderInLayer(int order)
    {
        spriteRenderer.sortingOrder = order;
    }

    private void FixedUpdate()
    {
        CheckCollision();
        CheckSquash();
        UpdateState();
        SetIdle2True();
        if (!jumpReachPeak)
            SetJumpReachPeak();
        if (jumpReachPeak)
            HalfJump(false);
    }
    

    #region Detection

    private Collider2D[] squashColliderArr;
    private void CheckSquash()
    {
        //the first condition of squash: contrast movement by two blocks or block and wall
        //the second condition of squash: the block or wall is in squash radius

        bool twoBlockContrast = false;
        bool blockWallContrast = false;

        int faceIndex = 0;
        foreach (FaceDetector face in faceDetectorList)
        {
            if (face.hasCandidate)
            {
                //check the opposite direction
                int oppositeFaceIndex = (faceIndex + 2) % 4;

                twoBlockContrast = faceDetectorList[oppositeFaceIndex].hasCandidate;

                Vector3 detectWorldPos = playerTransform.TransformPoint(faceDetectorList[oppositeFaceIndex]
                    .pointDetector[0]
                    .detectPos);
                blockWallContrast = GlobalParameters.Instance.OutOfScreen(detectWorldPos);
                
            }

            faceIndex++;
        }

        if (blockWallContrast)
        {
            Vector3 checkPos = new Vector3(Mathf.Abs(squashCentre.position.x), Mathf.Abs(squashCentre.position.y), 0) -
                               new Vector3(1, 1, 0) * squashRadius;
            if (GlobalParameters.Instance.OutOfScreen(checkPos))
            {
                if (playerAlive)
                {
                    PlayerDie();
                    return;
                }
            }
        }

        if (twoBlockContrast || blockWallContrast)
        {
            squashColliderArr = Physics2D.OverlapCircleAll(squashCentre.position, squashRadius, m_WhatIsGround);
            if (squashColliderArr.Length >= 1)
            {
                if (playerAlive)
                {
                    PlayerDie();
                }
            }
        }
    }

    private void CheckCollision()
    {
        for (int i = 0; i < faceDetectorList.Count; i++)
        {
            CheckFace(i);
        }
    }

    private Collider2D[] firstCorner;
    private Collider2D[] secondCorner;
    private void CheckFace(int index)
    {
        faceDetectorList[index].collided = false;
        faceDetectorList[index].hasCandidate = false;

        Vector2 originPos1 = playerTransform.TransformPoint(faceDetectorList[index].pointDetector[0].detectPos);

        Vector2 originPos2 = playerTransform.TransformPoint(faceDetectorList[index].pointDetector[1].detectPos);

        firstCorner = Physics2D.OverlapCircleAll(originPos1, k_DetectorRadius, m_WhatIsGround);

        secondCorner = Physics2D.OverlapCircleAll(originPos2, k_DetectorRadius, m_WhatIsGround);

        if (firstCorner.Length > 0 || secondCorner.Length > 0)
        {
            faceDetectorList[index].collided = true;
            for (int i = 0; i < firstCorner.Length; i++)
            {
                for (int j = 0; j < secondCorner.Length; j++)
                {
                    if (firstCorner[i].gameObject == secondCorner[j].gameObject)
                    {
                        faceDetectorList[index].hasCandidate = true;
                        faceDetectorList[index].pointDetector[0].stickableObj = firstCorner[i].gameObject;
                        faceDetectorList[index].pointDetector[1].stickableObj = firstCorner[i].gameObject;
                    }
                }
            }
        }
        else
        {
            faceDetectorList[index].pointDetector[0].stickableObj = null;
            faceDetectorList[index].pointDetector[1].stickableObj = null;
        }
    }

    private void UpdateState()
    {
        m_Grounded = faceDetectorList[(int)DetectorEnum.Bottom].collided ||
                     (gravity < 0 && faceDetectorList[(int)DetectorEnum.Upper].collided);

        m_Walled = faceDetectorList[(int)DetectorEnum.Right].collided ||
                   faceDetectorList[(int)DetectorEnum.Left].collided;

        if (m_Grounded)
        {
            if (!jumpCooling)
            {
                //this means player landed after a jump
                //function after land
                LandAfterJump();
            }

            jumpCooling = true;
        }
    }
    
    private ChunkClass currentChunkClass;
    private ChunkClass formerChunkClass;
    private void AssignCandidate(float move)
    {
        if (Mathf.Abs(move) > 0.2f && faceDetectorList[(int)DetectorEnum.Right].hasCandidate)
        {
            candidateObj = faceDetectorList[(int)DetectorEnum.Right].pointDetector[0].stickableObj;
        }
        else if (faceDetectorList[(int)DetectorEnum.Bottom].hasCandidate)
        {
            candidateObj = faceDetectorList[(int)DetectorEnum.Bottom].pointDetector[0].stickableObj;
        }
        else
        {
            candidateObj = null;
        }


        if (candidateObj != null)
        {
            if (candidateObj != previousCandidateObj)
            {
                if (previousCandidateObj != null)
                {
                    formerChunkClass = GlobalParameters.Instance.ReturnChunkByObj(previousCandidateObj);
                    if (formerChunkClass  != null)
                    {
                        formerChunkClass.PlayerRemoveHighLight(previousCandidateObj);
                    }
                }
                
                currentChunkClass = GlobalParameters.Instance.ReturnChunkByObj(candidateObj);

                if (currentChunkClass != null)
                {
                    if(previousCandidateObj != null)
                        currentChunkClass.PlayerRemoveHighLight(previousCandidateObj);
                    currentChunkClass.PlayerAddHighLight(candidateObj);
                    AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.PlayerSwitchBlock);
                }

                if (currentChunkClass != formerChunkClass)
                {
                    if(formerChunkClass != null)
                       formerChunkClass.UnsubscribeToAttachEvent(PlayerMoveByAttach, ChunkUpdateByAttach);
                    currentChunkClass.SubscribeToAttachEvent(PlayerMoveByAttach, ChunkUpdateByAttach);
                    formerChunkClass = currentChunkClass;
                }
                
                previousCandidateObj = candidateObj;
            }
        }
        else
        {
            if (formerChunkClass != null && previousCandidateObj != null)
            {
                formerChunkClass.PlayerRemoveHighLight(previousCandidateObj);
                formerChunkClass.UnsubscribeToAttachEvent(PlayerMoveByAttach, ChunkUpdateByAttach);
            }

            formerChunkClass = null;
            previousCandidateObj = null;
        }
    }

    #endregion

    #region Player action API

    public void StampCandidate()
    {
        if (candidateObj != null)
        {
            if (currentChunkClass.OperateTool(candidateObj, playerIndex, BagManager.Instance.PresentSelectedBagTool()))
                GameManager.Instance.IncrementPlayerOperationCount();
        }
    }

    private float wallJumpAirTimer;

    public void Move(float move, bool jump)
    {
        AssignCandidate(move * playerSpeed);
        //only control the player if grounded or airControl is turned on
        if (m_Grounded || m_AirControl)
        {
            InputAffectedMove(move * playerSpeed);

            PropAffectedMove(move * playerSpeed);

            playerAnimator.SetFloat("HorizontalSpeed", Mathf.Abs(move));


            // If the input is moving the player right and the player is facing left...
            if (move * playerSpeed > 0 && !m_FacingRight)
            {
                // ... flip the player.
                Flip();
            }
            // Otherwise if the input is moving the player left and the player is facing right...
            else if (move * playerSpeed < 0 && m_FacingRight)
            {
                // ... flip the player.
                Flip();
            }
        }


        if (jump)
        {
            jumpTimer = jumpBufferTime;
        }

        TimeBufferJump();

        if (!m_Grounded && jumpCooling)
        {
            edgeJumpTimer = edgeJumpBufferTime;
            jumpCooling = false;
        }

        EdgeJump(jump);

        ApplyBound();
        ApplyGravity();
    }

    public void HalfJump(bool half)
    {
        halfJump = half;
    }

    private void InputAffectedMove(float horizontalMove)
    {
        //let the character fly with momentum of wall jump direction
        //for a while
        if (wallJumpAirTimer > 0)
        {
            wallJumpAirTimer -= Time.fixedDeltaTime;
        }
        else
        {
            Vector3 targetVelocity = new Vector2(horizontalMove, m_Rigidbody2D.velocity.y);
            m_Rigidbody2D.velocity = Vector3.SmoothDamp(m_Rigidbody2D.velocity, targetVelocity, ref m_Velocity,
                m_MovementSmoothing);
        }
    }

    private void TimeBufferJump()
    {
        if (jumpTimer > 0)
        {
            jumpTimer -= Time.fixedDeltaTime;
            if (m_Grounded || canJumpOnWall)
            {
                if (canJumpOnWall)
                    wallJumpAirTimer = 0.1f;

                jumpTimer = -1;
                ApplyJump();
            }
        }
    }

    private void EdgeJump(bool jump)
    {
        if (edgeJumpTimer > 0)
        {
            edgeJumpTimer -= Time.fixedDeltaTime;
            if (jump)
            {
                edgeJumpTimer = -1;
                ApplyJump();
            }
        }
    }

    #endregion

    #region Prop affected movement

    private float jumpOnWallBufferTime = 0.2f;
    private float jumpOnWallTimer;
    private void PropAffectedMove(float horizontalMove)
    {
        jumpDirection = new Vector2(0, 1);
        if (jumpOnWallTimer > 0)
        {
            jumpOnWallTimer -= Time.fixedDeltaTime;
        }
        else
        {
            canJumpOnWall = false;
        }
        
        if (m_Grounded)
        {
            PropAffectedHorizontalMovement(DetectorEnum.Bottom);
            PropAffectedJumpDir(DetectorEnum.Bottom);
        }
        
        if (faceDetectorList[(int)DetectorEnum.Right].collided && Mathf.Abs(horizontalMove) > 0)
        {
            canJumpOnWall = true;
            jumpOnWallTimer = jumpOnWallBufferTime;
            PropAffectedHorizontalMovement(DetectorEnum.Right);
            PropAffectedJumpDir(DetectorEnum.Right);
            SlideOnWall();
        }
    }

    private void PropAffectedHorizontalMovement(DetectorEnum detector)
    {
        GameObject tempStickableObj = null;
        for (int i = 0; i < 2; i++)
        {
            tempStickableObj = faceDetectorList[(int)detector].pointDetector[i].stickableObj;
            if (tempStickableObj != null)
            {
                if(detector == DetectorEnum.Bottom)
                  playerTransform.position += ReturnChunkAccumulatedMove(tempStickableObj) * Time.fixedDeltaTime;
                else
                {
                    playerTransform.position +=
                        new Vector3(
                            ReturnChunkAccumulatedMove(tempStickableObj).x * Time.fixedDeltaTime,
                            0, 0);
                }
                break;
            }
        }
    }
    
    private void PropAffectedJumpDir(DetectorEnum detector)
    {
        switch (detector)
        {
            case DetectorEnum.Bottom:
                jumpDirection = upsideDown ? new Vector2(0, -1):new Vector2(0, 1);
                break;
            case DetectorEnum.Right:
                jumpDirection = playerTransform.TransformDirection(new Vector2(Mathf.Cos(Mathf.PI - jumpRadiance),
                    Mathf.Sin(Mathf.PI - jumpRadiance))) * 1.3f;
                break;
        }
        
    }

    private void SlideOnWall()
    {
        if (m_Rigidbody2D.velocity.y <= 0 && !upsideDown)
            m_Rigidbody2D.velocity = new Vector2(m_Rigidbody2D.velocity.x, -2) + SlideOnWallPropAffect();
           

        if (m_Rigidbody2D.velocity.y >= 0 && upsideDown)
            m_Rigidbody2D.velocity = new Vector2(m_Rigidbody2D.velocity.x, 2) + SlideOnWallPropAffect();
    }

    private Vector2 SlideOnWallPropAffect()
    {
        GameObject tempStickableObj;
        for (int i = 0; i < 2; i++)
        {
            tempStickableObj = faceDetectorList[(int)DetectorEnum.Right].pointDetector[i].stickableObj;
            if (tempStickableObj != null)
            {
                return new Vector2(0, ReturnChunkAccumulatedMove(tempStickableObj).y);
            }
        }

        return Vector2.zero;
    }

    private void ApplyBound()
    {
        if (playerTransform.position.x >= GameConst.horizontalBound && m_Rigidbody2D.velocity.x >= 0)
        {
            m_Rigidbody2D.velocity = new Vector2(0, m_Rigidbody2D.velocity.y);
            playerTransform.position =
                new Vector2(GameConst.horizontalBound, playerTransform.position.y);
        }


        if (playerTransform.position.x <= -GameConst.horizontalBound && m_Rigidbody2D.velocity.x <= 0)
        {
            m_Rigidbody2D.velocity = new Vector2(0, m_Rigidbody2D.velocity.y);
            playerTransform.position =
                new Vector2(-GameConst.horizontalBound, playerTransform.position.y);
        }


        if (playerTransform.position.y >= GameConst.verticalBound && m_Rigidbody2D.velocity.y >= 0)
        {
            m_Rigidbody2D.velocity = new Vector2(m_Rigidbody2D.velocity.x, 0);
            playerTransform.position = new Vector2(playerTransform.position.x, GameConst.verticalBound);

            if (upsideDown)
                PlayerDie();
        }


        if (playerTransform.position.y <= -GameConst.verticalBound && m_Rigidbody2D.velocity.y <= 0)
        {
            m_Rigidbody2D.velocity = new Vector2(m_Rigidbody2D.velocity.x, 0);
            playerTransform.position =
                new Vector2(playerTransform.position.x, -GameConst.verticalBound);
            if (!upsideDown)
                PlayerDie();
        }
    }

    private Vector3 ReturnChunkAccumulatedMove(GameObject obj)
    {
        ChunkClass chunk = GlobalParameters.Instance.ReturnChunkByObj(obj);

        if (chunk == null) return new Vector3(0, 0, 0);

        return chunk.accumulatedMove;
    }

    #endregion

    private void ApplyJump()
    {
        m_Grounded = false;
        m_Walled = false;
        canJumpOnWall = false;
        applyedJump = true;
        jumpReachPeak = false;
        jumpCooling = false;

        if (jumpAnimation != null)
        {
            StopCoroutine(jumpAnimation);
            playerAnimator.SetBool("Jump", false);
        }

        jumpAnimation = JumpAnimation();
        StartCoroutine(jumpAnimation);
        PlayDustEffect();
        AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.PlayerJump);

        Vector2 velocity = m_Rigidbody2D.velocity;
        if (velocity.y != 0)
            velocity = new Vector2(velocity.x, -velocity.y);

        m_Rigidbody2D.velocity += velocity + jumpDirection * JumpSpeed();
    }

    private void ApplyGravity()
    {
        if (halfJump)
        {
            if (gravity < 0)
                gravity = -2 * standardGravity;
            else
            {
                gravity = 2 * standardGravity;
            }
        }
        else
        {
            if (gravity < 0)
                gravity = -standardGravity;
            else
            {
                gravity = standardGravity;
            }
        }

        m_Rigidbody2D.velocity += new Vector2(0, -gravity) * Time.fixedDeltaTime;
    }

    private void Flip()
    {
        // Switch the way the player is labelled as facing.
        m_FacingRight = !m_FacingRight;

        // Multiply the player's x local scale by -1.
        playerTransform.rotation *= Quaternion.AngleAxis(180, Vector3.up);
    }

    public void FlipByChunk(ToolDirection dir)
    {
        if (dir == ToolDirection.Original || dir == ToolDirection.Left)
        {
            playerSpeed *= -1;
        }
        else if (dir == ToolDirection.Up || dir == ToolDirection.Down)
        {
            upsideDown = !upsideDown;
            gravity *= -1;
        }
    }
    
    private GameObject checkpointIndicator;
    private bool playerHasCheckpoint = false;
    public void CreateCheckpointAction(GameObject checkpoint)
    {
        checkpointIndicator = checkpoint;
        playerHasCheckpoint = true;
    }

    public void ClearCheckpointAction()
    {
        playerHasCheckpoint = false;
        if(checkpointIndicator != null)
            Destroy(checkpointIndicator);
    }

    #region Alive & Death

    public void GotAttacked()
    {
        PlayerDie();
    }
    
    private void PlayerDie()
    {
        if (playerAlive && dieTaskDone)
        {
            playerAnimator.enabled = false;
            playerAlive = false;
            m_Rigidbody2D.Sleep();
            m_BoxCollider2D.enabled = false;
            AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.PlayerDie);
            
            if(candidateObj != null && currentChunkClass != null)
               currentChunkClass.PlayerRemoveHighLight(candidateObj);
            
            spriteRenderer.color = Color.clear;
            
            DieTask();
        }
    }

    private bool upsideDown = false;
    public void PlayerAlive()
    {
        //enable those components
        playerTransform.rotation = Quaternion.identity;
        playerSpeed = 10;
        gravity = standardGravity;
        upsideDown = false;
        m_FacingRight = true;
        playerAlive = true;
        playerAnimator.enabled = true;
        spriteRenderer.color = colorManager.ReturnColorByType(GameManager.Instance.playerList[playerIndex].playerColor);
        m_Rigidbody2D.WakeUp();
        m_BoxCollider2D.enabled = true;
    }

    private float dieSec = 0.6f;
    private float reviveDur = 0.5f;
    public bool dieTaskDone { get; private set; } = true;
    private Task cameraShakeTask;
    private Task playerExplodeTask;
    
    private async Task DieTask()
    {
        dieTaskDone = false;
        Vector3 diePos = playerTransform.position;
        
        //prevent getting killed twice by killer
        playerTransform.position = new Vector3(-100, -100, 0);

        cameraShakeTask = cameraBehavior.TriggerShake(dieSec);
        playerExplodeTask = customParticleEffect.PlayExplosion(diePos, dieSec);
        await Task.WhenAll(cameraShakeTask, playerExplodeTask);
        
        if (playerHasCheckpoint)
        {
            AudioManager.Instance.PlayerAudioSourcePlay(playerIndex, PlayerAudioEnum.PlayerRevive);
            await customParticleEffect.PlayRevive(checkpointIndicator.transform, reviveDur, ()=>
            {
                AudioManager.Instance.PlayerAudioSourceStop(playerIndex);
                playerTransform.position = checkpointIndicator.transform.position;
                dieTaskDone = true;
                PlayerAlive();
            });
            
            
        }
        else
        {
            customParticleEffect.ShowParticle(false);
            GameManager.Instance.SetResult(playerIndex, false);
            
            dieTaskDone = true;
            GameManager.Instance.RewindMapIfRequiredDead();
        }

        
    }

    #endregion

    private IEnumerator jumpAnimation;

    private IEnumerator JumpAnimation()
    {
        playerAnimator.SetBool("Jump", true);
        yield return new WaitForSeconds(jumpAnimDur);
        playerAnimator.SetBool("Jump", false);
    }

    private bool halfJump = false;
  
    private float JumpSpeed()
    {
        return Mathf.Sqrt(2 * jumpHeight * Mathf.Abs(standardGravity));
    }

    private void SetJumpReachPeak()
    {
        //call at every jump
        if (applyedJump)
        {
            applyedJump = false;
            presentVerticalMove = m_Rigidbody2D.velocity.y;
        }


        if (presentVerticalMove > 0 || (presentVerticalMove < 0 && upsideDown))
            presentVerticalMove = m_Rigidbody2D.velocity.y;
        else
        {
            presentVerticalMove = 0;
            jumpReachPeak = true;
        }
    }

    private void LandAfterJump()
    {
        landParticle.Play();
    }

    private void PlayDustEffect()
    {
        //jumpParticle.
        jumpParticle.Play();
    }

    private void PlayerMoveByAttach(Vector3 offset)
    {
        playerTransform.position += offset;
    }

    private void ChunkUpdateByAttach()
    {
        if (candidateObj != null)
        {
            if (formerChunkClass != null)
            {
                formerChunkClass.PlayerRemoveHighLight(candidateObj);
                formerChunkClass.UnsubscribeToAttachEvent(PlayerMoveByAttach, ChunkUpdateByAttach);
            }
               
            formerChunkClass = null;
            previousCandidateObj = null;
        }

    }

    private float idle2Interval = 1;
    private float idle2IntervalMax = 2f;
    private float idle2IntervalMin = 0.8f;
    
    private void SetIdle2True()
    {
        if (idle2Interval > 0)
        {
            idle2Interval -= Time.fixedDeltaTime;
        }
        else
        {
            StartCoroutine(Idle2Anim());
            idle2Interval = Random.Range(idle2IntervalMin, idle2IntervalMax);
        }
            
    }

    private IEnumerator Idle2Anim()
    {
        playerAnimator.SetBool("Idle2", true);
        yield return new WaitForSeconds(0.2f);
        playerAnimator.SetBool("Idle2", false);
    }
    
    private void OnDrawGizmos()
    {
        Gizmos.color = Color.yellow;
    
        foreach (FaceDetector detector in faceDetectorList)
        {
            for (int i = 0; i < 2; i++)
            {
                Vector2 originPos = transform.TransformPoint(detector.pointDetector[i].detectPos);
                Gizmos.DrawSphere(originPos, k_DetectorRadius);
            }
        }
        
        //Gizmos.DrawSphere(squashCentre.position, squashRadius);
        
    }

}