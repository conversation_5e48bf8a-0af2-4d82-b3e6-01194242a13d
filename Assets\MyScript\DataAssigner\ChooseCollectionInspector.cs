using System.Linq;
using UnityEngine;

public class ChooseCollectionInspector : InspectorDataAssigner
{
    [SerializeField] private GameObject imgObj;
    [SerializeField] private TextContentInspector collectionInspector;
    private CollectionClass currentCollection;
    
    private string emptyText = "";
    

    public override void ShowCollectionInspector(CollectionClass collection, MapOrCollectionItemDataAssigner itemDataAssigner)
    {
        currentCollection = collection;
        collectionInspector.ShowCollectionTextInspector(collection);
        AssignData();
    }
    
    
    public override void ClearInspector()
    {
        collectionInspector.HideTextInspector();
    }

    public void PlayCollection()
    {
        if (currentCollection != null)
        {
            GameManager.Instance.SelectCollection(FilterBadMapCollection());
            GameManager.Instance.PickLevelStartAction();
            currentCollection = null;
        }
    }

    private CollectionClass FilterBadMapCollection()
    {
        CollectionClass collection = GlobalMethod.DeepClone(currentCollection);
        collection.mapIDList =
            collection.mapIDList.Where(i => MapExistAndFitCollectionCondition(i, collection)).ToList();
        return collection;
    }

    private bool MapExistAndFitCollectionCondition(int mapID, CollectionClass collection)
    {
        MapPreview map = GlobalParameters.Instance.ReturnMapPreviewByID(mapID);
        if (map == null)
            return false;

        return map.FitCollection(collection);
    }
    
    
}
