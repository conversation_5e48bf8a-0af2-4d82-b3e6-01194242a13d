using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using Unity.Mathematics;

public class CutSceneController : MonoBehaviour
{
    [SerializeField] private Material spriteMat;
    [SerializeField] private GameObject starGlowPrefab;
    [SerializeField] private Transform stickableObjs;
    
    private CutSceneRequest currentReq;

    private class PlayerTransform
    {
        public PlayerTransform(Transform t)
        {
            playerT = t;
            formerPos = t.position;
        }
        public Transform playerT;
        public Vector3 formerPos;
    }
    
    private List<PlayerTransform> playerTransforms = new List<PlayerTransform>();

    public bool cutSceneFinished
    {
        private set;
        get;
    }
    
    private void Start()
    {
        cutSceneFinished = true;
    }

    public IEnumerator RequestCutScene(CutSceneRequest req)
    {
        cutSceneFinished = false;
        GameManager.Instance.StateButtonAction((int)StateEnum.GamePlay);
        Time.timeScale = 0;
        GameManager.Instance.SetInputMap("UI");
        currentReq = req;

        if (req.playerNextPositions != null && req.winnerPlayerIndex != -1)
        {
            GetPlayerTransforms();
        }
        

        if (req.hasCurtain)
        {
            rebuildFinished = true;
            yield return CurtainAnimation();
        }
        else
        {
            if (currentReq.showWinner)
            {
                StartCoroutine(CompetitiveModeWinnerAnimation());
            }
            else
            {
                StartCoroutine(LevelResultAnimation());
            }

            StartCoroutine(PlayerMoveToCenter());
            yield return StarAnimation();
            
            yield return RebuildMapAnimation();
        }
        
        if (starGlowObj != null)
            starGlowObj.SetActive(false);

        if (currentReq.hasCountdown)
            yield return MapCountDown();
        
        Time.timeScale = 1;
        GameManager.Instance.SetInputMap("Player");

        cutSceneFinished = true;
    }

    private float curtainDur = 1;
    private IEnumerator CurtainAnimation()
    {
        spriteMat.SetFloat("_Fill",  -1);
        GlobalParameters.Instance.LoadMap(currentReq.nextMapID);
        GlobalParameters.Instance.ResetLevel();
        
        yield return new WaitForSecondsRealtime(.3f);
        float timer = curtainDur;
        while (timer >=0)
        {
            spriteMat.SetFloat("_Fill",  -1 + 2*(curtainDur - timer)/curtainDur);
            timer -= Time.unscaledDeltaTime;
            yield return null;
        }
        
        spriteMat.SetFloat("_Fill",  1);
    }

    private IEnumerator PlayerMoveToCenter()
    {
        WaitForSecondsRealtime animUpdateInterval = new WaitForSecondsRealtime(GameConst.animUpdateInterval);
        
        WaitForSecondsRealtime wait = new WaitForSecondsRealtime(GameConst.completeLevelStarAnimDur - GameConst.completeLevelPlayerMoveDur);
        yield return wait;
        float dur = GameConst.completeLevelPlayerMoveDur;
        float starAnimTimer = 0;
        while (starAnimTimer < dur)
        {
            starAnimTimer += GameConst.animUpdateInterval;
            Vector3 ridge = GetRidge(playerTransforms[currentReq.winnerPlayerIndex].formerPos, new Vector3(0, 5, 0));
            playerTransforms[currentReq.winnerPlayerIndex].playerT.position = ParabolaCurve(playerTransforms[currentReq.winnerPlayerIndex].formerPos, new Vector3(0, 5, 0), ridge ,starAnimTimer/dur);
            playerTransforms[currentReq.winnerPlayerIndex].playerT.localScale = Vector3.Lerp(Vector3.one, Vector3.one*3.5f, starAnimTimer/GameConst.completeLevelStarAnimDur);
            yield return animUpdateInterval;
        }

        playerTransforms[currentReq.winnerPlayerIndex].formerPos = playerTransforms[currentReq.winnerPlayerIndex].playerT.position;
    }

    private GameObject starGlowObj;
    private IEnumerator StarAnimation()
    {
        if (starGlowObj == null)
        {
            starGlowObj = Instantiate(starGlowPrefab, currentReq.targetObject.transform.position, quaternion.identity);
        }

        starGlowObj.transform.rotation = Quaternion.identity;
        starGlowObj.transform.position = currentReq.targetObject.transform.position;
        starGlowObj.transform.SetParent(currentReq.targetObject.transform);
        starGlowObj.SetActive(true);

        WaitForSecondsRealtime startAnimDur = new WaitForSecondsRealtime(GameConst.completeLevelStarAnimDur);
        yield return startAnimDur;

        if (starGlowObj != null)
            starGlowObj.SetActive(false);
    }
    
    [SerializeField] private LevelResultAssigner levelResultAssigner;
    private IEnumerator LevelResultAnimation()
    {
        levelResultAssigner.gameObject.SetActive(true);
        bool timeNewRecord = currentReq.timeNewRecord;
        bool operationNewRecord = currentReq.operationNewRecord;
        
        WaitForSecondsRealtime completeLevelDur = new WaitForSecondsRealtime(GameConst.completeLevelDur);

        levelResultAssigner.ShowLevelResult(currentReq.operation, currentReq.time, operationNewRecord,
            timeNewRecord);
        yield return completeLevelDur;
        levelResultAssigner.gameObject.SetActive(false);
    }
    
    [SerializeField] private GameObject mapWinnerObj;
    private IEnumerator CompetitiveModeWinnerAnimation()
    {
        WaitForSecondsRealtime completeLevelDur = new WaitForSecondsRealtime(GameConst.completeLevelDur);
        mapWinnerObj.SetActive(true);
        mapWinnerObj.GetComponent<TMP_Text>().color =
            GameManager.Instance.ReturnInGamePlayerColorByIndex(currentReq.winnerPlayerIndex);
        yield return completeLevelDur;
        mapWinnerObj.SetActive(false);
    }
    
    private bool rebuildFinished = false;
    private IEnumerator RebuildMapAnimation()
    {
        rebuildFinished = false;
        WaitForSecondsRealtime animUpdateInterval = new WaitForSecondsRealtime(GameConst.animUpdateInterval);
        //animate the old map to shrink
        float moveOutDur = 0.3f*GameConst.completeLevelDur;
        float counter = 0;
        while (counter < moveOutDur)
        {
            counter += GameConst.animUpdateInterval;
            stickableObjs.position = Vector3.Lerp(Vector3.zero, new Vector3(-40, 0, 0), counter / moveOutDur);
            yield return animUpdateInterval;
        }
        
        //GLobalParameters set map
        stickableObjs.gameObject.SetActive(false);
        stickableObjs.position = new Vector3(0, 0, 0);
        GlobalParameters.Instance.LoadMap(currentReq.nextMapID);
        GlobalParameters.Instance.ResetLevel();
        stickableObjs.position = new Vector3(40, 0, 0);
        stickableObjs.gameObject.SetActive(true);
        
        
        //show map gradually
        float leftTime = GameConst.completeLevelDur - counter - GameConst.completeLevelStarAnimDur*0.6f;
        
        float rebuildTimer = 0;
        while (rebuildTimer < leftTime)
        {
            rebuildTimer += GameConst.animUpdateInterval;
            SetPlayersParabolaCurve(rebuildTimer / leftTime);
            playerTransforms[currentReq.winnerPlayerIndex].playerT.localScale = Vector3.Lerp(Vector3.one*3.5f, Vector3.one,rebuildTimer/leftTime);
            stickableObjs.position = Vector3.Lerp(new Vector3(40, 0, 0), Vector3.zero, rebuildTimer/leftTime);
            yield return animUpdateInterval;
        }

        playerTransforms[currentReq.winnerPlayerIndex].playerT.localScale = Vector3.one;
        rebuildFinished = true;
    }
    
    [SerializeField] private GameObject countDownObj;
    private TMP_Text countDownText;
    private WaitForSecondsRealtime oneSecond = new WaitForSecondsRealtime(1);
    private IEnumerator MapCountDown()
    {
        int count = 3;
        
        countDownObj.SetActive(true);
        if (countDownText == null)
            countDownText = countDownObj.transform.Find("CountDownText").GetComponent<TMP_Text>();
        while (count > 0)
        {
            countDownText.text = count.ToString();
            count--;
            yield return oneSecond;
        }
        
        countDownObj.SetActive(false);
    }
    
    private Vector3 ParabolaCurve(Vector3 startPos, Vector3 endPos, Vector3 ridge, float f)
    {
        Vector3 point = startPos;

        Vector3 a = Vector3.Lerp(startPos, ridge, f);
        Vector3 b = Vector3.Lerp(ridge, endPos, f);

        return Vector3.Lerp(a, b, f);
    }

    private Vector3 GetRidge(Vector3 startPos, Vector3 endPos)
    {
        float ridgeHeight = 0.5f*Vector3.Distance(startPos, endPos);
        Vector3 moveDir = (endPos - startPos).normalized;
        Vector3 ridgeDir = Vector3.zero;
        if (moveDir.x <= 0)
        {
            ridgeDir = new Vector3(moveDir.y, -moveDir.x, 0);
        }
        else
        {
            ridgeDir = new Vector3(-moveDir.y, moveDir.x, 0);
        }

        return ridgeDir * ridgeHeight;
    }

    private void GetPlayerTransforms()
    {
        playerTransforms.Clear();
        for (int i = 0; i < currentReq.activePlayerCount; i++)
        {
            playerTransforms.Add(new PlayerTransform(GameManager.Instance.playerList[i].playerObj.transform));
        }
    }

    private void SetPlayersParabolaCurve(float f)
    {
        for (int i = 0; i < playerTransforms.Count; i++)
        {
            Vector3 ridge = GetRidge(playerTransforms[i].formerPos, currentReq.playerNextPositions[i]);
            playerTransforms[i].playerT.position = ParabolaCurve(playerTransforms[i].formerPos, currentReq.playerNextPositions[i], ridge ,f);
        }
    }
}




