using System;
using System.Collections.Generic;
using System.Linq;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Steamworks.Data;
using Color = UnityEngine.Color;

public class LeaderboardEntryAssigner : MonoBehaviour
{
    //each map data
    #region Map data

    [SerializeField] private Transform entryContent;
    [SerializeField] private GameObject entryItemTemplate;
    [SerializeField] private RectTransform upperBound;
    [SerializeField] private RectTransform lowerBound;

    private int maxEntryItemCount = 13;

    private class EntryItem
    {
        public EntryItem(GameObject obj, int index)
        {
            UIObj = obj;
            rectTransform = obj.GetComponent<RectTransform>();
            entryIndex = index;
            rankTMP = obj.transform.Find("Rank").GetComponent<TMP_Text>();
            playerNameTMP = obj.transform.Find("Name").GetComponent<TMP_Text>();
            valueTMP = obj.transform.Find("Value").GetComponent<TMP_Text>();
        }

        public GameObject UIObj;
        public RectTransform rectTransform;
        public int entryIndex;
        public TMP_Text rankTMP;
        public TMP_Text playerNameTMP;
        public TMP_Text valueTMP;
    }

    private List<EntryItem> mapItemList = new List<EntryItem>();
    
    #endregion

    [SerializeField] private DropdownStateComponent valueTypeDropdown;
    [SerializeField] private DropdownStateComponent rangeTypeDropdown;
    
    [Serializable]
    private class SelectOptionClass
    {
        [Tooltip("必须是enum中的值")] public string name;
        [Tooltip("必须是localization中的key")] public string localizationKey;
    }

    [SerializeField] private List<SelectOptionClass> valueTypeList = new List<SelectOptionClass>();
    [SerializeField] private List<SelectOptionClass> rangeTypeList = new List<SelectOptionClass>();

    [SerializeField] private GameObject emptyReasonTMP;

    [SerializeField] private TMP_FontAsset chineseFont;
    [SerializeField] private TMP_FontAsset englishFont;
    

    private string currentLeaderboardName;
    private LeaderboardValueType currentLbValueType;
    private LeaderboardRange currentRange;
    private bool hasInitialized = false;
    private LeaderboardEntry[] lbEntryArr;

    private ColorManager colorManager;

    public async void ShowLeaderboard(string lbName)
    {
        InitParameters();

        lbName = GlobalMethod.ChangeLbNameByType(lbName, currentLbValueType);

        posDiff = 0;
        if (currentLeaderboardName != lbName)
        {
            lbEntryArr = await SteamIntegration.Instance.GetLeaderboardByRange(lbName, currentRange);
            currentLeaderboardName = lbName;
            if (lbEntryArr == null)
                lbEntryArr = Array.Empty<LeaderboardEntry>();
        }

        //show each map
        ShowEntry();
        
        ShowEmptyReason(lbEntryArr.Length == 0, lbName);
        
    }
    

    private void InitParameters()
    {
        if (!hasInitialized)
        {
            contentRectTransform = entryContent.GetComponent<RectTransform>();
            mapVerticalLayoutGroup = entryContent.GetComponent<VerticalLayoutGroup>();
            for (int i = 0; i < maxEntryItemCount; i++)
            {
                if (i == 0)
                {
                    mapItemList.Add(new EntryItem(entryItemTemplate, i));
                }
                else
                {
                    mapItemList.Add(new EntryItem(Instantiate(entryItemTemplate, entryContent), i));
                }
            }

            valueTypeDropdown.InitDropdown(valueTypeList.Select(i => i.localizationKey).ToList(),
                ValueTypeChangeAction);
            rangeTypeDropdown.InitDropdown(rangeTypeList.Select(i => i.localizationKey).ToList(), RangeTypeChangeAction);
            hasInitialized = true;
        }
        
    }

    private void ValueTypeChangeAction(int index)
    {
        currentLbValueType = (LeaderboardValueType)index;
        ShowLeaderboard(currentLeaderboardName);
    }
    
    private void RangeTypeChangeAction(int index)
    {
        currentRange = (LeaderboardRange)index;
        ShowLeaderboard(currentLeaderboardName);
    }
    
    #region Entry data

    private int entryCount;
    private RectTransform contentRectTransform;
    private float entryItemHeight = 60;
    
    private int headPointer;
    private int rearPointer;

    private bool entryCanScroll = false;

    private void ShowEntry()
    {
        SetEntryParameters();
        for (int i = 0; i < maxEntryItemCount; i++)
        {
            AssignOneEntry(i, i);
        }
    }

    private void SetEntryParameters()
    {
        entryCount = lbEntryArr.Length;
        contentRectTransform.sizeDelta = new Vector2(contentRectTransform.sizeDelta.x, entryCount*entryItemHeight);
        contentRectTransform.anchoredPosition = new Vector2(contentRectTransform.anchoredPosition.x, 0);
        headPointer = 0;
        rearPointer = entryCount >= maxEntryItemCount ? maxEntryItemCount - 1 : entryCount - 1;
    }

    private void AssignOneEntry(int objIndex, int sequence)
    {
        
        if (sequence >= 0 && sequence < entryCount)
        {
            mapItemList[objIndex].UIObj.SetActive(true);
            mapItemList[objIndex].entryIndex = sequence;
            mapItemList[objIndex].rankTMP.text = lbEntryArr[sequence].GlobalRank.ToString();

            UpdateFont(mapItemList[objIndex].playerNameTMP, lbEntryArr[sequence].User.Name);
            mapItemList[objIndex].playerNameTMP.text = lbEntryArr[sequence].User.Name;
            mapItemList[objIndex].valueTMP.text = ValueText(lbEntryArr[sequence].Score);
            UpdateEntryColor(objIndex, sequence);
        }
        else
        {
            mapItemList[objIndex].UIObj.SetActive(false);
        }
    }

    private void UpdateEntryColor(int objIndex, int sequence)
    {
        Color entryColor = Color.white;
        if (lbEntryArr[sequence].User.IsMe)
            entryColor = ReturnColorByType(ColorEnum.UIButtonHighLight);
        mapItemList[objIndex].rankTMP.color = entryColor;
        mapItemList[objIndex].playerNameTMP.color = entryColor;
        mapItemList[objIndex].valueTMP.color = entryColor;
    }

    private string ValueText(int value)
    {
        return currentLbValueType == LeaderboardValueType.Time ? ConvertMilliseconds(value) : value.ToString();
    }

    private string ConvertMilliseconds(int value)
    {
        TimeSpan time = TimeSpan.FromMilliseconds(value);
        string formatted = time.ToString(@"hh\:mm\:ss\.fff");

        Console.WriteLine(formatted);

        return formatted;
    }
    
    private void ShowEmptyReason(bool show, string lbName)
    {
        emptyReasonTMP.SetActive(show);
        if (show)
        {
            int chapterIndex = GlobalMethod.ReturnChapterIndexByLbName(lbName);

            if (chapterIndex > 3)
            {
                emptyReasonTMP.GetComponent<TMP_Text>().text = $"{MyLocalizationManager.Instance.RetrieveStringByKey("NoOneFinished")} {MyLocalizationManager.Instance.RetrieveStringByKey("Story")}";
            }
            else
            {
                emptyReasonTMP.GetComponent<TMP_Text>().text = $"{MyLocalizationManager.Instance.RetrieveStringByKey("NoOneFinished")} {MyLocalizationManager.Instance.RetrieveStringByKey("Chapter")} {chapterIndex}";
            }
        }
        
    }

    private Color ReturnColorByType(ColorEnum cType)
    {
        if (colorManager == null)
            colorManager = GameManager.Instance.ReturnColorManager();
        return colorManager.ReturnColorByType(cType);
    }

    private void UpdateFont(TMP_Text t, string s)
    {
        t.font = GlobalMethod.ContainsChineseCharacters(s) ? chineseFont : englishFont;
    }
    

    #region Scroll move && arrange
    private float changeStep = 60;

    private VerticalLayoutGroup mapVerticalLayoutGroup;
    
    private float contentPosY;
    private float posDiff;

    private void Update()
    {
        if (entryCanScroll)
        {
            CheckStep();
        }
    }
    
    private void CheckStep()
    {
        float diff = contentRectTransform.anchoredPosition.y - contentPosY;
        contentPosY = contentRectTransform.anchoredPosition.y;
        
        if (Mathf.Abs(diff) > 0.5f)
            posDiff = diff;
        

        if (upperBound == null || lowerBound == null)
            return;
        
        if (posDiff > 0 && mapItemList[headPointer].rectTransform.position.y > upperBound.position.y)
        {
            MoveItem(1);
        }else if (posDiff < 0 && mapItemList[rearPointer].rectTransform.position.y < lowerBound.position.y)
        {
            MoveItem(-1);
        }
    }

    private void MoveItem(int dir)
    {
        if (dir > 0)
        {
            //check if there is new data behind rear
            //the head button goes to rear, and take the new data
            int newDataIndex = mapItemList[rearPointer].entryIndex + 1;
            if (newDataIndex < entryCount)
            {
                mapItemList[headPointer].UIObj.transform.SetSiblingIndex(maxEntryItemCount - 1);
                AssignOneEntry(headPointer, newDataIndex);
                rearPointer = headPointer;
                headPointer = headPointer + 1 >= maxEntryItemCount ? 0 : headPointer + 1;
                mapVerticalLayoutGroup.padding.top += (int)changeStep;
            }
        }
        else
        {
            //check if there is new data before head
            //the rear goes to head, and take the new data
            int newDataIndex = mapItemList[headPointer].entryIndex - 1;
            if (newDataIndex >= 0)
            {
                mapItemList[rearPointer].UIObj.transform.SetSiblingIndex(0);
                AssignOneEntry(rearPointer, newDataIndex);
                headPointer = rearPointer;
                rearPointer = rearPointer - 1 < 0 ? maxEntryItemCount - 1 : rearPointer - 1;
                mapVerticalLayoutGroup.padding.top -= (int)changeStep;
            }
        }
    }
    #endregion

    #endregion
}
