using UnityEngine;

public class UnscaledTimeToShader : MonoBehaviour
{
    [SerializeField] private Material[] materialArray;


    // Update is called once per frame
    void Update()
    {
        foreach (Material mat in materialArray)
        {
            mat.SetFloat("_UnscaledTime", Time.unscaledTime);
        }
    }
    
    void OnApplicationQuit()
    {
        ResetShaderParameter();
    }

    private void ResetShaderParameter()
    {
        foreach (Material mat in materialArray)
        {
            mat.SetFloat("_UnscaledTime", 0);
        }
    }
}
