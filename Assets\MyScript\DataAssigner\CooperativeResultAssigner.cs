using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class CooperativeResultAssigner : MonoBehaviour
{
    //each map data
    #region Map data

    [SerializeField] private Transform mapContent;
    [SerializeField] private GameObject mapItemTemplate;
    [SerializeField] private RectTransform upperBound;
    [SerializeField] private RectTransform lowerBound;

    private int maxMapItemCount = 13;

    private class MapItem
    {
        public MapItem(GameObject obj, int index)
        {
            UIObj = obj;
            rectTransform = obj.GetComponent<RectTransform>();
            mapIndex = index;
            mapNameTMP = obj.transform.Find("MapName").GetComponent<TMP_Text>();
            mapOperationTMP = obj.transform.Find("Operation").GetComponent<TMP_Text>();
            mapTimeTMP = obj.transform.Find("Time").GetComponent<TMP_Text>();
        }

        public GameObject UIObj;
        public RectTransform rectTransform;
        public int mapIndex;
        public TMP_Text mapNameTMP;
        public TMP_Text mapOperationTMP;
        public TMP_Text mapTimeTMP;
    }

    private List<MapItem> mapItemList = new List<MapItem>();
    

    #endregion

    
    
    private List<LevelResult> collectionResultList;
    private CollectionClass currentCollection;
    private bool firstTime = true;

    public void ShowResult(List<LevelResult> resultList, CollectionClass collection)
    {
        collectionResultList = resultList;
        currentCollection = collection;

        if (firstTime)
        {
            InitParameters();
            firstTime = false;
        }

        posDiff = 0;

        //show record
        ShowAndCheckRecord();

        //show each map
        ShowMap();
        
        //add achievement
        if (SteamIntegration.Instance)
        {
            SteamIntegration.Instance.UnlockAchievement("3");
        }
           
    }

    private void InitParameters()
    {
        mapRectTransform = mapContent.GetComponent<RectTransform>();
        mapVerticalLayoutGroup = mapContent.GetComponent<VerticalLayoutGroup>();
        AssignRecordObj();
        for (int i = 0; i < maxMapItemCount; i++)
        {
            if (i == 0)
            {
                mapItemList.Add(new MapItem(mapItemTemplate, i));
            }
            else
            {
                mapItemList.Add(new MapItem(Instantiate(mapItemTemplate, mapContent), i));
            }
        }
    }



    #region Record management

    [SerializeField] private Transform timeRecordTransform;
    [SerializeField] private Transform operationRecordTransform;

    private TMP_Text timeValueTMP;
    private TMP_Text operationValueTMP;
    private GameObject newTimeRecordObj;
    private GameObject newOperationRecordObj;

    private void AssignRecordObj()
    {
        timeValueTMP = timeRecordTransform.Find("TimeValue").GetComponent<TMP_Text>();
        operationValueTMP = operationRecordTransform.Find("OperationValue").GetComponent<TMP_Text>();
        newTimeRecordObj = timeRecordTransform.Find("NewTimeRecord").gameObject;
        newOperationRecordObj = operationRecordTransform.Find("NewOperationRecord").gameObject;
    }

    private void ShowAndCheckRecord()
    {
        float currentTimeDur = ReturnResultTimeDur();
        int currentOperation = ReturnResultOperation();

        timeValueTMP.text = currentTimeDur.ToString("F1");
        operationValueTMP.text = currentOperation.ToString();
        
        newTimeRecordObj.SetActive(false);
        newOperationRecordObj.SetActive(false);

#if !UNITY_EDITOR 
        CheckTimeRecord(currentTimeDur);
        CheckOperationRecord(currentOperation);
#endif
    }

    private void CheckTimeRecord(float time)
    {
        if (currentCollection.timeDur > 0)
        {
            //compare
            if (currentCollection.timeDur <= time)
            {
                return;
            }
            
            //show new time record
            newTimeRecordObj.SetActive(true);
        }
        
        currentCollection.timeDur = time;
        GlobalParameters.Instance.SaveCollectionList();
    }

    private void CheckOperationRecord(int operation)
    {
        if (currentCollection.operationCount > 0)
        {
            if (currentCollection.operationCount <= operation)
                return;
            
            newOperationRecordObj.SetActive(true);
        }

        currentCollection.operationCount = operation;
        GlobalParameters.Instance.SaveCollectionList();
    }
    
    
    private float ReturnResultTimeDur()
    {
        float time = 0;
        foreach (LevelResult result in collectionResultList)
        {
            time += result.timeDur;
        }

        return time;
    }

    private int ReturnResultOperation()
    {
        int operation = 0;
        foreach (LevelResult result in collectionResultList)
        {
            operation += result.operationCount;
        }

        return operation;
    }
    

    #endregion
    

    #region Map data

    private int mapCount;
    private RectTransform mapRectTransform;
    private float mapItemHeight = 60;
    
    private int headPointer;
    private int rearPointer;

    private bool mapCanScroll = false;

    private void ShowMap()
    {
        SetMapParameters();
        for (int i = 0; i < maxMapItemCount; i++)
        {
            AssignOneMap(i, i);
        }
    }

    private void SetMapParameters()
    {
        mapCount = currentCollection.mapIDList.Count;
        mapRectTransform.sizeDelta = new Vector2(mapRectTransform.sizeDelta.x, mapCount*mapItemHeight);
        mapRectTransform.anchoredPosition = new Vector2(0, 0);
        headPointer = 0;
        rearPointer = mapCount >= maxMapItemCount ? maxMapItemCount - 1 : mapCount - 1;
    }

    private void AssignOneMap(int objIndex, int sequence)
    {
        if (sequence >= 0 && sequence < currentCollection.mapIDList.Count)
        {
            int mapID = currentCollection.mapIDList[sequence];
            int operation = collectionResultList[sequence].operationCount;
            float time = collectionResultList[sequence].timeDur;
            mapItemList[objIndex].UIObj.SetActive(true);
            mapItemList[objIndex].mapIndex = sequence;
            mapItemList[objIndex].mapNameTMP.text = GlobalParameters.Instance
                .ReturnMapPreviewByID(mapID).mapName;
            
            mapItemList[objIndex].mapOperationTMP.text = operation >= 0 ? operation.ToString() : "/";
            mapItemList[objIndex].mapTimeTMP.text = time >= 0 ? time.ToString("F1") : "/";
        }
        else
        {
            mapItemList[objIndex].UIObj.SetActive(false);
        }
    }


    #region Scroll move && arrange
    private float changeStep = 60;

    private VerticalLayoutGroup mapVerticalLayoutGroup;
    
    private float contentPosY;
    private float posDiff;

    private void Update()
    {
        if (mapCanScroll)
        {
            CheckStep();
        }
    }
    
    private void CheckStep()
    {
        float diff = mapRectTransform.anchoredPosition.y - contentPosY;
        contentPosY = mapRectTransform.anchoredPosition.y;
        
        if (Mathf.Abs(diff) > 0.5f)
            posDiff = diff;
        

        if (upperBound == null || lowerBound == null)
            return;
        
        if (posDiff > 0 && mapItemList[headPointer].rectTransform.position.y > upperBound.position.y)
        {
            MoveItem(1);
        }else if (posDiff < 0 && mapItemList[rearPointer].rectTransform.position.y < lowerBound.position.y)
        {
            MoveItem(-1);
        }
    }

    private void MoveItem(int dir)
    {
        if (dir > 0)
        {
            //check if there is new data behind rear
            //the head button goes to rear, and take the new data
            int newDataIndex = mapItemList[rearPointer].mapIndex + 1;
            if (newDataIndex < currentCollection.mapIDList.Count)
            {
                mapItemList[headPointer].UIObj.transform.SetSiblingIndex(maxMapItemCount - 1);
                AssignOneMap(headPointer, newDataIndex);
                rearPointer = headPointer;
                headPointer = headPointer + 1 >= maxMapItemCount ? 0 : headPointer + 1;
                mapVerticalLayoutGroup.padding.top += (int)changeStep;
            }
        }
        else
        {
            //check if there is new data before head
            //the rear goes to head, and take the new data
            int newDataIndex = mapItemList[headPointer].mapIndex - 1;
            if (newDataIndex >= 0)
            {
                mapItemList[rearPointer].UIObj.transform.SetSiblingIndex(0);
                AssignOneMap(rearPointer, newDataIndex);
                headPointer = rearPointer;
                rearPointer = rearPointer - 1 < 0 ? maxMapItemCount - 1 : rearPointer - 1;
                mapVerticalLayoutGroup.padding.top -= (int)changeStep;
            }
        }
    }
    #endregion

    #endregion
}
