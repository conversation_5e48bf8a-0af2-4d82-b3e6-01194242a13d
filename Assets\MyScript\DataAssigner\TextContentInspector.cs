using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;

public class TextContentInspector : MonoBehaviour
{
    [SerializeField] private TMP_Text nameTMP;
    [SerializeField] private TMP_Text playerTMP;
    [SerializeField] private TMP_Text modeTMP;
    [SerializeField] private TMP_Text descriptionTMP;
    
    [SerializeField] private GameObject notEditableInstructionUI;

    public void ShowMapTextInspector(MapPreview map)
    {
        gameObject.SetActive(true);
        nameTMP.text = map.mapName;
        playerTMP.text = map.playerCount == map.playerCountMax ? $"{map.playerCount}" : $"{map.playerCount}~{map.playerCountMax}";
        modeTMP.text = map.multiplayerMode.ToString();
        descriptionTMP.text = map.mapDescription;

        if (map.playerCount == 1 && map.playerCountMax == 1)
        {
            modeTMP.transform.parent.gameObject.SetActive(false);
        }
        else
        {
            modeTMP.transform.parent.gameObject.SetActive(true);
        }

        if (map.isBuiltin && !Application.isEditor)
        {
            notEditableInstructionUI.SetActive(true);
        }
        else
        {
            notEditableInstructionUI.SetActive(false);
        }
    }
    
    public void ShowCollectionTextInspector(CollectionClass collection)
    {
        gameObject.SetActive(true);
        nameTMP.text = collection.name;
        playerTMP.text = collection.playerCount == collection.playerCountMax ? $"{collection.playerCount}" : $"{collection.playerCount}~{collection.playerCountMax}";
        modeTMP.text = collection.multiplayerMode.ToString();
        descriptionTMP.text = collection.description;
        
        if (collection.isBuiltin && !Application.isEditor)
        {
            notEditableInstructionUI.SetActive(true);
        }
        else
        {
            notEditableInstructionUI.SetActive(false);
        }
    }

    public void HideTextInspector()
    {
        gameObject.SetActive(false);
        notEditableInstructionUI.SetActive(false);
    }
}
