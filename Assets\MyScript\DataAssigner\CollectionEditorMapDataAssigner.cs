using UnityEngine;
using TMPro;

public class CollectionEditorMapDataAssigner : MapDataAssigner
{
    [SerializeField] private MapDataAssigner popupMapDataAssigner;
    [SerializeField] private TMP_Text collectionNameTMP;
    [SerializeField] private CollectionEditorDataAssigner collectionEditorDataAssigner;

    public override void ShowMap(CollectionClass collection, MapFilterCondition condition = null)
    {
        base.ShowMap(collection, condition);
        collectionNameTMP.text = collection.name;
    }

    protected override void ClickedUIObjButtonAction(int index)
    {
        base.ClickedUIObjButtonAction(index);
        collectionEditorDataAssigner.ClearSelection();
    }

    public void ActivatePopupMap()
    {
        if (currentCollection != null)
        {
            GameManager.Instance.StateButtonAction((int)StateEnum.ChooseMapPopup);
            popupMapDataAssigner.ClearMap();
            popupMapDataAssigner.ShowMap(null, new MapFilterCondition(currentCollection.playerCount, currentCollection.playerCountMax,currentCollection.isBuiltin?1:0, currentCollection.multiplayerMode));
        }
        
    }
    
    public void DuplicateButtonAction()
    {
        if (currentCollection != null && selectedMapID != -1)
        {
            //add the current mapID to the current collection mapIDList
            currentCollection.mapIDList.Add(selectedMapID);
            GlobalParameters.Instance.SaveCollectionList();
        
            //renew the map view
            ShowMap(currentCollection);
        }

    }

    public void DeleteButtonAction()
    {
        if (currentCollection != null && selectedMapID != -1)
        {
            //remove the selected map in mapIDList by sequence
            currentCollection.mapIDList.RemoveAt(selectedMapSequence);
            GlobalParameters.Instance.SaveCollectionList();
        
            //renew the map view
            ShowMap(currentCollection);
        }
    }

    public void AddMapByPopup(int mapID)
    {
        if (currentCollection != null && mapID != -1)
        {
            currentCollection.mapIDList.Add(mapID);
            GlobalParameters.Instance.SaveCollectionList();
            
            //need to update inspector
            inspectorDataAssigner.ClearInspector();
        
            GameManager.Instance.StateButtonAction((int)StateEnum.CollectionEditor);
            ShowMap(currentCollection);
        }
    }
    
    
    
}
