using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class LevelDataAssigner : MonoBehaviour
{
    [SerializeField] private Transform content;
    [SerializeField] private GameObject itemTemplate;
    [SerializeField] private int itemObjCount = 32;
    [SerializeField] private ChooseLevelInteractionUI chooseLevelInteractionUI;
    [SerializeField] private Image levelBG;
    [SerializeField] private Sprite[] levelBGArray;
    private class LevelObj
    {
        public LevelObj(GameObject obj)
        {
            levelUI = obj;
            nameTMP = obj.transform.Find("Name").GetComponent<TMP_Text>();
            operationUI = obj.transform.Find("Operation").gameObject;
            operationValueTMP = operationUI.transform.Find("OperationValue").GetComponent<TMP_Text>();
            timeUI = obj.transform.Find("Time").gameObject;
            timeValueTMP = timeUI.transform.Find("TimeValue").GetComponent<TMP_Text>();

        }
        
        public GameObject levelUI;
        public TMP_Text nameTMP;
        public GameObject operationUI;
        public GameObject timeUI;
        public TMP_Text operationValueTMP;
        public TMP_Text timeValueTMP;
    }

    private List<LevelObj> levelObjList = new List<LevelObj>();
    

    private Material thisMat;

    private CollectionClass currentCollection;

    public void ShowLevel(CollectionClass collection)
    {
        currentCollection = collection;
        levelBG.sprite = levelBGArray[collection.chapterIndex];
        SetMaterialProgress();
        UpdateLevel();
    }

    public void DialogueShowLevel()
    {
        SetMaterialProgress();
        UpdateLevel();
    }

    private void UpdateLevel()
    {
        if (itemObjCount != levelObjList.Count)
        {
            levelObjList.Clear();
            for (int i = 0; i < itemObjCount; i++)
            {
                if (i == 0)
                {
                    levelObjList.Add(new LevelObj(itemTemplate));
                }
                else
                {
                    levelObjList.Add(new LevelObj(Instantiate(itemTemplate, content)));
                }
                
                AssignOneButtonEvent(i);
            }
        }

        for (int i = 0; i < levelObjList.Count; i++)
        {
            AssignOneData(i);
        }
    }
    
    private TMP_Text tempLevelText;
    private void AssignOneData(int index)
    {
        if (GameManager.Instance.ReturnPresentState() == StateEnum.Dialogue)
        {
            levelObjList[index].levelUI.SetActive(false);
            chooseLevelInteractionUI.AddDisabledButton(index);
            return;
        }
        
        if (index < currentCollection.mapIDList.Count)
        {
            levelObjList[index].levelUI.SetActive(true);

            if (index > GameManager.Instance.ProgressCountToChooseLevelFillAmount())
            {
                chooseLevelInteractionUI.AddDisabledButton(index);
            }
            else
            {
                chooseLevelInteractionUI.RemoveDisabledButton(index);
            }
            
            levelObjList[index].nameTMP.text = (index + 1).ToString();
            levelObjList[index].timeValueTMP.text = GameManager.Instance.ReturnLevelResultTime(index).ToString("F0");
            levelObjList[index].operationValueTMP.text =
                GameManager.Instance.ReturnLevelResultOperation(index).ToString();
            
            levelObjList[index].nameTMP.gameObject.SetActive(false);
            levelObjList[index].operationUI.SetActive(false);
            levelObjList[index].timeUI.SetActive(false);
        }
        else
        {
            levelObjList[index].levelUI.SetActive(false);
            chooseLevelInteractionUI.AddDisabledButton(index);
        }
        
        
    }

    private void AssignOneButtonEvent(int index)
    {
        levelObjList[index].levelUI.GetComponent<Button>().onClick
            .AddListener(() => PickUIObjButtonAction(index));
    }
    
    private void PickUIObjButtonAction(int index)
    {
        //对话途中不能选择关卡
        if (GameManager.Instance.ReturnPresentState() != StateEnum.Dialogue)
        {
            int levelIndex = index;
            if (GameManager.Instance.SelectLevel(levelIndex))
            {
                if (GameManager.Instance.ChooseLevelEditMode())
                {
                    GameManager.Instance.StateButtonAction((int)StateEnum.MapEditor);
                }
                else
                {
                    GameManager.Instance.PickLevelStartAction();
                }
            
            }
        }
    }

    private void SetMaterialProgress()
    {
        if(thisMat == null)
            thisMat = content.GetComponent<Image>().material;

        thisMat.SetFloat("_Progress", GameManager.Instance.ProgressCountToChooseLevelFillAmount());
    }
    
}