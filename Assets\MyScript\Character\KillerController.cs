using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class KillerController : OtherCharacterBase
{
    private Animator guardAnimator;

    private int killStateBoolID = Animator.StringToHash("Attack");

    private class CheckGroundPointClass
    {
        public CheckGroundPointClass(Vector3 pos, float size)
        {
            checkPoint = pos - new Vector3(0, size + 0.1f, 0);
            checkDistance = size;
        }

        public float checkDistance;
        public Vector3 checkPoint;
        public Collider2D collider;
    }

    private CheckGroundPointClass[] checkGroundArr = new[]
    {
        new CheckGroundPointClass(new Vector3(-0.4f, -0.5f, 0), 0.08f),
        new CheckGroundPointClass(new Vector3(0.4f, -0.5f, 0), 0.08f)
    };

    private enum FaceEnum
    {
        Left,
        Up,
        Right,
    }

    private class KillZone
    {
        public KillZone(Vector3 pos, Vector2 scale, FaceEnum type)
        {
            switch (type)
            {
                case FaceEnum.Left:
                    pos -= new Vector3(.5f * scale.x, 0, 0);
                    break;
                case FaceEnum.Right:
                    pos += new Vector3(.5f * scale.x, 0, 0);
                    break;
                case FaceEnum.Up:
                    pos += new Vector3(0, .5f * scale.y, 0);
                    break;
            }

            origin = pos;
            boxScale = scale;
        }

        public Vector3 origin;
        public Vector2 boxScale;
    }

    private Dictionary<FaceEnum, KillZone> killZoneDict = new Dictionary<FaceEnum, KillZone>
    {
        { FaceEnum.Left, new KillZone(new Vector3(-0.5f, 0, 0), new Vector2(0.01f, 0.6f), FaceEnum.Left) },
        { FaceEnum.Up, new KillZone(new Vector3(0, 0.5f, 0), new Vector2(0.6f, 0.01f), FaceEnum.Up) },
        { FaceEnum.Right, new KillZone(new Vector3(0.5f, 0, 0), new Vector2(0.01f, 0.6f), FaceEnum.Right) },
    };


    private float standardVelocity = 1;
    private float horVelocity = 1;
    private float velocityBeforeKillState;

    private RaycastHit2D killHit;
    private Vector3 killOrigin;
    private ChunkClass candidateChunkClass;
    private ChunkClass formerCandidateChunkClass;
    private bool chunkRotate;


    private bool leftGrounded = true;
    private bool rightGrounded = true;

    private bool rightFaceTouch = false;
    private bool leftFaceTouch = false;

    private bool hasInit = false;

    public override void InitCharacter(ToolID toolID)
    {
        base.InitCharacter(toolID);
        if (guardAnimator == null)
            guardAnimator = GetComponent<Animator>();
        guardAnimator.runtimeAnimatorController = GlobalParameters.Instance.ReturnKillerController();
        horVelocity = standardVelocity;
        velocityBeforeKillState = standardVelocity;
        hasInit = true;
    }

    public override void CharacterGotAttacked()
    {
        CharacterDie();
    }

    public override void CloseCharacter()
    {
        base.CloseCharacter();
        StopKillStateAnim();
        ClearSubscribe();
        gameObject.SetActive(false);
        hasInit = false;
    }

    // Update is called once per frame
    void FixedUpdate()
    {
        if (hasInit)
        {
            leftGrounded = OnGround(0);
            rightGrounded = OnGround(1);
            SetCandidate();
            SetChunkRotate();

            leftFaceTouch = OnFace(FaceEnum.Left);
            OnFace(FaceEnum.Up);
            rightFaceTouch = OnFace(FaceEnum.Right);

            if (!leftGrounded)
            {
                horVelocity = 1;
                Flip();
            }
            else if (!rightGrounded)
            {
                horVelocity = -1;
                Flip();
            }
            else if (leftFaceTouch)
            {
                horVelocity = 1;
                Flip();
            }
            else if (rightFaceTouch)
            {
                horVelocity = -1;
                Flip();
            }

            if ((!leftGrounded && !rightGrounded) || chunkRotate)
            {
                ApplyGravity();
            }
            else
            {
                ApplyVelocity();

                //"ApplyGround should always be after SetCandidate"
                ApplyGround();
            }

            ApplyBlock();
            CloseOutOfScreenCharacter();
        }
    }

    private void Flip()
    {
        Vector3 moveDir = new Vector3(horVelocity > 0 ? 1 : -1, 0, 0);
        characterTransform.right = moveDir;
    }

    private GameObject candidate;
    private bool hasCandidate = false;
    private float candidatePosY = 0;

    private bool OnGround(int index)
    {
        Vector3 detectOrigin = characterTransform.position + checkGroundArr[index].checkPoint;

        checkGroundArr[index].collider =
            Physics2D.OverlapCircle(detectOrigin, checkGroundArr[index].checkDistance);

        if (checkGroundArr[index].collider != null)
        {
            return true;
        }

        return false;
    }

    private void SetCandidate()
    {
        hasCandidate = false;

        if (checkGroundArr[0].collider == checkGroundArr[1].collider && checkGroundArr[0].collider != null)
        {
            if (candidate != checkGroundArr[0].collider.gameObject &&
                checkGroundArr[0].collider.CompareTag("Stickable"))
            {
                candidate = checkGroundArr[0].collider.gameObject;
                candidateChunkClass = candidate.GetComponentInParent<ChunkClass>();

                if (formerCandidateChunkClass != candidateChunkClass)
                {
                    if (formerCandidateChunkClass != null)
                        formerCandidateChunkClass.UnsubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);

                    candidateChunkClass.SubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
                    formerCandidateChunkClass = candidateChunkClass;
                }

                //clean trash
                if (candidateChunkClass.RemoveTrash(candidate))
                    KillStateAnim();
            }

            if (candidate != null)
            {
                candidatePosY = candidate.transform.position.y;
                hasCandidate = true;
            }
        }
    }

    private void SetChunkRotate()
    {
        if (candidateChunkClass != null)
        {
            chunkRotate = candidateChunkClass.inRotateProcedure;
        }
        else
        {
            chunkRotate = false;
        }
    }

    private bool OnFace(FaceEnum face)
    {
        KillZone tempKillZone = killZoneDict[face];
        Vector3 detectOrigin = characterTransform.position + tempKillZone.origin;
        Collider2D col = Physics2D.OverlapBox(detectOrigin, tempKillZone.boxScale, 0);
        if (col != null)
        {
            if (col.gameObject != gameObject)
            {
                if (col.CompareTag("Stickable") || IsKiller(col.gameObject))
                {
                    return true;
                }

                ApplyKill(col.GetComponent<IAlive>());
                return false;
            }
        }

        return false;
    }

    private void ApplyVelocity()
    {
        Vector3 offset = new Vector3(horVelocity * Time.fixedDeltaTime, 0, 0);

        if (candidateChunkClass != null)
            offset += candidateChunkClass.accumulatedMove * Time.fixedDeltaTime;

        characterTransform.position += offset;
    }

    private void ApplyGround()
    {
        if (hasCandidate)
            characterTransform.position = new Vector3(characterTransform.position.x, candidatePosY + 1, 0);
    }

    private void ApplyGravity()
    {
        characterTransform.position += new Vector3(0, -6 * Time.fixedDeltaTime, 0);
    }

    private void ApplyBlock()
    {
        if (candidateChunkClass != null)
        {
            if (candidateChunkClass.GotChildInRange(characterTransform.position, 0.3f))
            {
                CharacterDie();
            }
        }
    }

    private void ApplyKill(IAlive colAlive)
    {
        if (colAlive != null)
        {
            colAlive.GotAttacked();
            StopKillStateAnim();
            killStateAnim = KillStateAnim();
            StartCoroutine(killStateAnim);
        }
    }

    private bool IsKiller(GameObject o)
    {
        if (o != null)
        {
            if (o.TryGetComponent(out CharacterInfo characterInfo))
            {
                if (characterInfo.ReturnCharacterInfo().toolID == ToolID.Killer)
                    return true;
            }
        }

        return false;
    }

    private void StopKillStateAnim()
    {
        if (killStateAnim != null)
        {
            StopCoroutine(killStateAnim);

            horVelocity = velocityBeforeKillState;
            guardAnimator.SetBool(killStateBoolID, false);
        }
    }

    private WaitForSecondsRealtime killAnimDur = new WaitForSecondsRealtime(0.8f);
    private IEnumerator killStateAnim;

    private IEnumerator KillStateAnim()
    {
        velocityBeforeKillState = horVelocity;
        horVelocity = 0;
        guardAnimator.SetBool(killStateBoolID, true);
        yield return killAnimDur;
        horVelocity = velocityBeforeKillState;
        guardAnimator.SetBool(killStateBoolID, false);
    }

    private void MoveByAttach(Vector3 offset)
    {
        characterTransform.position += offset;
    }

    private void ChunkChangeByAttach()
    {
        if (candidate != null)
        {
            candidateChunkClass = candidate.GetComponentInParent<ChunkClass>();
            if (formerCandidateChunkClass != candidateChunkClass)
            {
                if (formerCandidateChunkClass != null)
                    formerCandidateChunkClass.UnsubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
                if (candidateChunkClass != null)
                    candidateChunkClass.SubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
            }
        }
    }

    private void ClearSubscribe()
    {
        if (formerCandidateChunkClass != null)
            formerCandidateChunkClass.UnsubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);
        if (candidateChunkClass != null)
            candidateChunkClass.SubscribeToAttachEvent(MoveByAttach, ChunkChangeByAttach);

        formerCandidateChunkClass = null;
        candidateChunkClass = null;
    }
}