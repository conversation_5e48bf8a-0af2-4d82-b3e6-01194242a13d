using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

public class InteractionUI : MonoBehaviour
{
    [SerializeField] private Transform buttonParent;

    protected List<GameObject> buttonList = new List<GameObject>();
    
    private ColorManager colorManager;

    private int PresentIndex;

    protected int presentIndex
    {
        get => PresentIndex;

        private set
        {
            PresentIndex = value;
            highlightChangeAction?.Invoke(value); 
        }

        
    }

    protected List<int> disabledButtonList = new List<int>();

    public GraphicRaycaster raycaster; // Attach the canvas's raycaster
    public EventSystem eventSystem; // Reference to the EventSystem
    private PointerEventData pointerEventData;

    [HideInInspector] public int initIndex = 0;
    
    private void OnDisable()
    {
        CloseInteractionUI();
    }
    
    public void ActivateInteractionUI()
    {
        if(colorManager == null)
           colorManager = GameManager.Instance.ReturnColorManager();
        
        DecideColor();
        RetrieveButtonList();
        InitSelection();
    }

    private void InitSelection()
    {
        presentIndex = initIndex;
        UpdateSelection(0);
        initIndex = 0;
    }

    private void CloseInteractionUI()
    {
        HighLight(presentIndex, false);
    }

    private void RetrieveButtonList()
    {
        if (buttonParent != null)
        {
            if (buttonList.Count != buttonParent.childCount)
            {
                buttonList.Clear();
                foreach (Transform btn in buttonParent)
                {
                    buttonList.Add(btn.gameObject);
                }
            }
        }
    }

    protected void UpdateSelection(int changeAmount)
    {
        if (buttonParent != null)
        {
            if (presentIndex + changeAmount >= 0 &&
                presentIndex + changeAmount < buttonList.Count && !disabledButtonList.Contains(presentIndex + changeAmount))
            {
                HighLight(presentIndex, false);
                presentIndex += changeAmount;
                HighLight(presentIndex, true);
            }
        }
    }

    protected virtual void DecideColor()
    {
    }

    protected virtual void HighLight(int index, bool highlight)
    {
    }


    private Button selectedButton;

    protected void TriggerSelectedButtonEvent()
    {
        if (buttonParent != null && presentIndex >= 0 && presentIndex < buttonList.Count)
        {
            selectedButton = buttonList[presentIndex].GetComponent<Button>();
            if (selectedButton != null)
            {
                selectedButton.onClick.Invoke();
            }
                
        }
    }

    protected Color ReturnColor(ColorEnum colorType) => colorManager.ReturnColorByType(colorType);

    void Update()
    {
        if (buttonList.Count > 0)
            MouseHoverDetect();
    }

    private void MouseHoverDetect()
    {
        // Create PointerEventData from the current mouse position
        pointerEventData = new PointerEventData(eventSystem);
        pointerEventData.position = Input.mousePosition;

        // Create a list to hold the results of the raycast
        List<RaycastResult> results = new List<RaycastResult>();

        // Perform the raycast using the raycaster on the canvas
        raycaster.Raycast(pointerEventData, results);

        // If results contain any hits, the mouse is over a UI element
        if (results.Count > 0)
        {
            UpdateSelectedIndexByMouse(results[0].gameObject);
        }
    }

    private void UpdateSelectedIndexByMouse(GameObject obj)
    {
        for (int i = 0; i < buttonList.Count; i++)
        {
            if (obj == buttonList[i] || obj.transform.parent.gameObject == buttonList[i])
            {
                int changeAmount = i - presentIndex;
                UpdateSelection(changeAmount);
            }
        }
    }

    public void UpdateInitSelection(int index)
    {
        initIndex = index;
        // int changeAmount = index - presentIndex;
        // UpdateSelection(changeAmount);
    }
    
    
    
    public void AddDisabledButton(int index)
    {
        if (!disabledButtonList.Contains(index))
        {
            //HighLight(index, false);
            disabledButtonList.Add(index);
        }
            
    }

    public void RemoveDisabledButton(int index)
    {
        disabledButtonList.Remove(index);
    }

    private Action<int> highlightChangeAction;
    public void SubscribeHighlightChangeAction(Action<int> ctx)
    {
        highlightChangeAction += ctx;
    }

    public void UnsubscribeHighlightChangeAction(Action<int> ctx)
    {
        highlightChangeAction -= ctx;
    }


    #region API for player action

    public virtual void UIRespondPlayerVerticalInput(int changeAmount)
    {
    }

    public virtual void UIRespondPlayerHorizontalInput(int changeAmount, int playerIndex = 0)
    {
    }

    public virtual void UIRespondPlayerSubmitInput()
    {
    }

    #endregion
}