using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ChunkAttachEffect : MonoBehaviour
{
    [SerializeField] private GameObject effectPrefab;
    
    private class EffectPool
    {
        private Stack<EffectStruct> effectStack;
        private GameObject prefab;
        private Transform parent;

        public EffectPool(GameObject o, int initialSize, Transform p = null)
        {
            prefab = o;
            parent = p;
            effectStack = new Stack<EffectStruct>();
            for (int i = 0; i < initialSize; i++)
            {
                CreateObject();
            }
        }

        private EffectStruct CreateObject()
        {
            EffectStruct newEffect = new EffectStruct(Instantiate(prefab, parent));
            newEffect.obj.SetActive(false);
            effectStack.Push(newEffect);
            return newEffect;
        }

        public EffectStruct GetObject()
        {
            EffectStruct effect = effectStack.Count == 0 ? CreateObject() : effectStack.Pop();
            effect.obj.SetActive(true);
            return effect;
        }

        public void ReturnObject(EffectStruct e)
        {
            e.obj.SetActive(false);
            effectStack.Push(e);
        }
    }
    
    
    private struct EffectStruct
    {
        public EffectStruct(GameObject o)
        {
            obj = o;
            effectTransform = obj.transform;
            audioSource = obj.GetComponent<AudioSource>();
            particleSystem = obj.GetComponent<ParticleSystem>();
        }
        
        public GameObject obj;
        public Transform effectTransform;
        public AudioSource audioSource;
        public ParticleSystem particleSystem;
    }

    private EffectPool myEffectPool;
    private float audioVolumeFactor = 0.2f;
    private float audioVolume;

    private ColorManager colorManager;
    
    // Start is called before the first frame update
    void Start()
    {
        
        myEffectPool = new EffectPool(effectPrefab, 5, transform);
    }
    

    public void SetVolume(float vol)
    {
        audioVolume = vol * audioVolumeFactor;
    }


    public void PutEffect(Vector3 pos, Vector3 dir)
    {
        EffectStruct effect = myEffectPool.GetObject();
        effect.effectTransform.position = pos;
        
        ParticleSystem.MainModule mainModule = effect.particleSystem.main;
        ColorEnum colorEnum = GlobalParameters.Instance.currentBlockShaderParam.colorType;
        if (colorManager == null)
            colorManager = GameManager.Instance.ReturnColorManager();
        mainModule.startColor = colorManager.ReturnColorByType(colorEnum);
        
        float horizontalAlignment = Mathf.Abs(Vector3.Dot(Vector3.right, dir));
        if (horizontalAlignment >= 1)
        {
            effect.effectTransform.rotation = Quaternion.AngleAxis(90, Vector3.forward);
        }
        else
        {
            effect.effectTransform.rotation = Quaternion.identity;
        }

        effect.audioSource.volume = audioVolume;
        effect.audioSource.Play();
        effect.particleSystem.Play();
        StartCoroutine(EffectSustain(effect));
    }

    public void PutSoundEffect(Vector3 pos)
    {
        EffectStruct effect = myEffectPool.GetObject();
        effect.effectTransform.position = pos;
        
        effect.audioSource.volume = audioVolume;
        effect.audioSource.Play();
        
        StartCoroutine(EffectSustain(effect));
    }

    private WaitForSeconds effectDur = new WaitForSeconds(0.3f);
    private IEnumerator EffectSustain(EffectStruct effect)
    {
        yield return effectDur;
        effect.audioSource.Stop();
        effect.particleSystem.Stop();
        myEffectPool.ReturnObject(effect);
    }
}


