

public enum ToolID
{
    Block = 0,
    Move = 1,
    Rotate = 2,
    <PERSON> = 4,
    Stave = 5,
    <PERSON> = 7,
    <PERSON> = 8,
    Flip = 9,
    Info = 10,
    <PERSON> = 11,
    <PERSON> = 12,
    Trash = 13,
    <PERSON>er = 14,
    Checkpoint = 15,
    Switch = 16,
    <PERSON> = 17,
    <PERSON> = 18,
    <PERSON> = 19,
    <PERSON>mitter = 20
}


public enum ToolType
{
    Symbol,
    Character,
    Player,
    Dialogue,
    None,
}

public enum ColorEnum
{
    BlockChapter1 = 0,
    PlanterHighLightBlock = 1,
    PlayerHighLightBlock =2,
    UIButtonHighLight = 3,
    UIButtonOriginal = 4,
    LevelButtonHighLight = 5,
    LevelButtonOriginal = 6,
    PlayerColor1 = 7,
    PlayerColor2 = 8,
    PlayerColor3 = 9,
    PlayerColor4 = 10,
    PlayerColor5 = 11,
    PlayerColor6 = 12,
    PlayerColor7 = 13,
    PlayerColor8 = 14,
    BlockChapter2 = 15,
    BlockChapter3 = 16,
    UIButtonDisabled = 17
}


public enum ToolDirection
{
    Original,
    Up,
    Left,
    Down,
    Flip
}

public enum StateEnum
{
    Home = 0,
    ChooseSlot = 1,
    ChooseStoryLevel = 2,
    GamePlay = 3,
    GamePlayPause = 4,
    ChooseEditorMap = 5,
    MapEditor = 6,
    MapEditorPause = 7,
    Option = 8,
    Dialogue = 9,
    ChoosePlayer = 10,
    ChooseCollection = 11,
    CollectionResult = 12,
    Editor = 13,
    CollectionEditor = 14,
    ChooseMapPopup = 15,
    ChooseChapter = 16,
    Leaderboard = 17,
    Play = 18,
    Credits = 19
}

public enum DetectorEnum
{
    Bottom,
    Right,
    Upper,
    Left,
}

public enum PlayerAudioEnum
{
    PlayerJump,
    PlayerStick,
    PlayerCollect,
    StarGlow,
    PlayerDie,
    PlayerRevive,
    PlayerPortal,
    PlayerSwitchBlock,
    SuccessReady,
}


public enum LanguageEnum
{
    English,
    Chinese
}

public enum CharacterEnum
{
    Bunny,
    Arthur,
    Guard,
    Planter,
    Switch,
    Transmitter,
    TheOne,
}

public enum ExpressionEnum
{
    Normal,
    Surprised,
    Happy,
    Question,
}

public enum BGMType
{
    Menu,
    Chapter1,
    Chapter2,
    Chapter3,
    Bridge1
}

public enum ControlType
{
    Keyboard,
    Gamepad
}

public enum SpecificController
{
    None,
    Keyboard1,
    Keyboard2,
    Xbox,
    PS,
}

public enum MultiplayerMode
{
    Competitive,
    Cooperative,
    None,
}

public enum InstructionType
{
    StartDialogueInstruction,
    NoteWrongDirectionInstruction,
    SaveSuccessResultInstruction,
    SaveFailResultInstruction,
    MoreThanOneSwitchInstruction,
    MoreThanEightPlayerInstruction,
    QuitGameInstruction,
    WrongSequenceInstruction,
    BagFullInstruction,
    CannotChangePlayerCountMax,
    PlayerCountMaxNotReached,
}

public enum AttackType
{
    Symbol,
    Character
}

public enum LeaderboardValueType
{
    Time,
    Operation,
}

public enum LeaderboardRange
{
    TopPlayer,
    Friends,
    Around
}

