using UnityEngine;

[ExecuteInEditMode]
public class Ground : MonoBehaviour
{
    private Transform thisTransform;

    private Vector3 formerScale;
    
    // Start is called before the first frame update
    void Start()
    {
        thisTransform = transform;
        formerScale = thisTransform.localScale;
    }

    // Update is called once per frame
    void Update()
    {
        if (thisTransform.localScale != formerScale)
        {
            SetShaderScale();
            formerScale = thisTransform.localScale;
        }
    }

    private void SetShaderScale()
    {
        GetComponent<SpriteRenderer>().material.SetVector("_Scale", thisTransform.localScale*0.2f);
    }
}
