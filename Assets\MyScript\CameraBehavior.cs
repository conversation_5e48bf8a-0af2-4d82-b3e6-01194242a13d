using UnityEngine;
using System.Threading.Tasks;
using Random = UnityEngine.Random;

public class CameraBehavior : MonoBehaviour
{
    [SerializeField] private Camera mainCamera;

    private Vector3 originalCameraPos;
    private float originalCameraSize;
    private Transform mainCameraTransform;

    private void Start()
    {
        mainCameraTransform = mainCamera.transform;
        originalCameraPos = mainCameraTransform.position;
        originalCameraSize = mainCamera.orthographicSize;
    }

    #region Zoom

    [SerializeField] private Vector3 editorModeCameraPos = new Vector3(6, -2, -10);
    [SerializeField] private float editorModeCameraSize = 13;    
    public void ActivateMapEditorZoom(bool activated)
    {
        if (activated)
        {
            mainCameraTransform.position = editorModeCameraPos;
            mainCamera.orthographicSize = editorModeCameraSize;
        }
        else
        {
            mainCameraTransform.position = originalCameraPos;
            mainCamera.orthographicSize = originalCameraSize;
        }
    }
    
    #endregion

    #region Shake
    [SerializeField] private float shakeMagnitude = 0.1f;  // 震动幅度

    public async Task TriggerShake(float dur)
    {
        await ShakeCamera(dur);
    }

    private async Task ShakeCamera(float dur)
    {
        float timer = dur;

        while (timer > 0)
        {
            mainCameraTransform.position = originalCameraPos + Random.insideUnitSphere * shakeMagnitude;
            await Task.Yield();
            timer -= Time.unscaledDeltaTime;
        }

        mainCameraTransform.position = originalCameraPos;
    }
    

    #endregion
}

