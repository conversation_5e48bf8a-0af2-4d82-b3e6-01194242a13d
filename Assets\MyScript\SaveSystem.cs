using System;
using System.Collections.Generic;
using UnityEngine;
using System.IO;
using System.Runtime.Serialization.Formatters.Binary;

public static class SaveSystem
{
    private static string basePath = Application.persistentDataPath;
    private static string progressDirName = "Progress";
    private static string mapDataDirName = "MapData";
    private static string mapsDirName = "Maps";

    private static BinaryFormatter binaryFormatter;

    public static Progress LoadProgress(int index)
    {
        string progressDir = Path.Combine(basePath, progressDirName);
        if (!Directory.Exists(progressDir))
        {
            Directory.CreateDirectory(progressDir);
        }
        string path = Path.Combine(progressDir, "progress_" + index + ".bin");
        
        if (File.Exists(path))
        {
            return LoadData<Progress>(path);
        }
        return new Progress(index, DateTime.Now.Date.ToString("d"));
    }

    public static void SetProgress(int index)
    {
        string progressDir = Path.Combine(basePath, progressDirName);
        if (!Directory.Exists(progressDir))
        {
            Directory.CreateDirectory(progressDir);
        }

        string path = Path.Combine(progressDir, "progress_" + index + ".bin");
        
        SaveData(path, GameManager.Instance.presentProgress);
    }

    public static void SetProgress(Progress progress)
    {
        string progressDir = Path.Combine(basePath, progressDirName);
        if (!Directory.Exists(progressDir))
        {
            Directory.CreateDirectory(progressDir);
        }

        string path = Path.Combine(progressDir, "progress_" + progress.slot + ".bin");
        
        SaveData(path, GameManager.Instance.presentProgress);
    }

    public static List<CollectionClass> LoadCollectionList()
    {
        string mapDataDir = Path.Combine(basePath, mapDataDirName);
        if (!Directory.Exists(mapDataDir))
        {
            Directory.CreateDirectory(mapDataDir);
        }
        string path = Path.Combine(mapDataDir, "CollectionList" + ".bin");
        
        
        if (File.Exists(path))
        {
            return LoadData<List<CollectionClass>>(path);
        }
        return new List<CollectionClass>();
    }

    public static void SetCollectionList(List<CollectionClass> collectionList)
    {
        string mapDataDir = Path.Combine(basePath, mapDataDirName);
        if (!Directory.Exists(mapDataDir))
        {
            Directory.CreateDirectory(mapDataDir);
        }
        string path = Path.Combine(mapDataDir, "CollectionList" + ".bin");
        
        SaveData(path, collectionList);
    }

    public static List<MapPreview> LoadMapList()
    {
        string mapDataDir = Path.Combine(basePath, mapDataDirName);
        if (!Directory.Exists(mapDataDir))
        {
            Directory.CreateDirectory(mapDataDir);
        }
        string path = Path.Combine(mapDataDir, "MapList" + ".bin");
        
        if (File.Exists(path))
        {
            return LoadData<List<MapPreview>>(path);
        }
        return new List<MapPreview>();
    }

    public static void SetMapList(List<MapPreview> previewList)
    {
        string mapDataDir = Path.Combine(basePath, mapDataDirName);
        if (!Directory.Exists(mapDataDir))
        {
            Directory.CreateDirectory(mapDataDir);
        }
        
        string path = Path.Combine(mapDataDir, "MapList" + ".bin");
        SaveData(path, previewList);
    }

    public static MapTemplate LoadMap(string mapID)
    {
        string mapsDir = Path.Combine(basePath, mapDataDirName, mapsDirName);
        if (!Directory.Exists(mapsDir))
        {
            Directory.CreateDirectory(mapsDir);
        }
        
        string path = Path.Combine(mapsDir, mapID + ".bin");
        if (File.Exists(path))
        {
            return LoadData<MapTemplate>(path);
        }
        return new MapTemplate(null);
    }

    public static void SetMap(string mapID, MapTemplate mapTemplate)
    {
        string mapsDir = Path.Combine(basePath, mapDataDirName, mapsDirName);
        if (!Directory.Exists(mapsDir))
        {
            Directory.CreateDirectory(mapsDir);
        }
        
        string path = Path.Combine(mapsDir, mapID + ".bin");
        
        SaveData(path, mapTemplate);
    }
    
    public static void DeleteMap(string mapID)
    {
        // Combine the persistent data path with the file name
        string mapDir = Path.Combine(basePath, mapDataDirName, mapsDirName, mapID + ".bin");

        // Check if the file exists before trying to delete it
        if (File.Exists(mapDir))
        {
            // Delete the file
            File.Delete(mapDir);
        }
        else
        {
            Debug.LogError("File not found: " + mapDir);
        }
    }

    #region Actual save/load/delete

    private static void SaveData<T>(string path, T data)
    {
        if (binaryFormatter == null)
            binaryFormatter = new BinaryFormatter();
        using(FileStream stream = new FileStream(path, FileMode.Create))
        {
            binaryFormatter.Serialize(stream, data);
        }
    }

    private static T LoadData<T>(string path)
    {
        if (binaryFormatter == null)
            binaryFormatter = new BinaryFormatter();
        using(FileStream stream = new FileStream(path, FileMode.Open))
        {
            return (T) binaryFormatter.Deserialize(stream);
        }
    }

    #endregion
    
    

}
