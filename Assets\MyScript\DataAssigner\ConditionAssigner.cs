using UnityEngine;
using TMPro;

public class ConditionAssigner : MonoBehaviour
{
    [SerializeField] private GameObject conditionUI;
    [SerializeField] private GameObject noResultWarningUI;
    
    private GameObject playerCountUI;
    private TMP_Text playerCountTMP;
    private TMP_Text multiplayerModeTMP;
    

    private bool firstTime = true;


    public void ShowCondition(MapFilterCondition condition)
    {
        if (firstTime)
        {
            InitConditionUI();
        }
        
        if (condition != null)
        {
            playerCountUI.SetActive(true);
            playerCountTMP.text = condition.playerCount == condition.playerCountMax ? $"{condition.playerCount}" : $"{condition.playerCount}~{condition.playerCountMax}";
            if (condition.playerCount > 1)
            {
                multiplayerModeTMP.text = MyLocalizationManager.Instance.RetrieveStringByKey(condition.multiplayerMode.ToString());
            }
            else
            {
                multiplayerModeTMP.text = string.Empty;
            }
                
        }
        else
        {
            playerCountUI.SetActive(false);
            multiplayerModeTMP.text = string.Empty;
        }
    }
    
    private void InitConditionUI()
    {
        playerCountUI = conditionUI.transform.Find("PlayerCount").gameObject;
        playerCountTMP = playerCountUI.transform.Find("PlayerCountValue").GetComponent<TMP_Text>();
        multiplayerModeTMP = conditionUI.transform.Find("MultiplayerModeValue").GetComponent<TMP_Text>();
    }

    public void NoResultGivenCondition(bool showWarning)
    {
        noResultWarningUI.SetActive(showWarning);
    }
    
}
