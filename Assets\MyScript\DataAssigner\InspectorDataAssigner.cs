using TMPro;
using UnityEngine;


public class InspectorDataAssigner : MonoBeh<PERSON><PERSON>
{

    protected enum InspectType
    {
        Map,
        Collection
    }

    protected InspectType currentInspectType;


    public virtual void ShowMapInspector(MapPreview map, MapOrCollectionItemDataAssigner itemDataAssigner, int mapSequence)
    {
    }


    public virtual void ShowCollectionInspector(CollectionClass collection, MapOrCollectionItemDataAssigner itemDataAssigner)
    {
    }
    
    
    protected virtual void AssignData()
    {
        
    }
    


    public virtual void ClearInspector()
    {
    }

    protected virtual void ClearActions()
    {
    }
    


    
}
