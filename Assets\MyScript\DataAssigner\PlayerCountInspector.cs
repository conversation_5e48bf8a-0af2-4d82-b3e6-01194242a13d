using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;

public class PlayerCountInspector : MonoBehaviour
{
    [SerializeField] private GameObject editablePlayerObj;
    [SerializeField] private NumberCounter playerMinCounter;
    [SerializeField] private NumberCounter playerMaxCounter;

    [Header("Fields below can be null")]
    [SerializeField] private GameObject notEditablePlayerObj;
    [SerializeField] private TMP_Text notEditablePlayerTMP;
    
    private CollectionClass currentCollection;
    private MapOrCollectionItemDataAssigner currentItemDataAssigner;
    
    public void SetMapPlayerCountInspector(MapPreview map, Func<int, bool> minCountFunc, Func<int, bool> maxCountFunc)
    {
        editablePlayerObj.SetActive(true);
        playerMinCounter.SetNumber(map.playerCount, minCountFunc);
        playerMaxCounter.SetNumber(map.playerCountMax, maxCountFunc);
    }
    
    public void SetCollectionPlayerCountInspector(CollectionClass collection, Func<int, bool> minCountFunc, Func<int, bool> maxCountFunc, bool editable)
    {
        editablePlayerObj.SetActive(editable);
        notEditablePlayerObj.SetActive(!editable);
        playerMinCounter.SetNumber(collection.playerCount,  minCountFunc);
        playerMaxCounter.SetNumber(collection.playerCountMax, maxCountFunc);
        
        notEditablePlayerTMP.text = collection.playerCount == collection.playerCountMax ? $"{collection.playerCount}": $"{collection.playerCount}~{collection.playerCountMax}" ;
    }

}