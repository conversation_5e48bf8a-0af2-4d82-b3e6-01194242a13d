using System.Collections;
using System.Collections.Generic;
using System.Threading.Tasks;
using UnityEngine;

public class WaitFirstInputController : MonoBehaviour
{
    [SerializeField] private float animDur = 1;
    private GameObject pressKeyIndicator;
    private RectTransform bgRT;
    private Vector3 bigScale = Vector3.one*3;
    private Vector3 normalScale = Vector3.one;

    private bool hasInit;
    private Task currentTask;

    public async Task BeginWaitFirstInputAnim(bool selected)
    {
        if (selected)
            currentTask = PickedFirstControllerAnim();
        else
        {
            currentTask = NotPickedFirstControllerAnim();
        }

        await currentTask;
    }

    public void CloseWaitInputController()
    {
        InitSettings();
        currentTask.Dispose();
        bgRT.gameObject.SetActive(false);
        pressKeyIndicator.SetActive(false);
    }


    private async Task PickedFirstControllerAnim()
    {
        InitSettings();
        bgRT.gameObject.SetActive(true);
        pressKeyIndicator.SetActive(false);

        float timer = 0;
        while (timer < animDur)
        {
            timer += Time.unscaledDeltaTime;
            bgRT.localScale = Vector3.Lerp(normalScale, bigScale, timer/animDur);
            await Task.Yield();
        }
        
        bgRT.gameObject.SetActive(false);
    }
    
    
    private async Task NotPickedFirstControllerAnim()
    {
        InitSettings();
        bgRT.gameObject.SetActive(true);
        pressKeyIndicator.SetActive(false);

        float timer = 0;
        while (timer < animDur)
        {
            timer += Time.unscaledDeltaTime;
            bgRT.localScale = Vector3.Lerp(bigScale, normalScale, timer/animDur);
            await Task.Yield();
        }
        
        pressKeyIndicator.SetActive(true);
    }

    private void InitSettings()
    {
        if (!hasInit)
        {
            pressKeyIndicator = transform.Find("PressKeyIndicator").gameObject;
            bgRT = transform.Find("WaitInputBG").GetComponent<RectTransform>();
        }
    }
}
