using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class PlayerSpawnIndexManager : MonoBehaviour
{
    [SerializeField] private GameObject indexObjPrefab;
    [SerializeField] private Transform parentTransform;
    [Serializable]
    private class SpawnIndexClass
    {
        public SpawnIndexClass(GameObject obj)
        {
            indexObj = obj;
            indexBG = indexObj.GetComponent<Image>();
            indexText = indexObj.GetComponentInChildren<TMP_Text>();
            rt = indexObj.GetComponent<RectTransform>();
        }
        public GameObject indexObj;
        [HideInInspector] public Transform playerSpawnTransform;
        [HideInInspector] public TMP_Text indexText;
        [HideInInspector] public Image indexBG;
        [HideInInspector] public RectTransform rt;
    }

    private List<SpawnIndexClass> spawnIndexClassList;

    private ColorManager colorManager;

    private int playerCount = 8;
    
    // Start is called before the first frame update
    void Start()
    {
        colorManager = GameManager.Instance.ReturnColorManager();
        InitSpawnIndex();
    }

    private void InitSpawnIndex()
    {
        spawnIndexClassList = new List<SpawnIndexClass>();
        for (int i = 0; i < playerCount; i++)
        {
            if (i == 0)
            {
                spawnIndexClassList.Add(new SpawnIndexClass(indexObjPrefab));
            }
            else
            {
                spawnIndexClassList.Add(new SpawnIndexClass(Instantiate(indexObjPrefab, parentTransform)));
            }
            spawnIndexClassList[i].indexObj.SetActive(false);
        }
    }

    private Vector2 offset = new Vector2(1, -8);
    public void PutPlayerSpawnIndex(Transform spawnTransform)
    {
        int index = GlobalParameters.Instance.playerSpawnObjPool.ReturnPlayerIndex(spawnTransform.gameObject);
        if (index >= 0)
        {
            Vector2 worldToScreen = GlobalMethod.WorldPositionToCanvasPosition(spawnTransform.position);
            spawnIndexClassList[index].playerSpawnTransform = spawnTransform;
            spawnIndexClassList[index].rt.anchoredPosition = worldToScreen + offset;
            spawnIndexClassList[index].indexObj.SetActive(true);
            spawnIndexClassList[index].indexBG.color = colorManager.PlayerColorByIndex(index);
            spawnIndexClassList[index].indexText.text = $"P{index}";
        }

        
    }

    public void UpdatePlayerSpawnIndex()
    {
        HideAll();
        int index = 0;
        foreach (GameObject o in GlobalParameters.Instance.playerSpawnObjPool.activePool)
        {
            Vector2 worldToScreen = GlobalMethod.WorldPositionToCanvasPosition(o.transform.position);
            spawnIndexClassList[index].playerSpawnTransform = o.transform;
            spawnIndexClassList[index].rt.anchoredPosition = worldToScreen + offset;
            spawnIndexClassList[index].indexObj.SetActive(true);
            spawnIndexClassList[index].indexBG.color = colorManager.PlayerColorByIndex(index);
            spawnIndexClassList[index].indexText.text = $"P{index}";
            index++;
        }
    }

    public void HideAll()
    {
        foreach (SpawnIndexClass spawnIndexClass in spawnIndexClassList)
        {
            spawnIndexClass.indexObj.SetActive(false);
        }
    }

    public void UpdatePlayerSpawnIndexPos()
    {
        foreach (SpawnIndexClass s in spawnIndexClassList)
        {
            if (s.playerSpawnTransform != null)
            {
                Vector2 worldToScreen = GlobalMethod.WorldPositionToCanvasPosition(s.playerSpawnTransform.position);
                s.rt.anchoredPosition = worldToScreen + offset;
            }
        }
    }
    
    
}
