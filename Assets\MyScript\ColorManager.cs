using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class ColorManager
{
    private MyColorTemplate myColorTemplate;
    private static Dictionary<ColorEnum, Color> colorDict;
    
    public ColorManager(MyColorTemplate template)
    {
        myColorTemplate = template;
        InitColorDict();
    }

    private void InitColorDict()
    {
        colorDict = new Dictionary<ColorEnum, Color>();
        foreach (ColorStruct colorStruct in myColorTemplate.colorList)
        {
            colorDict.Add(colorStruct.colorType, colorStruct.colorValue);
        }
    }


    public Color ReturnColorByType(ColorEnum type) => colorDict[type];

    public Color PlayerColorByIndex(int index)
    {
        int baseIndex = (int)ColorEnum.PlayerColor1;
        int colorIndex = baseIndex + index;
        if (colorIndex >= baseIndex && colorIndex <= (int)ColorEnum.PlayerColor8)
        {
            return colorDict[(ColorEnum)colorIndex];
        }
        return Color.clear;
    }

    #region name
    public Color BlockOriginal => colorDict[ColorEnum.BlockChapter1];

    public Color UIButtonHighLight => colorDict[ColorEnum.UIButtonHighLight];

    public Color UIButtonOriginal => colorDict[ColorEnum.UIButtonOriginal];

    public Color UIButtonDisabled => colorDict[ColorEnum.UIButtonDisabled];

    public Color PlayerColor1 => colorDict[ColorEnum.PlayerColor1];

    #endregion
}
