using System.Collections.Generic;
using UnityEngine;

public class ChapterArtController : MonoBehaviour
{
    [SerializeField] private GameObject waterTextureCam;
    [SerializeField] private List<GameObject> backgroundObjList;


    [SerializeField] private List<BlockShaderParam> blockShaderParams;
    

    private void Start()
    {
        InitBackground();
    }

    private void InitBackground()
    {
        if (PlayerPrefs.HasKey(GameConst.backgroundPrefKey))
        {
            UpdateBackgroundWithChapterIndex(PlayerPrefs.GetInt(GameConst.backgroundPrefKey));
        }
        else
        {
            UpdateBackgroundWithChapterIndex(0);
        }
    }

    public void UpdateBackgroundWithChapterIndex(int index)
    {
        foreach (GameObject o in backgroundObjList)
        {
            o.SetActive(false);
        }

        if (index >= 0 && index < backgroundObjList.Count)
        {
            backgroundObjList[index].SetActive(true);
            GlobalParameters.Instance.SetCurrentBlockShaderParam(blockShaderParams[index]);
            waterTextureCam.SetActive(index == 0);
            PlayerPrefs.SetInt(GameConst.backgroundPrefKey, index);
        }
            
    }
}
