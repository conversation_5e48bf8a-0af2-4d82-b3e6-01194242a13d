using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MapDataAssigner : MonoBehaviour
{
    [SerializeField] private Transform content;
    [SerializeField] private GameObject itemTemplate;
    [SerializeField] protected InspectorDataAssigner inspectorDataAssigner;
    [SerializeField] private int itemObjCount = 13;
    [SerializeField] private RectTransform upperBound;
    [SerializeField] private RectTransform lowerBound;
    [SerializeField] private TMP_InputField searchInputField;
    
    private bool firstTime = true;

    private class MapObj
    {
        public MapObj(GameObject obj, int index)
        {
            UIObj = obj;
            itemDataAssigner = obj.GetComponent<MapOrCollectionItemDataAssigner>();
            rectTransform = obj.GetComponent<RectTransform>();
            mapIndex = index;
            mapSequence = index;
        }

        public GameObject UIObj;
        public MapOrCollectionItemDataAssigner itemDataAssigner;
        public RectTransform rectTransform;
        public int mapIndex;  //the index of all map list
        public int mapSequence; //sequence in current viewable list
    }

    private List<MapObj> mapObjList = new List<MapObj>();

    protected CollectionClass currentCollection;

    protected List<MapPreview> allMapList = new List<MapPreview>();

    private RectTransform contentRectTransform;
    private VerticalLayoutGroup contentVerticalLayoutGroup;

    private int headPointer;
    private int rearPointer;

    protected int selectedMapID;
    protected int selectedMapIndex;
    protected int selectedMapSequence;

    private int selectedObjIndex = -1;

    private int mapCount = 0;


    protected MapFilterCondition currentMapFilterCondition;

    private ConditionAssigner conditionAssigner;

    private bool noResult = true;

    public void ClearMap()
    {
        currentCollection = null;
        selectedMapID = -1;
        selectedMapIndex = -1;
        selectedMapSequence = -1;
        ClearSelection();
        if (mapObjList.Count <= 0)
        {
            itemTemplate.SetActive(false);
        }
        else
        {
            foreach (MapObj mapObj in mapObjList)
            {
                mapObj.UIObj.SetActive(false);
            }
        }
    }
    
    public virtual void ShowMap(CollectionClass collection, MapFilterCondition condition = null)
    {
        currentCollection = collection;
        currentMapFilterCondition = condition;

        if (searchInputField && (condition == null || string.IsNullOrEmpty(condition.searchText)))
            searchInputField.text = string.Empty;
        
        
        canScroll = false;
        SetParameters();
        UpdateMap();
        ClearSelection();
        canScroll = true;
        
        if(conditionAssigner != null)
            conditionAssigner.NoResultGivenCondition(noResult);

        firstTime = false;
    }

    private void SetParameters()
    {
        if (firstTime)
        {
            allMapList = GlobalParameters.Instance.allMapList;
            contentRectTransform = content.GetComponent<RectTransform>();
            contentVerticalLayoutGroup = content.GetComponent<VerticalLayoutGroup>();

            conditionAssigner = GetComponent<ConditionAssigner>();
            
            if(searchInputField != null)
               searchInputField.onValueChanged.AddListener(SearchMap);
        }

        CalculateMapCount();
        
        if(conditionAssigner != null)
            conditionAssigner.ShowCondition(currentMapFilterCondition);

        contentVerticalLayoutGroup.padding.top = 20;
        contentRectTransform.anchoredPosition = new Vector2(0, 0);
        contentRectTransform.sizeDelta =
            new Vector2(contentRectTransform.sizeDelta.x, mapCount * changeStep);
        posDiff = 0;

        headPointer = 0;
        rearPointer = itemObjCount - 1;
        noResult = true;
    }
    
    private void UpdateMap()
    {
        if (itemObjCount != mapObjList.Count)
        {
            mapObjList.Clear();
            for (int i = 0; i < itemObjCount; i++)
            {
                if (i == 0)
                    mapObjList.Add(new MapObj(itemTemplate, i));
                else
                {
                    mapObjList.Add(new MapObj(Instantiate(itemTemplate, content), i));
                }

                AssignOneButtonEvent(i);
            }
        }

        for (int i = 0; i < itemObjCount; i++)
        {
            mapObjList[i].UIObj.transform.SetSiblingIndex(i);
            AssignOneData(i, i);
        }
    }

    private void AssignOneData(int objIndex, int sequence)
    {
        int mapIndex = sequence;

        if (currentCollection != null)
        {
            //look in collection map list
            mapIndex = QueryMapIndexInCurrentCollection(sequence);
            
        }
        else
        {
            //look in all map list
            mapIndex = QueryMapIndexInAllByCondition(sequence);
        }


        if (mapIndex >= 0 && mapIndex < allMapList.Count)
        {
            //at least got one result
            noResult = false;
            
            //assign data
            //correct max player count;
            CorrectPlayerCountMax(mapIndex);
            
            mapObjList[objIndex].UIObj.SetActive(true);
            mapObjList[objIndex].mapIndex = mapIndex;
            mapObjList[objIndex].mapSequence = sequence;
            mapObjList[objIndex].itemDataAssigner.SetMapData(allMapList[mapIndex]);
            mapObjList[objIndex].itemDataAssigner.MapFitCollection(allMapList[mapIndex].FitCollection(currentCollection));
        }
        else
        {
            mapObjList[objIndex].UIObj.SetActive(false);
        }
    }

    private int QueryMapIndexInCurrentCollection(int sequence)
    {
        int index = -1;
        if (sequence >= 0 && sequence < currentCollection.mapIDList.Count)
        {
            int mapID = currentCollection.mapIDList[sequence];
            index = ReturnIndexByID(mapID);
            
            //the second check
            //the relevant map must have been deleted, therefore update that info in collection
            if (index < 0)
            {
                currentCollection.mapIDList.RemoveAt(sequence);
                GlobalParameters.Instance.SaveCollectionList();
                return QueryMapIndexInCurrentCollection(sequence);
            }
                
        }

        return index;
    }

    private int QueryMapIndexInAllByCondition(int sequence)
    {
        int count = 0;
        int index = -1;
        for (int i = 0; i < allMapList.Count; i++)
        {
            if (FitCondition(i))
            {
                if (count == sequence)
                    index = i;
                count++;
            }
        }

        return index;
    }

    private bool FitCondition(int mapIndex)
    {
        bool showChapter = false;
#if UNITY_EDITOR
        showChapter = true;
#endif
        bool chapterMatch = showChapter || allMapList[mapIndex].chapterIndex < 0;
        
        if (currentMapFilterCondition != null)
        {
            if (string.IsNullOrEmpty(currentMapFilterCondition.searchText))
            {
                bool playerCountMatch = currentMapFilterCondition.playerCount == -1 || (allMapList[mapIndex].playerCount == currentMapFilterCondition.playerCount && allMapList[mapIndex].playerCountMax == currentMapFilterCondition.playerCountMax);
                bool builtinMatch =  currentMapFilterCondition.isBuiltin == -1 || allMapList[mapIndex].isBuiltin == (currentMapFilterCondition.isBuiltin == 1);
                bool modeMatch = currentMapFilterCondition.multiplayerMode == MultiplayerMode.None || allMapList[mapIndex].multiplayerMode == currentMapFilterCondition.multiplayerMode;
                return playerCountMatch && builtinMatch && chapterMatch && modeMatch;
            }
            
            return FitSearchTextCondition(mapIndex);
            
        }

        return chapterMatch;
    }

    private void AssignOneButtonEvent(int index)
    {
        mapObjList[index].UIObj.GetComponent<Button>().onClick.AddListener(() => ClickedUIObjButtonAction(index));
    }

    protected virtual void ClickedUIObjButtonAction(int index)
    {
        //highlight selected item
        UpdateSelectAppearance(index);
        
        //set up parameters
        int mapIndex = mapObjList[index].mapIndex;
        selectedMapID = allMapList[mapIndex].mapID;
        selectedMapIndex = mapIndex;
        selectedMapSequence = mapObjList[index].mapSequence;

        //this is expensive, improve later
        GlobalParameters.Instance.LoadMap(selectedMapID);
        GlobalParameters.Instance.ResetLevel();

        inspectorDataAssigner.ShowMapInspector(allMapList[mapIndex],
            mapObjList[index].itemDataAssigner, selectedMapSequence);
    }

    private void UpdateSelectAppearance(int index)
    {
        if (index != selectedObjIndex)
        {
            if (selectedObjIndex >= 0)
                mapObjList[selectedObjIndex].itemDataAssigner.IfItemSelected(false);
            
            if(index >= 0)
                mapObjList[index].itemDataAssigner.IfItemSelected(true);
            
            selectedObjIndex = index;
        }
    }
    
    public void ClearSelection()
    {
        selectedMapID = -1;
        selectedMapIndex = -1;
        selectedMapSequence = -1;
        
        if (selectedObjIndex >= 0)
            mapObjList[selectedObjIndex].itemDataAssigner.IfItemSelected(false);
        selectedObjIndex = -1;
    }

    private int ReturnIndexByID(int mapID)
    {
        int index = -1;
        for (int i = 0; i < allMapList.Count; i++)
        {
            if (allMapList[i].mapID == mapID)
                index = i;
        }

        return index;
    }

    private void CalculateMapCount()
    {
        mapCount = 0;
        if (currentCollection != null)
        {
            CalculateCollectionActualMapCount(0);
        }
        else
        {
            for (int i = 0; i < allMapList.Count; i++)
            {
                if (FitCondition(i))
                    mapCount++;
            }
        }
    }

    private void CalculateCollectionActualMapCount(int sequence)
    {
        if (sequence >= 0 && sequence < currentCollection.mapIDList.Count)
        {
            int mapID = currentCollection.mapIDList[sequence];
            int index = ReturnIndexByID(mapID);
            if (index >= 0)
            {
                mapCount++;
                CalculateCollectionActualMapCount(sequence + 1);
            }
            else
            {
                //the relevant map must have been deleted, therefore update that info in collection
                currentCollection.mapIDList.RemoveAt(sequence);
                GlobalParameters.Instance.SaveCollectionList();
                CalculateCollectionActualMapCount(sequence);
            }
        }
    }

    private void SearchMap(string searchText)
    {
        if (currentMapFilterCondition != null)
        {
            currentMapFilterCondition.searchText = searchText;
        }
        else
        {
            currentMapFilterCondition = new MapFilterCondition(-1, -1, -1,MultiplayerMode.None, searchText);
        }
        
        ShowMap(currentCollection, currentMapFilterCondition);
    }

    private bool FitSearchTextCondition(int index)
    {
        //search input is empty
        if (string.IsNullOrWhiteSpace(currentMapFilterCondition.searchText)) return true;

        //contains not-upper-case sensitive
        if (allMapList[index].mapName.ToUpper().Contains(currentMapFilterCondition.searchText.ToUpper())) return true;
        
        //abbreviation
        string mapNameAbb = AbbreviationString(allMapList[index].mapName, false);
        string searchTextAbb = AbbreviationString(currentMapFilterCondition.searchText, true);

        if (mapNameAbb.Contains(searchTextAbb))
            return true;
        

        return false;
    }
    
    private void CorrectPlayerCountMax(int mapIndex)
    {
        if (allMapList[mapIndex].playerCountMax < allMapList[mapIndex].playerCount)
            allMapList[mapIndex].playerCountMax = allMapList[mapIndex].playerCount;
    }

    #region scroll move & arrange

    private float changeStep = 60;
    
    private float contentPosY;
    private float posDiff;
    private bool canScroll = false;

    private void Update()
    {
        if (canScroll)
        {
            CheckStep();
        }
    }

    private void CheckStep()
    {
        float diff = contentRectTransform.anchoredPosition.y - contentPosY;
        contentPosY = contentRectTransform.anchoredPosition.y;
        
        if (Mathf.Abs(diff) > 0.5f)
            posDiff = diff;
        

        if (upperBound == null || lowerBound == null)
            return;
        
        if (posDiff > 0 && mapObjList[headPointer].rectTransform.position.y > upperBound.position.y)
        {
            MoveItem(1);
        }else if (posDiff < 0 && mapObjList[rearPointer].rectTransform.position.y < lowerBound.position.y)
        {
            MoveItem(-1);
        }
        
    }

    private void MoveItem(int dir)
    {
        if (dir > 0)
        {
            //check if there is new data behind rear
            //the head button goes to rear, and take the new data
            int newSequence = mapObjList[rearPointer].mapSequence + 1;
            if (newSequence < mapCount)
            {
                
                mapObjList[headPointer].UIObj.transform.SetSiblingIndex(itemObjCount - 1);
                AssignOneData(headPointer, newSequence);
                
                //if this item is selected, then the selected appearance
                //should be adjusted
                if(headPointer == selectedObjIndex)
                    UpdateSelectAppearance(-1);
                if(mapObjList[headPointer].mapSequence == selectedMapSequence)
                    UpdateSelectAppearance(headPointer);
                
                rearPointer = headPointer;
                headPointer = headPointer + 1 >= itemObjCount ? 0 : headPointer + 1;
                contentVerticalLayoutGroup.padding.top += (int)changeStep;
            }
        }
        else
        {
            //check if there is new data before head
            //the rear goes to head, and take the new data
            int newSequence = mapObjList[headPointer].mapSequence - 1;
            if (newSequence >= 0)
            {
                mapObjList[rearPointer].UIObj.transform.SetSiblingIndex(0);
                AssignOneData(rearPointer, newSequence);
                
                if(rearPointer == selectedObjIndex)
                    UpdateSelectAppearance(-1);
                if(mapObjList[rearPointer].mapSequence == selectedMapSequence)
                    UpdateSelectAppearance(rearPointer);
                
                headPointer = rearPointer;
                rearPointer = rearPointer - 1 < 0 ? itemObjCount - 1 : rearPointer - 1;
                contentVerticalLayoutGroup.padding.top -= (int)changeStep;
            }
        }
    }
    

    #endregion

    public void MoveItemSequence(int oldSequence, int newSequence)
    {
        //if not shown in the current view, then first move to the right place
        int upmostViewableSequence = mapObjList[headPointer].mapSequence;
        int leastViewableSequence = mapObjList[rearPointer].mapSequence;
        if (newSequence < upmostViewableSequence || oldSequence < upmostViewableSequence)
        {
            //need to move up view port
            int moveTimes = Mathf.Max(upmostViewableSequence - newSequence, upmostViewableSequence - oldSequence) + 3;
            for (int i = 0; i < moveTimes; i++)
            {
                contentRectTransform.anchoredPosition -= new Vector2(0, changeStep);
                MoveItem(-1);
            }
        }

        if (newSequence > leastViewableSequence || oldSequence > leastViewableSequence)
        {
            //need to move down view port
            int moveTimes = Mathf.Max(newSequence - leastViewableSequence, oldSequence - leastViewableSequence) + 3;
            for (int i = 0; i < moveTimes; i++)
            {
                contentRectTransform.anchoredPosition += new Vector2(0, changeStep);
                MoveItem(1);
            }
        }
            
        int oldPointer = ReturnPointerBySequence(oldSequence);
        int newPointer = ReturnPointerBySequence(newSequence);
        
        AssignOneData(oldPointer, oldSequence);
        AssignOneData(newPointer, newSequence);
        UpdateSelectAppearance(-1);
        UpdateSelectAppearance(newPointer);
    }

    private int ReturnPointerBySequence(int sequence)
    {
        int pointer = -1;
        for (int i = 0; i < mapObjList.Count; i++)
        {
            if (mapObjList[i].mapSequence == sequence)
                pointer = i;
        }
        return pointer;
    }


    private List<char> tempCharList = new List<char>();
    private string AbbreviationString(string value, bool isSearchText)
    {
        //first character, and all other upper case character
        tempCharList.Clear();
        int index = 0;
        
        foreach (char c in value)
        {
            if (index == 0 || char.IsUpper(c) || isSearchText || char.IsNumber(c) || FormerCharIsPunctuation(value, index))
            {
                tempCharList.Add(c);
            }

            index++;
        }

        return new string(tempCharList.ToArray()).ToUpper();
    }

    private bool FormerCharIsPunctuation(string s, int index)
    {
        if(index > 0)
            if (char.IsPunctuation(s[index - 1]) && !char.IsPunctuation(s[index]))
            {
                return true;
            }

        return false;
    }
}