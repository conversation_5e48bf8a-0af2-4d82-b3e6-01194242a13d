using System;
using UnityEngine;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Runtime.Serialization;

[Serializable]
public struct BagTool
{
    public BagTool(ToolID id, ToolDirection dir)
    {
        toolID = id;
        toolDirection = dir;
    }

    public ToolID toolID;
    public ToolDirection toolDirection;
    
    public static bool operator == (BagTool tool1, BagTool tool2)
    {
        return tool1.toolID == tool2.toolID && tool1.toolDirection == tool2.toolDirection;
    }

    public static bool operator !=(BagTool tool1, BagTool tool2)
    {
        return !(tool1 == tool2);
    }

    public override bool Equals(object obj)
    {
        if (obj is BagTool)
        {
            return this == (BagTool)obj;
        }

        return false;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(toolID, toolDirection);
    }
}

[Serializable]
public class Chunk
{
    public List<PropTool> chunkPropList = new List<PropTool>();
}


[Serializable]
public struct PropTool:ISerializable
{
    public PropTool(int id, int dir, Vector3 pos)
    {
        toolID = id;
        toolDirection = dir;
        toolPos = pos;
    }

    public int toolID;
    public int toolDirection;
    public Vector3 toolPos;
    
    // Deserialization constructor
    public PropTool(SerializationInfo info, StreamingContext context)
    {
        toolPos = new Vector3(
            info.GetSingle("posX"),
            info.GetSingle("posY"),
            info.GetSingle("posZ"));
        toolID = info.GetInt32("toolID");
        toolDirection = info.GetInt32("toolDirection");
    }

    // Custom serialization logic
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        info.AddValue("posX", toolPos.x);
        info.AddValue("posY", toolPos.y);
        info.AddValue("posZ", toolPos.z);
        info.AddValue("toolID", toolID);
        info.AddValue("toolDirection", toolDirection);
    }
}



[Serializable]
public class Dialogue
{
    public string speakerName;
    public CharacterEnum characterType;
    public ExpressionEnum expressionType;
    public string contentText;
    public string contentTextChineseSimplified;
    public string videoName;
    public Sprite[] contentImage;
    public bool leftSide;
}

[Serializable]
public class DialogueArea:ISerializable
{
    public DialogueArea(GameObject area)
    {
        dialogueName = area.name;
        triggerPos = area.transform.position;
    }

    public string dialogueName;
    public Vector3 triggerPos;
    
    // Deserialization constructor
    public DialogueArea(SerializationInfo info, StreamingContext context)
    {
        triggerPos = new Vector3(
            info.GetSingle("posX"),
            info.GetSingle("posY"),
            info.GetSingle("posZ"));
        dialogueName = info.GetString("dialogueName");
    }

    // Custom serialization logic
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        info.AddValue("posX", triggerPos.x);
        info.AddValue("posY", triggerPos.y);
        info.AddValue("posZ", triggerPos.z);
        info.AddValue("dialogueName", dialogueName);
    }
}

[Serializable]
public class OtherCharacterArea:ISerializable
{
    public OtherCharacterArea(Vector3 pos, BagTool info)
    {
        characterPos = pos;
        characterInfo = info;
    }

    public Vector3 characterPos;
    public BagTool characterInfo;
    
    // Deserialization constructor
    public OtherCharacterArea(SerializationInfo info, StreamingContext context)
    {
        characterPos = new Vector3(
            info.GetSingle("posX"),
            info.GetSingle("posY"),
            info.GetSingle("posZ"));
        characterInfo = new BagTool((ToolID)info.GetInt32("toolID"), (ToolDirection)info.GetInt32("toolDirection"));
    }

    // Custom serialization logic
    public void GetObjectData(SerializationInfo info, StreamingContext context)
    {
        info.AddValue("posX", characterPos.x);
        info.AddValue("posY", characterPos.y);
        info.AddValue("posZ", characterPos.z);
        info.AddValue("toolID", (int)characterInfo.toolID);
        info.AddValue("toolDirection", (int)characterInfo.toolDirection);
    }
}

public class MapFilterCondition
{
    public MapFilterCondition(int player, int playerMax, int builtin, MultiplayerMode mode = MultiplayerMode.Competitive, string search = "")
    {
        playerCount = player;
        playerCountMax = playerMax;
        isBuiltin = builtin;
        multiplayerMode = mode;
        searchText = search;
    }
    public int playerCount;
    public int playerCountMax;
    public int isBuiltin;
    public MultiplayerMode multiplayerMode;
    public string searchText;
}

[Serializable]
public class MapPreview
{
    public MapPreview(int id, string name, int player, int playerMax)
    {
        mapID = id;
        mapName = name;
        playerCount = player;
        playerCountMax = playerMax;
        multiplayerMode = MultiplayerMode.Competitive;
    }

    [HideInInspector] public int mapID;
    public string mapName;
    [HideInInspector] public int playerCount;
    [HideInInspector] public int playerCountMax;
    [HideInInspector] public MultiplayerMode multiplayerMode;
    [HideInInspector] public bool isBuiltin;
    [HideInInspector] public int chapterIndex = -1;
    [HideInInspector] public string mapDescription;
    [HideInInspector] public bool hasCountdown = false;

    public bool FitCollection(CollectionClass collection)
    {
        if (collection == null)
            return true;
        return playerCount == collection.playerCount && playerCountMax == collection.playerCountMax &&
               multiplayerMode == collection.multiplayerMode && isBuiltin == collection.isBuiltin;
    }
}

[Serializable]
public class CollectionClass
{
    public string name;
    public MultiplayerMode multiplayerMode;
    public string description;
    public bool isChapter = false;
    public bool isBuiltin = false;
    public int chapterIndex;
    [HideInInspector] public List<int> mapIDList = new List<int>();
    [HideInInspector] public int playerCount;
    [HideInInspector] public int playerCountMax;
    [HideInInspector] public float timeDur;
    [HideInInspector] public int operationCount;
}


[Serializable]
public class LevelResult
{
    public bool hasPassed = false;
    public float timeDur = 0;
    public int operationCount = 0;
    public int playerIndex = 0;
}

[Serializable]
public class Progress
{
    public Progress(int index, string date)
    {
        slot = index;
        lastPlayDate = date;
    }

    public int slot;
    public int chapter;
    public List<LevelResult> levelResultList = new List<LevelResult>();
    public List<string> dialogueProgress = new List<string>();
    public string lastPlayDate;
}

[Serializable]
public struct ColorStruct
{
    public ColorEnum colorType;
    public Color colorValue;
}

public struct CutSceneRequest
{
    public bool hasCurtain;
    public bool hasCountdown;
    public bool showWinner;
    public bool moveAllPlayers;
    public int nextMapID;
    public GameObject targetObject;
    public int winnerPlayerIndex;
    public int activePlayerCount;
    public Vector3[] playerNextPositions;
    public float time;
    public int operation;
    public bool timeNewRecord;
    public bool operationNewRecord;
}

[Serializable]
public class DialogueCharacter
{
    public CharacterEnum characterType;
    public List<DialogueExpression> characterExpressionList;
}

[Serializable]
public class DialogueExpression
{
    public ExpressionEnum expressionType;
    public Sprite expressionImage;
    public AudioClip expressionAudio;
}


//this is exactly the same as Level Template
[Serializable]
public class MapTemplate
{
    //NOTE: when there is a change, also update "CopyMapTemplateToLevelTemplate" and "LevelTemplate"
    public MapTemplate(LevelTemplate levelTemplate)
    {
        if (levelTemplate != null)
        {
            playerPosList = levelTemplate.playerPosList;
            sceneChunkList = levelTemplate.sceneChunkList;
            bagToolList = levelTemplate.bagToolList;
            dialogueAreaList = levelTemplate.dialogueAreaList;
            otherCharacterList = levelTemplate.otherCharacterList;
            portalSequence = levelTemplate.portalSequence;
        }
    }
    
    public List<MyVector3> playerPosList = new List<MyVector3>();
    public List<Chunk> sceneChunkList = new List<Chunk>();
    public List<BagTool> bagToolList = new List<BagTool>();
    public List<DialogueArea> dialogueAreaList = new List<DialogueArea>();
    public List<OtherCharacterArea> otherCharacterList = new List<OtherCharacterArea>();
    public List<MyVector3> portalSequence = new List<MyVector3>();
}

[Serializable]
public struct MyVector3
{
    public MyVector3(Vector3 pos)
    {
        x = pos.x;
        y = pos.y;
        z = pos.z;
    }

    public Vector3 ConvertToVector3()
    {
        return new Vector3(x, y, z);
    }

    public float x;
    public float y;
    public float z;

    public float this[int index]
    {
        get
        {
            switch (index)
            {
                case 0: return x;
                case 1: return y;
                case 2: return z;
                default: throw new IndexOutOfRangeException();
            }
        }

        set
        {
            switch (index)
            {
                case 0:
                    x = value;
                    break;
                case 1:
                    y = value;
                    break;
                case 2:
                    z = value;
                    break;
                default: throw new IndexOutOfRangeException();
            }
        }
    }
}

public struct ChunkStickableIndex
{
    public ChunkStickableIndex(int c, int s)
    {
        chunkIndex = c;
        stickableIndex = s;
    }

    public void Deconstruct(out int cIndex, out int sIndex)
    {
        cIndex = chunkIndex;
        sIndex = stickableIndex;
    }
    public int chunkIndex;
    public int stickableIndex;
}

[Serializable]
public class BlockShaderParam
{
    public ColorEnum colorType;
    
    [Range(0, 50)] public float maxOffset;

    [Range(0.005f, 0.1f)] public float maxSpeed;

    [Range(0.1f, 0.9f)] public float density;

    [Range(1, 15)] public float size;
}

public class MObjPool<T>
{
    private Stack<T> objPool = new Stack<T>();
    private List<T> activeObjPool = new List<T>();
    public ReadOnlyCollection<T> activePool => activeObjPool.AsReadOnly();
    
    protected Transform parentTransform;
    protected GameObject prefabObj;
    protected string objName;
    protected int objCount;

    public void InitPool(string name, int initCount, Transform parent, GameObject prefab = null)
    {
        objName = name;
        parentTransform = parent;
        prefabObj = prefab;

        for (int i = 0; i < initCount; i++)
        {
            CreateObj();
        }
    }

    protected virtual void CreateObj()
    {
        //create an obj, with name and parent
        //set false
        //push it to the stack
        objCount++;
    }

    public virtual T GetObj()
    {
        if (objPool.Count == 0)
        {
            CreateObj();
        }

        T t = objPool.Pop();
        activeObjPool.Add(t);
        return t;
    }

    public virtual void ReturnObj(T t)
    {
        activeObjPool.Remove(t);
        objPool.Push(t);
    }

    public void ReturnAll()
    {
        for (int i = activeObjPool.Count - 1; i >= 0; i--)
        {
            ReturnObj(activeObjPool[i]);
        }
        
        activeObjPool.Clear();
    }
}