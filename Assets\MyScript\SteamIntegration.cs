using System;
using System.Threading.Tasks;
using UnityEngine;
using Steamworks.Data;

public class SteamIntegration : MonoBehaviour
{
    public static SteamIntegration Instance { private set; get; }

    private const string achievementBase = "NEW_ACHIEVEMENT_1_";

    private string currentLeaderboardName;
    private Leaderboard currentLeaderBoard;
    private bool gotLeaderboard = false;

    private int millisecondScale = 1000;
    

    private void Awake()
    {
        if (Instance != null && Instance == this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(Instance);
    }

    // Start is called before the first frame update
    void Start()
    {
// #if UNITY_EDITOR
//         return;
// #endif
        try
        {
            Steamworks.SteamClient.Init(3878020);
        }
        catch (Exception e)
        {
            Console.WriteLine(e);
            throw;
        }
    }

    #region API

    public void UnlockAchievement(string index)
    {
// #if UNITY_EDITOR
//         return;
// #endif
        var ach = new Achievement(achievementBase+index);
        if(!ach.State)
          ach.Trigger();
    }

    public async void SubmitCollectionTime(string name, float time)
    {
        await SetLeaderboard(name, LeaderboardDisplay.TimeMilliSeconds);
        if (!gotLeaderboard)
            return;
        int roundedTime = (int)Mathf.Round(time * millisecondScale);
        await SteamLeaderBoardSystem.SubmitLeaderboard(currentLeaderBoard, roundedTime);
    }

    public async void SubmitCollectionOperation(string name, int op)
    {
        await SetLeaderboard(name, LeaderboardDisplay.Numeric);
        if (!gotLeaderboard)
            return;
        
        await SteamLeaderBoardSystem.SubmitLeaderboard(currentLeaderBoard, op);
    }

    public async Task<LeaderboardEntry[]> GetLeaderboardByRange(string name, LeaderboardRange range)
    {
        LeaderboardEntry[] le;
        switch (range)
        {
            case LeaderboardRange.TopPlayer:
                le = await GetTopPlayers(name);
                break;
            case LeaderboardRange.Friends:
                le = await GetScoresOfFriends(name);
                break;
            case LeaderboardRange.Around:
                le = await GetScoresAround(name);
                break;
            default:
                le = await GetTopPlayers(name);
                break;
        }

        return le;
    }
    
    #endregion

    #region Helper
    
    private async Task SetLeaderboard(string name, LeaderboardDisplay display)
    {

        if (currentLeaderboardName == name)
        {
            gotLeaderboard = true;
            return;
        }
        currentLeaderboardName = name;
        
        try
        {
            var lb = await SteamLeaderBoardSystem.GetLeaderBoards(name, display);
            if (lb.HasValue)
            {
                currentLeaderBoard = lb.Value;
                gotLeaderboard = true;
            }
            else
            {
                gotLeaderboard = false;
                Debug.LogError("leaderboard did not have value");
            }
        }
        catch (Exception e)
        {
            gotLeaderboard = false;
            Debug.LogError($"Error loading leaderboards: {e.Message}");
            // Optionally, you could handle the exception, e.g., retry logic or fallback behavior
        }
    }

    private async Task<LeaderboardEntry[]> GetTopPlayers(string name)
    {
        LeaderboardDisplay display = name.Contains("Operation")?LeaderboardDisplay.Numeric:LeaderboardDisplay.TimeMilliSeconds;
        await SetLeaderboard(name, display);
        if (!gotLeaderboard)
            return null;
        // Get top 20 scores
        LeaderboardEntry[] le = await currentLeaderBoard.GetScoresAsync( 20 );
        return le;
    }
    
    private async Task<LeaderboardEntry[]> GetScoresOfFriends(string name)
    {
        LeaderboardDisplay display = name.Contains("Operation")?LeaderboardDisplay.Numeric:LeaderboardDisplay.TimeMilliSeconds;
        await SetLeaderboard(name, display);
        if (!gotLeaderboard)
            return null;
        // Get top 20 scores
        LeaderboardEntry[] le = await currentLeaderBoard.GetScoresFromFriendsAsync();;
        return le;
    }
    
    private async Task<LeaderboardEntry[]> GetScoresAround(string name)
    {
        LeaderboardDisplay display = name.Contains("Operation")?LeaderboardDisplay.Numeric:LeaderboardDisplay.TimeMilliSeconds;
        await SetLeaderboard(name, display);
        if (!gotLeaderboard)
            return null;
        // Get top 20 scores
        LeaderboardEntry[] le = await currentLeaderBoard.GetScoresAroundUserAsync( -10, 10 );
        return le;
    }
    
    void OnApplicationQuit()
    {
        Steamworks.SteamClient.Shutdown();
    }
    
    #endregion
}
