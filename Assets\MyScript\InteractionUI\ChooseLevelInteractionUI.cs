using UnityEngine;
using TMPro;
using UnityEngine.UI;

public class ChooseLevelInteractionUI : InteractionUI
{
    private Color levelButtonHighLightColor;
    private Color levelButtonNormalColor;
    protected override void DecideColor()
    {
        levelButtonHighLightColor = ReturnColor(ColorEnum.LevelButtonHighLight);
        levelButtonNormalColor = ReturnColor(ColorEnum.LevelButtonOriginal);
    }
    protected override void HighLight(int index, bool highlight)
    {
        if (index >= 0 && index < buttonList.Count)
        {
            buttonList[index].GetComponent<Image>().color =
                highlight ? levelButtonHighLightColor : levelButtonNormalColor;

            foreach (Transform item in buttonList[index].transform)
            {
                item.gameObject.SetActive(highlight);
            }
        }
            
    }

    public override void UIRespondPlayerSubmitInput()
    {
        TriggerSelectedButtonEvent();
    }

    public override void UIRespondPlayerVerticalInput(int changeAmount)
    {
        changeAmount *= -8;
        UpdateSelection(changeAmount);
    }

    public override void UIRespondPlayerHorizontalInput(int changeAmount, int playerIndex)
    {
        if (playerIndex == 0)
        {
            changeAmount *= 1;
            UpdateSelection(changeAmount); 
        }
        
    }
}
