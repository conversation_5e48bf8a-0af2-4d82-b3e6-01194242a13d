using System.Collections.Generic;
using TMPro;
using UnityEngine;


public class LanguageSelector : MonoBehaviour
{
    private int currentLanguageIndex;

    [SerializeField] private List<LanguageEnum> selectableLanguageList;

    public TMP_Text languageText;

    [SerializeField] private GameObject nextButtonObj;
    [SerializeField] private GameObject lastButtonObj;

    public void NextLanguageButtonAction()
    {
        //update language settings
        if (currentLanguageIndex < selectableLanguageList.Count - 1)
        {
            MyLocalizationManager.Instance.UpdateLanguage(currentLanguageIndex + 1);
        }
    }

    public void LastLanguageButtonAction()
    {
        //update language settings
        if (currentLanguageIndex > 0)
        {
            MyLocalizationManager.Instance.UpdateLanguage(currentLanguageIndex - 1);
        }
    }

    public void UpdateSelectorLanguage(int index)
    {
        currentLanguageIndex = index;
        UpdateAppearance();
    }

    private void UpdateAppearance()
    {
        //update language presentation
        languageText.text = selectableLanguageList[currentLanguageIndex].ToString();
        //update "clickablility" of next and last
        
        lastButtonObj.SetActive(currentLanguageIndex != 0);
        nextButtonObj.SetActive(currentLanguageIndex != selectableLanguageList.Count - 1);
    }
    
    
    
}