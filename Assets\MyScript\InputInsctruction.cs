using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class InputInsctruction : MonoBehaviour
{
    [SerializeField] private GameObject playerJoinInstructionObj;

    [SerializeField] private GameObject gameDataInstructionObj;

    private GameObject uiObj;

    [SerializeField] private SlotDataAssigner slotDataAssigner;

    [SerializeField] private Transform container;

    private Image holdPresenter;

    private GameObject deviceUIObj;

    private enum ButtonName
    {
        None,
        A,
        X,
        R2,
        Space,
        Shift,
        Enter
    }

    [Serializable]
    private class InputSprite
    {
        public ButtonName btnName;
        public Sprite mSprite;
    }

    [SerializeField] private List<InputSprite> spriteList;

    private Dictionary<ButtonName, Sprite> spriteDict = new Dictionary<ButtonName, Sprite>();
    

    private void Start()
    {
        playerJoinInstructionObj.SetActive(false);
        gameDataInstructionObj.SetActive(false);
        foreach (InputSprite inputSprite in spriteList)
        {
            spriteDict.Add(inputSprite.btnName, inputSprite.mSprite);
        }
    }

    private void PrepareChooseSlotUI(SpecificController specificController)
    {
        DisableChildren(uiObj.transform);
        
        //hold presenter
        deviceUIObj = uiObj.transform.Find(specificController.ToString()).gameObject;
        deviceUIObj.SetActive(true);
        holdPresenter = deviceUIObj.transform.Find("Delete").Find("HoldPresenter").GetComponent<Image>();
        holdPresenter.fillAmount = 0;
        UpdateChooseSlotUI(slotDataAssigner.IsHighlightedGameDataEmpty());
    }

    private string instructionContainerKey;
    private Transform instructionContainer;
    private void PrepareChoosePlayerUI(SpecificController specificController)
    {
        DisableChildren(uiObj.transform);
        container.gameObject.SetActive(true);

        string newInstructionContainerKey = "KeyboardInstruction";
        if (specificController != SpecificController.Keyboard1)
        {
            newInstructionContainerKey = "GamepadInstruction";
        }

        if (newInstructionContainerKey != instructionContainerKey)
        {
            if (instructionContainer != null)
            {
                instructionContainer.SetParent(uiObj.transform);
                instructionContainer.gameObject.SetActive(false);
            }
            instructionContainer = uiObj.transform.Find(newInstructionContainerKey);
            instructionContainer.SetParent(container);
            instructionContainer.gameObject.SetActive(true);

            instructionContainerKey = newInstructionContainerKey;
        }
        
        

        if (specificController == SpecificController.Keyboard1)
        {
            instructionContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.Enter];
        }else if (specificController == SpecificController.Xbox)
        {
            instructionContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.A];
        }else if (specificController == SpecificController.PS)
        {
            instructionContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.X];
        }

        UpdateChoosePlayerUI();
    }

    private void DisableChildren(Transform t)
    {
        foreach (Transform child in t)
        {
            child.gameObject.SetActive(false);
        }
    }


    #region API

    public void ShowInputInstruction(StateEnum state, SpecificController specificController)
    {
        bool isChoosePlayer = state == StateEnum.ChoosePlayer;
        bool isChooseSlot = state == StateEnum.ChooseSlot;
        playerJoinInstructionObj.SetActive(isChoosePlayer);
        gameDataInstructionObj.SetActive(isChooseSlot);

        if (isChoosePlayer)
        {
            uiObj = playerJoinInstructionObj;
            PrepareChoosePlayerUI(specificController);
        }else if (isChooseSlot)
        {
            uiObj = gameDataInstructionObj;
            PrepareChooseSlotUI(specificController);
        }
    }

    public void BeginHoldPresenterAnim()
    {
        CancelHoldPresenterAnim();
        holdPresenterAnim = HoldPresenterAnim();
        StartCoroutine(holdPresenterAnim);
    }

    public void CancelHoldPresenterAnim()
    {
        //cancel the count down
        if (holdPresenterAnim != null)
            StopCoroutine(holdPresenterAnim);
        holdPresenter.fillAmount = 0;
    }

    public void UpdateChooseSlotUI(bool isDataEmpty)
    {
        deviceUIObj.transform.Find("Delete").gameObject.SetActive(!isDataEmpty);
        string instructionKey = isDataEmpty ? "New game" : "Continue";
        deviceUIObj.transform.Find("Continue").Find("Instruction").GetComponent<TMP_Text>().text =
            MyLocalizationManager.Instance.RetrieveStringByKey(instructionKey);
    }

    private string presenterContainerKey;
    private Transform currentPresenterContainer;
    public void UpdateChoosePlayerUI()
    {
        SpecificController nextSpecificController = GameManager.Instance.GetNextJoinPlayerSpecificController();
        if (nextSpecificController == SpecificController.None)
        {
            if (currentPresenterContainer != null)
            {
                currentPresenterContainer.SetParent(uiObj.transform);
                currentPresenterContainer.gameObject.SetActive(false);
            }

            presenterContainerKey = string.Empty;
            return;
        }
        string newContainerKey = "KeyboardHoldPresenter";
        if (nextSpecificController != SpecificController.Keyboard1 && nextSpecificController != SpecificController.Keyboard2)
        {
            newContainerKey = "GamepadHoldPresenter";
        }

        if (newContainerKey != presenterContainerKey)
        {
            if (currentPresenterContainer != null)
            {
                currentPresenterContainer.SetParent(uiObj.transform);
                currentPresenterContainer.gameObject.SetActive(false);
            }
            
            
            currentPresenterContainer = uiObj.transform.Find(newContainerKey);
            currentPresenterContainer.SetParent(container);
            currentPresenterContainer.gameObject.SetActive(true);
            
            presenterContainerKey = newContainerKey;
        }
        

        if (nextSpecificController == SpecificController.Keyboard1)
        {
            currentPresenterContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.Space];
        }else if (nextSpecificController == SpecificController.Keyboard2)
        {
            currentPresenterContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.Shift];
        }else if (nextSpecificController == SpecificController.Xbox)
        {
            currentPresenterContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.R2];
        }else if (nextSpecificController == SpecificController.PS)
        {
            currentPresenterContainer.Find("ButtonImg").GetComponent<Image>().sprite = spriteDict[ButtonName.R2];
        }
        
        //hold presenter
        holdPresenter = currentPresenterContainer.Find("HoldPresenter").GetComponent<Image>();
        holdPresenter.fillAmount = 0;
    }

    #endregion
    
    
    private IEnumerator holdPresenterAnim;
    private float holdAnimDur = 1;

    private IEnumerator HoldPresenterAnim()
    {
        float time = 0;
        while (time < holdAnimDur)
        {
            time += Time.unscaledDeltaTime;
            holdPresenter.fillAmount = time / holdAnimDur;
            yield return null;
        }

        CancelHoldPresenterAnim();
    }
}
