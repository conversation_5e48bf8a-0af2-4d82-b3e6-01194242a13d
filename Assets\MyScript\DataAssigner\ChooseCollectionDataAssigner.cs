using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;


public class ChooseCollectionDataAssigner : MonoBehaviour
{
    [SerializeField] private Transform content;
    [SerializeField] private GameObject itemTemplate;
    [SerializeField] private int itemObjCount = 13;
    [SerializeField] private RectTransform upperBound;
    [SerializeField] private RectTransform lowerBound;

    [SerializeField] private ChooseCollectionInspector chooseCollectionInspector;

    [SerializeField] private bool showChapterCollection = false;

    [SerializeField] private SelectStateComponent multiplayerModeSelector;
    

    private class CollectionObj
    {
        public CollectionObj(GameObject obj, int index)
        {
            UIObj = obj;
            itemDataAssigner = obj.GetComponent<MapOrCollectionItemDataAssigner>();
            rectTransform = obj.GetComponent<RectTransform>();
            collectionIndex = index;
        }

        public GameObject UIObj;
        public MapOrCollectionItemDataAssigner itemDataAssigner;
        public RectTransform rectTransform;
        public int collectionIndex;
    }

    private List<CollectionObj> collectionObjList = new List<CollectionObj>();
    
    private List<CollectionClass> allCollectionList;

    private int currentCollectionListCount;

    private RectTransform contentRectTransform;
    private VerticalLayoutGroup contentVerticalLayoutGroup;

    private MapFilterCondition currentCollectionFilterCondition;

    private int headPointer;
    private int rearPointer;

    private bool firstTime = true;

    private int selectedCollectionIndex;

    private int selectedObjIndex;

    private ConditionAssigner conditionAssigner;

    private bool noResult = true;
    

    public void ShowCollection(MapFilterCondition condition = null)
    {
        currentCollectionFilterCondition = condition;

        if (condition.playerCount > 1)
        {
            multiplayerModeSelector.gameObject.SetActive(true);
            multiplayerModeSelector.SetSelection(condition.multiplayerMode.ToString(), MultiplayerModeChangeAction);
        }
        else
        {
            multiplayerModeSelector.gameObject.SetActive(false);
        }
        
        UpdateAllContent();
    }

    private void UpdateAllContent()
    {
        canScroll = false;
        SetParameters();
        UpdateCollection();
        ClearSelection();
        canScroll = true;
        
        chooseCollectionInspector.ClearInspector();
        
        if(conditionAssigner != null)
            conditionAssigner.NoResultGivenCondition(noResult);
        
        firstTime = false;
    }
    

    private void SetParameters()
    {
        if (firstTime)
        {
            allCollectionList = GlobalParameters.Instance.allCollectionList;

            contentRectTransform = content.GetComponent<RectTransform>();
            contentVerticalLayoutGroup = content.GetComponent<VerticalLayoutGroup>();

            conditionAssigner = GetComponent<ConditionAssigner>();
        }
        
        if(conditionAssigner != null)
            conditionAssigner.ShowCondition(currentCollectionFilterCondition);


        contentRectTransform.anchoredPosition = new Vector2(0, 0);
        currentCollectionListCount = allCollectionList.Count;
        contentRectTransform.sizeDelta =
            new Vector2(contentRectTransform.sizeDelta.x, currentCollectionListCount * changeStep);
        posDiff = 0;
        
        
        headPointer = 0;
        rearPointer = itemObjCount - 1;

        noResult = true;
    }

    private void UpdateCollection()
    {
        if (itemObjCount != collectionObjList.Count)
        {
            collectionObjList.Clear();
            for (int i = 0; i < itemObjCount; i++)
            {
                if (i == 0)
                    collectionObjList.Add(new CollectionObj(itemTemplate, i));
                else
                {
                    collectionObjList.Add(new CollectionObj(Instantiate(itemTemplate, content), i));
                }

                AssignOneButtonEvent(i);
            }
        }

        for (int i = 0; i < itemObjCount; i++)
        {
            AssignOneData(i, i);
        }
    }

    private void AssignOneData(int objIndex, int sequence)
    {
        int collectionIndex = QueryCollectionIndex(sequence);
        if (collectionIndex >= 0)
        {
            //got at least one result
            noResult = false;
            
            //assign data
            CorrectPlayerCountMax(collectionIndex);
            collectionObjList[objIndex].UIObj.SetActive(true);
            collectionObjList[objIndex].collectionIndex = collectionIndex;
            collectionObjList[objIndex].itemDataAssigner.SetCollectionData(allCollectionList[collectionIndex]);
        }
        else
        {
            collectionObjList[objIndex].UIObj.SetActive(false);
        }
    }

    private int QueryCollectionIndex(int sequence)
    {
        int count = 0;
        int index = -1;
        for (int i = 0; i < allCollectionList.Count; i++)
        {
            if (FitCondition(i))
            {
                if (count == sequence)
                    index = i;
                count++;
            }
        }

        return index;
    }

    private void AssignOneButtonEvent(int index)
    {
        collectionObjList[index].UIObj.GetComponent<Button>().onClick
            .AddListener(() => ClickedUIObjButtonAction(index));
    }

    private void ClickedUIObjButtonAction(int index)
    {
        HighlightObj(index);
        selectedCollectionIndex = collectionObjList[index].collectionIndex;
        chooseCollectionInspector.ShowCollectionInspector(allCollectionList[selectedCollectionIndex], collectionObjList[index].itemDataAssigner);
    }
    
    private void HighlightObj(int index)
    {
        if (index != selectedObjIndex)
        {
            if (selectedObjIndex >= 0)
                collectionObjList[selectedObjIndex].itemDataAssigner.IfItemSelected(false);
            
            if(index >= 0)
                collectionObjList[index].itemDataAssigner.IfItemSelected(true);
            
            selectedObjIndex = index;
        }
    }

    private void ClearSelection()
    {
        selectedCollectionIndex = -1;
        
        if (selectedObjIndex >= 0)
            collectionObjList[selectedObjIndex].itemDataAssigner.IfItemSelected(false);
        selectedObjIndex = -1;
    }
    
    private bool FitCondition(int collectionIndex)
    {
        //return false if the collection is empty
        if (allCollectionList[collectionIndex].mapIDList.Count <= 0)
            return false;
        
        if (currentCollectionFilterCondition != null)
        {
            bool playerCountMatch = allCollectionList[collectionIndex].playerCount <= currentCollectionFilterCondition.playerCount && allCollectionList[collectionIndex].playerCountMax >= currentCollectionFilterCondition.playerCountMax;
            bool isChapterMatch = !allCollectionList[collectionIndex].isChapter || showChapterCollection;
            bool multiplayerModeMatch = allCollectionList[collectionIndex].multiplayerMode ==
                                        currentCollectionFilterCondition.multiplayerMode;
            return playerCountMatch && isChapterMatch && multiplayerModeMatch;
        }

        return true;
    }

    private bool MultiplayerModeChangeAction(string mode)
    {
        switch (mode)
        {
            case "Competitive":
                currentCollectionFilterCondition.multiplayerMode = MultiplayerMode.Competitive;
                UpdateAllContent();
                return true;
            case "Cooperative":
                currentCollectionFilterCondition.multiplayerMode = MultiplayerMode.Cooperative;
                UpdateAllContent();
                return true;
            default:
                return false;
        }
        
        
    }
    
    private void CorrectPlayerCountMax(int collectionIndex)
    {
        if (allCollectionList[collectionIndex].playerCountMax < allCollectionList[collectionIndex].playerCount)
            allCollectionList[collectionIndex].playerCountMax = allCollectionList[collectionIndex].playerCount;
    }


    #region Scroll move & arrange

    private float changeStep = 60;

    private float contentPosY;
    private float posDiff;
    private bool canScroll = false;

    private void Update()
    {
        if (canScroll)
        {
            CheckStep();
        }
    }

    private void CheckStep()
    {
        
        float diff = contentRectTransform.anchoredPosition.y - contentPosY;
        contentPosY = contentRectTransform.anchoredPosition.y;
        
        if (Mathf.Abs(diff) > 0.5f)
            posDiff = diff;
        

        if (upperBound == null || lowerBound == null)
            return;
        
        if (posDiff > 0 && collectionObjList[headPointer].rectTransform.position.y > upperBound.position.y)
        {
            MoveItem(1);
        }else if (posDiff < 0 && collectionObjList[rearPointer].rectTransform.position.y < lowerBound.position.y)
        {
            MoveItem(-1);
        }
        
    }

    private void MoveItem(int dir)
    {
        if (dir > 0)
        {
            //check if there is new data behind rear
            //the head button goes to rear, and take the new data
            int newDataIndex = collectionObjList[rearPointer].collectionIndex + 1;
            if (newDataIndex < currentCollectionListCount)
            {
                collectionObjList[headPointer].UIObj.transform.SetSiblingIndex(itemObjCount - 1);
                AssignOneData(headPointer, newDataIndex);
                
                if(headPointer == selectedObjIndex)
                    HighlightObj(-1);
                if(collectionObjList[headPointer].collectionIndex == selectedCollectionIndex)
                    HighlightObj(headPointer);
                
                rearPointer = headPointer;
                headPointer = headPointer + 1 >= itemObjCount ? 0 : headPointer + 1;
                contentVerticalLayoutGroup.padding.top += (int)changeStep;
            }
        }
        else
        {
            //check if there is new data before head
            //the rear goes to head, and take the new data
            int newDataIndex = collectionObjList[headPointer].collectionIndex - 1;
            if (newDataIndex >= 0)
            {
                collectionObjList[rearPointer].UIObj.transform.SetSiblingIndex(0);
                AssignOneData(rearPointer, newDataIndex);
                
                if(rearPointer == selectedObjIndex)
                    HighlightObj(-1);
                if(collectionObjList[rearPointer].collectionIndex == selectedCollectionIndex)
                    HighlightObj(rearPointer);
                
                headPointer = rearPointer;
                rearPointer = rearPointer - 1 < 0 ? itemObjCount - 1 : rearPointer - 1;
                contentVerticalLayoutGroup.padding.top -= (int)changeStep;
            }
        }
    }

    #endregion
    
}
