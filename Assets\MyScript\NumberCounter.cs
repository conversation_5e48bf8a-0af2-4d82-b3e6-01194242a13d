using System;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class NumberCounter : MonoBehaviour
{
    [SerializeField] private TMP_Text numberText;
    [SerializeField] private Button decreaseButton;
    [SerializeField] private Button increaseButton;

    private Func<int, bool> setNumberAction;
    
    private int currentNumber = 0;
    private bool hasInitialized = false;

    public void SetNumber(int num, Func<int, bool> cFunc)
    {
        InitComponent();
        
        currentNumber = num;
        setNumberAction = cFunc;
        
        UpdateText();
    }

    private void InitComponent()
    {
        if (!hasInitialized)
        {
            increaseButton.onClick.AddListener(IncreaseNumber);
            decreaseButton.onClick.AddListener(DecreaseNumber);
            hasInitialized = true;
        }
    }

    void IncreaseNumber()
    {
        int newValue = currentNumber + 1;
        bool setNumber = setNumberAction(newValue);
        if (setNumber)
        {
            currentNumber = newValue;
            UpdateText();
        }
            
        
    }

    void DecreaseNumber()
    {
        int newValue = currentNumber - 1;
        bool setNumber = setNumberAction(newValue);
        if (setNumber)
        {
            currentNumber = newValue;
            UpdateText();
        }
    }

    void UpdateText()
    {
        numberText.text = currentNumber.ToString();
    }
}