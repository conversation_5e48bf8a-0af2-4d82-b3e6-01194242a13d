using System;
using System.Threading;
using System.Threading.Tasks;
using UnityEngine;

public abstract class OtherCharacterBase : MonoBehaviour
{
    protected Transform characterTransform;
    private float screenHalfWidth;
    private float screenHalfHeight;

    private Vector3 currentCharacterPos;
    private SpriteRenderer sr;
    protected Material characterMat;
    
    private ParticleSystem dieParticle;
    private ParticleSystem.MainModule dieParticleMain;

    private bool died = false;

    private void OnDisable()
    {
        cts?.Cancel();
    }

    public virtual void CharacterGotAttacked()
    {
    }

    public virtual void InitCharacter(ToolID toolID)
    {
        characterTransform = transform;
        screenHalfHeight = GameConst.verticalBound;
        screenHalfWidth = GameConst.horizontalBound;
        
        if (characterMat == null)
            characterMat = ToolDataManager.Instance.ReturnToolMaterial(toolID);

        if (sr == null)
            sr = GetComponent<SpriteRenderer>();

        sr.material = characterMat;
        sr.color = Color.white;

        if (dieParticle == null)
        {
            dieParticle = Instantiate(GlobalParameters.Instance.ReturnOtherCharacterDieParticle(), characterTransform.position, Quaternion.identity, characterTransform);
            dieParticleMain = dieParticle.main;
        }

        dieParticleMain.startColor = toolID == ToolID.Killer || toolID == ToolID.Transmitter ? Color.red : Color.blue;

        died = false;
    }

    protected virtual async void CharacterDie()
    {
        if (!died)
        {
            died = true;
            cts = new CancellationTokenSource();
            await DieProcedure(cts.Token);
        }
    }

    public virtual void CloseCharacter()
    {
        cts?.Cancel();
    }

    protected void CloseOutOfScreenCharacter()
    {
        currentCharacterPos = characterTransform.position;
        if (Mathf.Abs(currentCharacterPos.x) > screenHalfWidth ||
            Mathf.Abs(currentCharacterPos.y) > screenHalfHeight)
        {
            CharacterDie();
        }
    }

    private CancellationTokenSource cts = new CancellationTokenSource();
    private async Task DieProcedure(CancellationToken token)
    {
        try
        {
            dieParticle.Play();
            sr.color = Color.clear;
            await Task.Delay(500, token);
            if (token.IsCancellationRequested)
            {
                Debug.Log("Other character die task forcefully canceled?");
                dieParticle.Stop();
                return; 
            }
                
            dieParticle.Stop();
            CloseCharacter();
        }
        catch(TaskCanceledException)
        {
            Debug.Log("Task was forcefully canceled!");
        }
        
    }
}