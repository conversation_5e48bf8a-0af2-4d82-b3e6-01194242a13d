using UnityEngine;
using UnityEngine.InputSystem.LowLevel;
using UnityEngine.InputSystem.UI;

public class VirtualMouseController : MonoBehaviour
{
    [SerializeField] private RectTransform canvasRT;
    [SerializeField] private Transform mouseVisual;

    [SerializeField] private RectTransform vmRT;
    [SerializeField] private RectTransform visualRT;

    private float scaleFactorX;
    private float scaleFactorY;

    private VirtualMouseInput virtualMouseInput;

    private bool initialized = false;

    private bool active = false;

    private Vector2 hdVirtualMouseSize = new Vector2(30, 30);

    private float virtualMouseScale = 1;

    // Update is called once per frame
    void LateUpdate()
    {
        if(active)
           ClampPosition();
    }

    private void ClampPosition()
    {
        Vector2 virtualPos = virtualMouseInput.virtualMouse.position.value;
        virtualPos.x = Mathf.Clamp(virtualPos.x, 0,Screen.width);
        virtualPos.y = Mathf.Clamp(virtualPos.y, currentRect.y+50, currentRect.y + currentRect.height-50);
        InputState.Change(virtualMouseInput.virtualMouse.position, virtualPos);
    }


    #region API

    public void ShowVirtualMouse(bool show)
    {
        if (!initialized)
        {
            virtualMouseInput = GetComponent<VirtualMouseInput>();
            initialized = true;
        }
        
        float scaleFactor = 1 / canvasRT.localScale.x;
        vmRT.localScale = Vector3.one * scaleFactor;
        visualRT.sizeDelta = (currentRect.width/1920)*hdVirtualMouseSize;

        if (show)
        {
            virtualMouseInput.stickAction.action.Enable();
        }
        else
        {
            virtualMouseInput.stickAction.action.Disable();
        }
        
        mouseVisual.gameObject.SetActive(show);
        active = show;
    }
    

    private Rect currentRect;
    public void SetCurrentRect(Rect r)
    {
        currentRect = new Rect(r.x*Screen.width, r.y*Screen.height, r.width*Screen.width, r.height*Screen.height);
    }

    #endregion
}
