using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class BagManager : MonoBehaviour
{

    public GameObject BagUI;
    public List<BagTool> currentBagList = new List<BagTool>();
    public int presentBagCapacity = 3;

    private class BagItemUI
    {
        public BagItemUI(Image img)
        {
            itemImage = img;
            itemBorder = img.transform.GetChild(0).GetComponent<Image>();
        }

        public Image itemImage;
        public Image itemBorder;
    }

    private List<BagItemUI> BagItemUIList = new List<BagItemUI>();

    private List<RectTransform> bagItemRectTransformList = new List<RectTransform>();

    [SerializeField] private Color bagItemSelectedColor;

    private int mapEditorSelectedIndex = 0;

    private ColorManager colorManager;
    
    public static BagManager Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        DontDestroyOnLoad(this);
        InitBag();
    }

    private void InitBag()
    {
        GetBagItemUIList();
        UpdateAllTools();
    }

    private void GetBagItemUIList()
    {
        int uiItemCount = BagUI.transform.childCount;
        for (int i = 0; i < uiItemCount; i++)
        {
            if (BagUI.transform.GetChild(i).name != "Title")
            {
                BagItemUIList.Add(new BagItemUI(BagUI.transform.GetChild(i).GetComponent<Image>()));
                AddButtonListener(i);
                bagItemRectTransformList.Add(BagUI.transform.GetChild(i).GetComponent<RectTransform>());
                BagItemUIList[i].itemImage.color = Color.clear;
            }
            
        }
        
        firstImageRT = BagItemUIList[0].itemImage.GetComponent<RectTransform>();
    }

    private void AddButtonListener(int i)
    {
        int index = i;
        BagItemUIList[i].itemImage.transform.GetChild(0).GetComponent<Button>().onClick.AddListener(() => ClickBagItem(index));
    }

    private void ClickBagItem(int index)
    {
        if (GameManager.Instance.ReturnPresentState() == StateEnum.MapEditor)
        {
            SelectedBagItemHighlight(false);
            mapEditorSelectedIndex = index;
            SelectedBagItemHighlight(true, index);
            MapEditor.Instance.ClickedBag(index);
        }
    }

    public void UpdateAllTools()
    {
        for (int i = 0; i < BagItemUIList.Count; i++)
        {
            if (i < currentBagList.Count)
            {
                UpdateSprite(currentBagList[i], i);
            }
            else
            {
                BagItemUIList[i].itemImage.color = Color.clear;
            }
        }
        
        NoteMatchStaveFeedback();
    }

    public void AddTool(BagTool tool)
    {
        if (currentBagList.Count < presentBagCapacity)
        {
            currentBagList.Add(tool);
            UpdateSprite(tool, currentBagList.Count-1);
        }
        
        if(currentBagList.Count < 2)
          NoteMatchStaveFeedback();
    }
    
    public void DeleteSelectedTool(int index = 0)
    {
        //delete the first item
        if (currentBagList.Count > 0)
        {
            BagItemUIList[currentBagList.Count - 1].itemImage.color = Color.clear;
            currentBagList.RemoveAt(index);
            for (int i = 0; i < currentBagList.Count; i++)
            {
                UpdateSprite(currentBagList[i], i);
            }
            
            NoteMatchStaveFeedback();
        }
    }

    public void SetTool(int index, BagTool tool)
    {
        if (index >= 0 && index < currentBagList.Count)
        {
            currentBagList[index] = tool;
            UpdateSprite(tool, index);
        }
    }

    public void UpdateSprite(BagTool tool, int index)
    {
        BagItemUIList[index].itemImage.color = Color.white;
        BagItemUIList[index].itemImage.sprite =
            ToolDataManager.Instance.ReturnToolSprite(tool.toolID);
        GlobalMethod.OperateUIDirection(BagItemUIList[index].itemImage.gameObject, (int)tool.toolDirection);
    }
    
    public BagTool PresentSelectedBagTool()
    {
        if (currentBagList.Count > 0)
            return currentBagList[0];
        else
        {
            return new BagTool(ToolID.Block, ToolDirection.Original);
        }
    }

    public BagTool ReturnToolByIndex(int index)
    {
        if (index >= 0 && index < currentBagList.Count)
        {
            return new BagTool(currentBagList[index].toolID, currentBagList[index].toolDirection);
        }
        else
        {
            return new BagTool(ToolID.Block, ToolDirection.Original);
        }
    }

    public bool IsFull()
    {
        if (presentBagCapacity > currentBagList.Count)
        {
            return false;
        }
        else
        {
            return true;
        }
    }

    public bool GotItem(int index)
    {
        return index < currentBagList.Count;
    }
    
    public void BagItemColorTint(bool tint)
    {
        foreach (var item in BagItemUIList)
        {
            item.itemImage.transform.GetChild(0).GetComponent<Button>().transition = tint ? Selectable.Transition.ColorTint : Selectable.Transition.None;
        }
    }

    public void SelectedBagItemHighlight(bool highlight, int index = -1)
    {
        index = index >= 0 ? index : mapEditorSelectedIndex;
        BagItemUIList[index].itemBorder.color =
            highlight ? bagItemSelectedColor : Color.white;
    }


    private void NoteMatchStaveFeedback()
    {
        StopChangeFirstImageAnimation();
        StartChangeFirstImageAnimation();
        StopReadyAnimation();
        //if the first one is note
        if (GameManager.Instance.ReturnPresentState() == StateEnum.GamePlay)
        {
            BagTool firstTool = ReturnToolByIndex(0);
            if (firstTool.toolID == ToolID.Note)
            {
                //check if there are any stave in level
                //check if the direction match
                ChunkStickableIndex csValue;
                if (GlobalParameters.Instance.HasRequiredToolInLevel(ToolID.Stave, out csValue))
                {
                    if (GlobalMethod.PairNoteStaveDirection(firstTool.toolDirection,
                            GlobalParameters.Instance.chunkPool.activePool[csValue.chunkIndex]
                                .chunkChildList[csValue.stickableIndex].toolDir))
                    {
                        AudioManager.Instance.PlayerAudioSourcePlay(0, PlayerAudioEnum.SuccessReady);
                        StartReadyAnimation();
                        BagItemUIList[0].itemImage.color = ReturnColorByType(ColorEnum.UIButtonHighLight);
                    }
                    
                }
            }
        }
    }

    private void StartReadyAnimation()
    {
        StopChangeFirstImageAnimation();
        readyAnimation = StartCoroutine(ReadyAnimation());
    }

    private void StopReadyAnimation()
    {
        if(readyAnimation != null)
            StopCoroutine(readyAnimation);
        if (currentBagList.Count > 0)
        {
            firstImageRT.localScale = Vector2.one * hightlightScale;
        }
        else
        {
            firstImageRT.localScale= Vector2.one;
        }
    }

    private Coroutine readyAnimation;
    private Vector2 normalSize = new Vector2(80, 80);
    private float hightlightScale = 1.2f;
    private float readyAnimDur = 1;
    private RectTransform firstImageRT;
    [SerializeField] private float animSinFreq = 2f;
    private IEnumerator ReadyAnimation()
    {
        WaitForSecondsRealtime interval = new WaitForSecondsRealtime(GameConst.animUpdateInterval);
        float timer = 0;
        while (timer < readyAnimDur)
        {
            timer += GameConst.animUpdateInterval;
            firstImageRT.localScale = Vector2.one*(1 + 0.2f*Mathf.Sin(timer*Mathf.PI*animSinFreq));
            yield return interval;
        }
        
        StopReadyAnimation();
    }


    private void StartChangeFirstImageAnimation()
    {
        if (currentBagList.Count > 0)
        {
            changeFirstImageAnimation = StartCoroutine(ChangeFirstImageAnimation());
        }
    }

    private void StopChangeFirstImageAnimation()
    {
        if(changeFirstImageAnimation != null)
            StopCoroutine(changeFirstImageAnimation);

        if (currentBagList.Count > 0)
        {
            firstImageRT.localScale = Vector2.one * hightlightScale;
        }
        else
        {
            firstImageRT.localScale= Vector2.one;
        }
        
    }

    private Coroutine changeFirstImageAnimation;
    private float firstImgAnimDur = 0.4f;
    private IEnumerator ChangeFirstImageAnimation()
    {
        WaitForSecondsRealtime interval = new WaitForSecondsRealtime(GameConst.animUpdateInterval);
        float timer = 0;
        while (timer < firstImgAnimDur)
        {
            timer += GameConst.animUpdateInterval;
            firstImageRT.localScale = Vector2.one*(1 + 0.2f*Mathf.Sin(timer/firstImgAnimDur*0.5f*Mathf.PI));
            yield return interval;
        }

        StopChangeFirstImageAnimation();
    }

    private Color ReturnColorByType(ColorEnum cType)
    {
        if (colorManager == null)
            colorManager = GameManager.Instance.ReturnColorManager();

        return colorManager.ReturnColorByType(cType);
    }
}
