using System;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System.Text;

public class CollectionEditorInspector : InspectorDataAssigner
{
    [SerializeField] protected GameObject mapTextureCam;
    [SerializeField] private GameObject imgObj;

    //text editor
    [SerializeField] private GameObject textContentEditor;
    [SerializeField] private TMP_InputField nameInputField;
    [SerializeField] private TMP_InputField descriptionInputField;
    [SerializeField] private Toggle isBuiltinToggle;
    [SerializeField] private NumberCounter mapSequenceCounter;
    [SerializeField] private PlayerCountInspector playerCountInspector;
    [SerializeField] private PlayModeInspector playModeInspector;
    
    //text content
    [SerializeField] private TextContentInspector textContentInspector;
    
    [SerializeField] private GameObject saveButton;

    [SerializeField] private GameObject editableInstructionUI;

    [SerializeField] private MapDataAssigner mapDataAssigner;
    
    private CollectionClass currentInspectedCollection;
    private MapPreview currentMapPreview;

    private MapOrCollectionItemDataAssigner collectionItemDataAssigner;

    private int selectedMapSequence;

    private bool canEditCollection;
    
    private Action<InstructionType, bool, string> editorInstructionAction;

    private void OnDisable()
    {
        editorInstructionAction?.Invoke(InstructionType.SaveSuccessResultInstruction, false, "");
        if(mapTextureCam)
           mapTextureCam.SetActive(false);
    }

    public override void ShowMapInspector(MapPreview map, MapOrCollectionItemDataAssigner itemDataAssigner, int mapSequence)
    {
        //clear actions should always be called first
        ClearActions();
        currentMapPreview = map;
        selectedMapSequence = mapSequence;
        currentInspectType = InspectType.Map;

        currentInspectedCollection.playerCount = currentMapPreview.playerCount;
        currentInspectedCollection.playerCountMax = currentMapPreview.playerCountMax;
        
        mapSequenceCounter.SetNumber(mapSequence+1, MapSequenceChangeAction);
        
        CheckVisible();
        AssignData();
    }
    
    public override void ShowCollectionInspector(CollectionClass collection, MapOrCollectionItemDataAssigner itemDataAssigner)
    {
        //clear actions should always be called first
        ClearActions();
        currentInspectedCollection = collection;
        collectionItemDataAssigner = itemDataAssigner;
        currentInspectType = InspectType.Collection;

        canEditCollection = Application.isEditor || !currentInspectedCollection.isBuiltin;
        
        playerCountInspector.SetCollectionPlayerCountInspector(collection, SetCollectionPlayerMinFunc, SetCollectionPlayerMaxFunc, collection.mapIDList.Count <= 0 && canEditCollection);
        playModeInspector.SetCollectionPlayModeInspector(collection, collection.mapIDList.Count <= 0 && canEditCollection);
        
        CheckVisible();
        AssignData();
        AssignActions();
    }
    
    protected override void AssignData()
    {
        if (currentInspectType == InspectType.Map)
        {
            textContentInspector.ShowMapTextInspector(currentMapPreview);
        }
        else
        {
            nameInputField.text = currentInspectedCollection.name;
            descriptionInputField.text = currentInspectedCollection.description;
            if(!canEditCollection)
              textContentInspector.ShowCollectionTextInspector(currentInspectedCollection);
            else
            {
                textContentInspector.HideTextInspector();
            }
#if UNITY_EDITOR
            isBuiltinToggle.isOn = currentInspectedCollection.isBuiltin;
#endif
        
        }

    }

    private void CheckVisible()
    {
        mapTextureCam.SetActive(currentInspectType == InspectType.Map);
        imgObj.SetActive(currentInspectType == InspectType.Map);
        mapSequenceCounter.transform.parent.gameObject.SetActive(currentInspectType == InspectType.Map && canEditCollection);

        if (currentInspectType == InspectType.Map)
        {
            textContentEditor.SetActive(false);
        }
        else
        {
            isBuiltinToggle.transform.parent.gameObject.SetActive(Application.isEditor);
            
            textContentEditor.SetActive(canEditCollection);
            saveButton.SetActive(canEditCollection);
            
            editableInstructionUI.SetActive(!canEditCollection);
        }
        
        
    }
    

    #region Actions
    
    private void AssignActions()
    {
        nameInputField.onEndEdit.AddListener(NameChangeConfirmAction);
        descriptionInputField.onValueChanged.AddListener(DescriptionChangeAction);
#if UNITY_EDITOR
        isBuiltinToggle.onValueChanged.AddListener(IsBuiltinStateChangeAction);
#endif
    }
    
    protected override void ClearActions()
    {
        nameInputField.onValueChanged.RemoveAllListeners();
        nameInputField.onEndEdit.RemoveAllListeners();
        descriptionInputField.onValueChanged.RemoveAllListeners();
#if UNITY_EDITOR
        isBuiltinToggle.onValueChanged.RemoveAllListeners();
#endif
    }
    
    
    private StringBuilder collectionNameSB = new StringBuilder();
    private void NameChangeConfirmAction(string newName)
    {
        //if new name is different, then set
        if (!string.Equals(newName, currentInspectedCollection.name))
        {
            collectionNameSB.Clear();
            collectionNameSB.Append(newName);
            GlobalParameters.Instance.GetNewCollectionName(collectionNameSB, 0);
            
            currentInspectedCollection.name = collectionNameSB.ToString();
            collectionItemDataAssigner.SetCollectionData(currentInspectedCollection);
        }
        
    }

    private void DescriptionChangeAction(string desc)
    {
        currentInspectedCollection.description = desc;
    }

    private void IsBuiltinStateChangeAction(bool isBuiltin)
    {
        currentInspectedCollection.isBuiltin = isBuiltin;
    }

    private bool MapSequenceChangeAction(int textSeq)
    {
        int realSeq = textSeq - 1;
        if (realSeq >= 0 && realSeq < currentInspectedCollection.mapIDList.Count)
        {
            //move the ID in the list
            int mapID = currentInspectedCollection.mapIDList[selectedMapSequence];
            currentInspectedCollection.mapIDList.RemoveAt(selectedMapSequence);
            currentInspectedCollection.mapIDList.Insert(realSeq, mapID);
            
            mapDataAssigner.MoveItemSequence(selectedMapSequence, realSeq);
            selectedMapSequence = realSeq;
            return true;
        }

        return false;
    }
    
    private bool SetCollectionPlayerMinFunc(int counter)
    {
        if (counter > 0 && counter <= currentInspectedCollection.playerCountMax)
        {
            currentInspectedCollection.playerCount = counter;
            
            //update item UI
            collectionItemDataAssigner.SetCollectionData(currentInspectedCollection);
            
            //update play mode UI
            playModeInspector.SetCollectionPlayModeInspector(currentInspectedCollection, canEditCollection);
            return true;
        }

        return false;
    }
    
    private bool SetCollectionPlayerMaxFunc(int counter)
    {
        if (counter >= currentInspectedCollection.playerCount && counter <= 8)
        {
            currentInspectedCollection.playerCountMax = counter;
                
            //update item UI
            collectionItemDataAssigner.SetCollectionData(currentInspectedCollection);
                
            //update play mode UI
            playModeInspector.SetCollectionPlayModeInspector(currentInspectedCollection, canEditCollection);
            return true;
        }

        return false;
    }
    

    #endregion

    public override void ClearInspector()
    {
        currentMapPreview = null;
        currentInspectedCollection = null;
        imgObj.SetActive(false);
        mapTextureCam.SetActive(false);
        textContentEditor.SetActive(false);
        textContentInspector.HideTextInspector();
    }

    public void SubscribeEditorInstructionAction(Action<InstructionType, bool, string> cAction)
    {
        editorInstructionAction = cAction;
    }
    
    public void ApplyChangeButtonAction()
    {
        try
        {
            GlobalParameters.Instance.SaveCollectionList();
            editorInstructionAction?.Invoke(InstructionType.SaveSuccessResultInstruction, true, "");
        }
        catch (Exception e)
        {
            editorInstructionAction?.Invoke(InstructionType.SaveFailResultInstruction, true, e.ToString());
            throw;
        }
    }

    
    
    
}
