using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

public class CloudGenerator : MonoBeh<PERSON>our
{
    [SerializeField] private GameObject cloudPrefab;
    [SerializeField] private Sprite[] cloudSprites;
    [SerializeField] private float cloudSpeed;
    [SerializeField] private int initCloudCount = 3;
    [SerializeField] private int moveDirection = -1;
    private float initPosX = 28;
    private float initPosMaxY = 2;
    private float initPosMinY = -2;

    private float generateDur;
    
    private class CloudPool
    {
        private Stack<GameObject> objPool;
        private Transform parentTransform;
        private GameObject cloudPrefab;
        private Sprite[] cloudSprites;
        private string objName;
        private int objCount;

        public void InitPool(string name, int initCount, Transform parent, GameObject prefab, Sprite[] sprites)
        {
            objName = name;
            parentTransform = parent;
            cloudPrefab = prefab;
            cloudSprites = sprites;
            objPool = new Stack<GameObject>();

            for (int i = 0; i < initCount; i++)
            {
                CreateObj();
            }
        }

        private void CreateObj()
        {
            GameObject o = Instantiate(cloudPrefab, parentTransform);
            o.name = objName + objCount;
            ReturnObj(o);
            objCount++;
        }

        private void SetSprite(GameObject o)
        {
            int spriteIndex = Random.Range(0, cloudSprites.Length);
            if (spriteIndex >= cloudSprites.Length)
            {
                Debug.Log("cloud index out of range");
                return;
            }

            o.GetComponent<SpriteRenderer>().sprite = cloudSprites[spriteIndex];
        }

        public GameObject GetObj()
        {
            if (objPool.Count == 0)
            {
                CreateObj();
            }

            GameObject o = objPool.Pop();
            o.SetActive(true);
            SetSprite(o);
            return o;
        }

        public void ReturnObj(GameObject o)
        {
            o.SetActive(false);
            objPool.Push(o);
        }
    }

    private CloudPool cloudPool = new CloudPool();
    
    private List<GameObject> generatedCloudList = new List<GameObject>();

    private bool hasInitialized = false;

    private void OnEnable()
    {
        if (!hasInitialized)
        {
            cloudPool.InitPool("Cloud", 7, transform, cloudPrefab, cloudSprites);
            hasInitialized = true;
        }
        InitSomeCloudsWhenActivateBg();
    }

    private void InitSomeCloudsWhenActivateBg()
    {
        for (int i = 0; i < initCloudCount; i++)
        {
            GameObject o = cloudPool.GetObj();
            o.transform.position = new Vector3(Random.Range(-3, 3), Random.Range(initPosMinY, initPosMaxY), 0);
            generatedCloudList.Add(o);
        }
    }

    // Update is called once per frame
    void Update()
    {
        GenerateCloudWithTimer();
        MoveCloud();
    }

    private void GenerateCloudWithTimer()
    {
        generateDur -= Time.unscaledDeltaTime;
        if (generateDur <= 0)
        {
            GameObject o = cloudPool.GetObj();
            
            o.transform.position = RandomInitPos();
            generatedCloudList.Add(o);
            
            generateDur = RandomGenerateTime();
        }
    }

    private void MoveCloud()
    {
        for (int i = 0; i < generatedCloudList.Count; i++)
        {
            generatedCloudList[i].transform.position += new Vector3(moveDirection*cloudSpeed, 0)*Time.unscaledDeltaTime;
            
            if (Mathf.Abs(generatedCloudList[i].transform.position.x) > 30)
            {
                cloudPool.ReturnObj(generatedCloudList[i]);
                generatedCloudList.RemoveAt(i);
            }
        }
    }

    private float RandomGenerateTime()
    {
        return Random.Range(8, 13);
    }

    private Vector3 RandomInitPos()
    {
        float y = Random.Range(initPosMinY, initPosMaxY);
        return new Vector3(-moveDirection*initPosX, y, 0);
    }
}
