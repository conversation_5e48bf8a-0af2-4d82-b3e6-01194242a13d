using System;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using System.Text;

public class ChooseEditorMapInspector : InspectorDataAssigner
{
    [SerializeField] protected GameObject mapTextureCam;
    [SerializeField] private GameObject buttonListUI;
    
    //text content editor
    [SerializeField] private GameObject textContentEditor;
    [SerializeField] private TMP_InputField nameInputField;
    [SerializeField] private TMP_InputField descriptionInputField;
    [SerializeField] private Toggle isBuiltinToggle;
    [SerializeField] private ChooseEditorMapDataAssigner chooseEditorMapDataAssigner;
    [SerializeField] private Toggle hasCountdownToggle;
    [SerializeField] private PlayerCountInspector playerCountInspector;
    [SerializeField] private PlayModeInspector playModeInspector;
    
    
    //text content
    [SerializeField] private TextContentInspector mapTextContentInspector;
    

    private MapPreview currentMapPreview;
    private MapOrCollectionItemDataAssigner currentItemDataAssigner;

    private bool canEditMap = false;
    
    private Action<InstructionType, bool, string> editorInstructionAction;

    private void OnDisable()
    {
        editorInstructionAction?.Invoke(InstructionType.SaveSuccessResultInstruction, false, "");
        if(mapTextureCam)
           mapTextureCam.SetActive(false);
    }

    public override void ShowMapInspector(MapPreview map, MapOrCollectionItemDataAssigner itemDataAssigner, int mapSequence)
    {
        mapTextureCam.SetActive(true);
        //clear actions should always be called first
        ClearActions();

        canEditMap = Application.isEditor || !map.isBuiltin;
        currentMapPreview = map;
        currentItemDataAssigner = itemDataAssigner;
        
        playerCountInspector.SetMapPlayerCountInspector(map, SetMapPlayerMinFunc, SetMapPlayerMaxFunc);
        playModeInspector.SetMapPlayModeInspector(map, canEditMap);
        
        CheckVisible();
        AssignData();
        AssignActions();
    }
    

    private void CheckVisible()
    {
        isBuiltinToggle.transform.parent.gameObject.SetActive(Application.isEditor);
        hasCountdownToggle.transform.parent.gameObject.SetActive(Application.isEditor);
        
        textContentEditor.SetActive(canEditMap);
        buttonListUI.SetActive(canEditMap);
        
        if(!canEditMap)
            mapTextContentInspector.ShowMapTextInspector(currentMapPreview);
        else
        {
            mapTextContentInspector.HideTextInspector();
        }
    }
    

    #region Actions

    private void AssignActions()
    {
        nameInputField.onEndEdit.AddListener(NameChangeConfirmAction);
        descriptionInputField.onValueChanged.AddListener(DescriptionChangeAction);
#if UNITY_EDITOR
        isBuiltinToggle.onValueChanged.AddListener(IsBuiltinStateChangeAction);
        hasCountdownToggle.onValueChanged.AddListener(HasCountdownStateChangeAction);
#endif
    }

    protected override void ClearActions()
    {
        nameInputField.onValueChanged.RemoveAllListeners();
        nameInputField.onEndEdit.RemoveAllListeners();
        descriptionInputField.onValueChanged.RemoveAllListeners();
#if UNITY_EDITOR
        isBuiltinToggle.onValueChanged.RemoveAllListeners();
        hasCountdownToggle.onValueChanged.RemoveAllListeners();
#endif
    }
    

    private StringBuilder mapNameSB = new StringBuilder();

    private void NameChangeConfirmAction(string newName)
    {
        //if new name is different, then we check if the new name has repetition
        if (!string.Equals(newName, currentMapPreview.mapName))
        {
            mapNameSB.Clear();
            mapNameSB.Append(newName);
            GlobalParameters.Instance.GetNewMapName(mapNameSB, 0);
            currentMapPreview.mapName = mapNameSB.ToString();
            currentItemDataAssigner.SetMapData(currentMapPreview);
        }
    }

    private void DescriptionChangeAction(string desc)
    {
        currentMapPreview.mapDescription = desc;
    }

    private void IsBuiltinStateChangeAction(bool isBuiltin)
    {
        currentMapPreview.isBuiltin = isBuiltin;
        GlobalParameters.Instance.SaveMapPreviewList();

        chooseEditorMapDataAssigner.BuiltinStateChangeAction(isBuiltin);
    }

    private void HasCountdownStateChangeAction(bool hasCountdown)
    {
        currentMapPreview.hasCountdown = hasCountdown;
        GlobalParameters.Instance.SaveMapPreviewList();
    }

    private bool SetMapPlayerMinFunc(int counter)
    {
        if (counter > 0 && counter <= currentMapPreview.playerCountMax)
        {
            currentMapPreview.playerCount = counter;
            
            //update item UI
            currentItemDataAssigner.SetMapData(currentMapPreview);
            
            //update play mode UI
            playModeInspector.SetMapPlayModeInspector(currentMapPreview, canEditMap);
            return true;
        }

        return false;
    }
    
    private bool SetMapPlayerMaxFunc(int counter)
    {
        //if the map was added player before
        //the player max cannot be updated by this button
        if (GlobalParameters.Instance.playerSpawnObjPool.activePool.Count > 0)
        {
            //show instruction
            editorInstructionAction?.Invoke(InstructionType.CannotChangePlayerCountMax, true, string.Empty);
        }
        else
        {
            if (counter >= currentMapPreview.playerCount && counter <= 8)
            {
                currentMapPreview.playerCountMax = counter;
                
                //update item UI
                currentItemDataAssigner.SetMapData(currentMapPreview);
                
                //update play mode UI
                playModeInspector.SetMapPlayModeInspector(currentMapPreview, canEditMap);
                return true;
            }
        }

        return false;
    }
    #endregion

    protected override void AssignData()
    {
        nameInputField.text = currentMapPreview.mapName;
        descriptionInputField.text = currentMapPreview.mapDescription;

#if UNITY_EDITOR
        isBuiltinToggle.isOn = currentMapPreview.isBuiltin;
        hasCountdownToggle.isOn = currentMapPreview.hasCountdown;
#endif
    }

    public override void ClearInspector()
    {
        mapTextureCam.SetActive(false);
        currentMapPreview = null;
        textContentEditor.SetActive(false);
        mapTextContentInspector.HideTextInspector();
    }
    
    public void SubscribeEditorInstructionAction(Action<InstructionType, bool, string> cAction)
    {
        editorInstructionAction = cAction;
    }

    public void ApplyChangeButtonAction()
    {
        try
        {
            GlobalParameters.Instance.SaveMapPreviewList();
            editorInstructionAction?.Invoke(InstructionType.SaveSuccessResultInstruction, true, string.Empty);
        }
        catch (Exception e)
        {
            editorInstructionAction?.Invoke(InstructionType.SaveFailResultInstruction, true, e.ToString());
            throw;
        }
    }

    public void OpenMapEditorButtonAction()
    {
        if (currentMapPreview != null)
        {
            GameManager.Instance.StateButtonAction((int)StateEnum.MapEditor);
        }
    }
}