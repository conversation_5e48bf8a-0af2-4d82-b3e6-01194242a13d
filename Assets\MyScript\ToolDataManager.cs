using System.Collections.Generic;
using UnityEngine;

public class ToolDataManager : MonoBehaviour
{
    [SerializeField] private ToolDataList toolDataAsset;


    private class ToolData
    {
        public Sprite toolSprite;
        public string toolName;
        public ToolType toolType;
        public string toolNameChinese;
        public string toolDescription;
        public string toolDescriptionChinese;
        public Material material;
    }

    private Dictionary<ToolID, ToolData> toolDataDict = new Dictionary<ToolID, ToolData>();


    public static ToolDataManager Instance { get; private set; }


    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(this);
            return;
        }

        Instance = this;

        DontDestroyOnLoad(this);
        InitToolDict();
        
    }

    private void InitToolDict()
    {
        toolDataDict.Clear();
        foreach (var toolData in toolDataAsset.toolDataList)
        {
            ToolData newTool = new ToolData();
            newTool.toolSprite = toolData.toolSprite;
            newTool.toolType = toolData.toolType;
            newTool.toolName = toolData.toolName;
            newTool.toolNameChinese = toolData.toolNameChinese;
            newTool.toolDescription = toolData.toolDescription;
            newTool.toolDescriptionChinese = toolData.toolDescriptionChinese;
            newTool.material = toolData.material;
            toolDataDict.Add(toolData.toolID, newTool);
        }
    }
    

    public Sprite ReturnToolSprite(ToolID toolID)
    {
        return toolDataDict[toolID].toolSprite;
    }

    public string ReturnToolName(ToolID toolID)
    {
        switch (MyLocalizationManager.Instance.currentLanguage)
        {
            case LanguageEnum.Chinese:
                return toolDataDict[toolID].toolNameChinese;
            default:
                return toolDataDict[toolID].toolName;
        }
    }

    public string ReturnToolDescription(ToolID toolID)
    {
        switch (MyLocalizationManager.Instance.currentLanguage)
        {
            case LanguageEnum.Chinese:
                return toolDataDict[toolID].toolDescriptionChinese;
            default:
                return toolDataDict[toolID].toolDescription;
        }
    }

    public List<BagTool> ReturnMapEditorSymbolTabToolList()
    {
        List<BagTool> l = new List<BagTool>();
        foreach (KeyValuePair<ToolID, ToolData> pair in toolDataDict)
        {
            if (pair.Value.toolType == ToolType.Symbol)
                l.Add(new BagTool(pair.Key, ToolDirection.Original));
        }

        l.Insert(0, new BagTool(ToolID.Mouse, ToolDirection.Original));
#if UNITY_EDITOR
        l.Insert(3, new BagTool(ToolID.Info, ToolDirection.Original));
#endif
        return l;
    }

    public List<BagTool> ReturnMapEditorCharacterTabToolList()
    {
        List<BagTool> l = new List<BagTool>();
        foreach (KeyValuePair<ToolID, ToolData> pair in toolDataDict)
        {
            if (pair.Value.toolType == ToolType.Character)
                l.Add(new BagTool(pair.Key, ToolDirection.Original));
        }

        l.Insert(0, new BagTool(ToolID.Player, ToolDirection.Original));
        l.Insert(0, new BagTool(ToolID.Mouse, ToolDirection.Original));
        return l;
    }

    public ToolType ReturnToolType(ToolID toolID) => toolDataDict[toolID].toolType;

    public Material ReturnToolMaterial(ToolID toolID) => toolDataDict[toolID].material;
}