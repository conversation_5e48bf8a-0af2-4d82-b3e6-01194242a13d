using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class InstructionController : MonoBehaviour
{
    [SerializeField] private GameObject gameInstructionObj;

    [SerializeField] private GameObject editorInstructionObj;
    
    [SerializeField] private CollectionEditorInspector collectionEditorInspector;
    [SerializeField] private ChooseEditorMapInspector chooseEditorMapInspector;
    

    private TMP_Text gameInstructionTMP;
    private Image gameInstructionImg;

    private TMP_Text editorInstructionTMP;

    private GameObject currentInstructionObj;
    private TMP_Text currentInstructionTMP;
    private string currentInstructionContent;

    private SpecificController currentSC;

    public bool isShowingInstruction { get; private set; }

    [Serializable]
    public class SpriteClass
    {
        public SpecificController sc;
        public Sprite sprite;
    }

    [Serializable]
    private class InstructionContent
    {
        public InstructionType instructionType;
        public string EnglishContent;
        public string ChineseContent;
        public bool showText;
        public bool showImg;
        public List<SpriteClass> instructionImg;
    }

    [SerializeField] private List<InstructionContent> instructionList;

    private Dictionary<InstructionType, InstructionContent> instructionDict = new Dictionary<InstructionType, InstructionContent>();
    
    // Start is called before the first frame update
    void Start()
    {
        InitDict();
        AssignObj();
        SubscribeAction();
    }

    private void InitDict()
    {
        foreach (InstructionContent content in instructionList)
        {
            instructionDict.Add(content.instructionType, content);
        }
    }

    private void AssignObj()
    {
        gameInstructionTMP = gameInstructionObj.GetComponentInChildren<TMP_Text>();
        gameInstructionImg = gameInstructionObj.GetComponentInChildren<Image>();
        editorInstructionTMP = editorInstructionObj.GetComponentInChildren<TMP_Text>();
        
        gameInstructionObj.SetActive(false);
        editorInstructionObj.SetActive(false);
    }
    
    private void SubscribeAction()
    {
        GameManager.Instance.SubscribeGameInstructionAction(ShowGameInstruction);
        MapEditor.Instance.SubscribeEditorInstructionAction(ShowEditorInstruction);
        collectionEditorInspector.SubscribeEditorInstructionAction(ShowEditorInstruction);
        chooseEditorMapInspector.SubscribeEditorInstructionAction(ShowEditorInstruction);
    }
    

    private void ShowGameInstruction(InstructionType type, Vector3 worldPos, bool show)
    {
        CloseFormerInstructionAnimation();
        
        if(!show)
            return;
        
        currentInstructionObj = gameInstructionObj;
        currentInstructionTMP = gameInstructionTMP;
        
        Vector2 WorldObject_ScreenPosition = GlobalMethod.WorldPositionToCanvasPosition(worldPos);
        currentInstructionObj.GetComponent<RectTransform>().anchoredPosition = WorldObject_ScreenPosition;
        
        currentSC  = GameManager.Instance.firstPlayerSpecificController;
        
        
        SetupInstructionLayout(type);
        SetupInstructionContent(type);
        
        showInstructionAnimation = ShowInstructionAnimation();
        StartCoroutine(showInstructionAnimation);
    }
    
    private void ShowEditorInstruction(InstructionType type, bool show,string additionalInfo)
    {
        CloseFormerInstructionAnimation();

        if (!show)
            return;
        
        currentInstructionObj = editorInstructionObj;
        currentInstructionTMP = editorInstructionTMP;
        
        currentSC  = GameManager.Instance.firstPlayerSpecificController;
        
        
        SetupInstructionLayout(type);
        SetupInstructionContent(type);
        
        if(!string.IsNullOrEmpty(additionalInfo))
           currentInstructionContent += ": " + additionalInfo;
        
        showInstructionAnimation = ShowInstructionAnimation();
        StartCoroutine(showInstructionAnimation);
    }
    
    
    private void SetupInstructionLayout(InstructionType type)
    {
        bool isKeyboard = currentSC == SpecificController.Keyboard1;
        bool isQuitGame = type == InstructionType.QuitGameInstruction;
        
        gameInstructionImg.gameObject.SetActive(instructionDict[type].showImg);
        gameInstructionTMP.gameObject.SetActive(instructionDict[type].showText);

        if (instructionDict[type].showImg && instructionDict[type].showText)
            gameInstructionTMP.alignment = TextAlignmentOptions.Left;
        else
        {
            gameInstructionTMP.alignment = TextAlignmentOptions.Center;
        }

        if (isKeyboard)
        {
            gameInstructionImg.rectTransform.sizeDelta = isQuitGame ? new Vector3(100, 100) : new Vector3(150, 50);
        }
        else
        {
            gameInstructionImg.rectTransform.sizeDelta = isQuitGame ? new Vector3(100,100) : new Vector3(50, 50);
        }
        
    }
    
    private void SetupInstructionContent(InstructionType type)
    {
        //setup text content
        if (MyLocalizationManager.Instance.currentLanguage == LanguageEnum.English)
        {
            currentInstructionContent =  instructionDict[type].EnglishContent;
        }
        else
        {
            currentInstructionContent =  instructionDict[type].ChineseContent;
        }
        
        if(type == InstructionType.QuitGameInstruction || type == InstructionType.StartDialogueInstruction)
           gameInstructionImg.sprite = instructionDict[type].instructionImg.Find(i => i.sc == currentSC).sprite;
    }

    private void CloseFormerInstructionAnimation()
    {
        isShowingInstruction = false;
        if(currentInstructionObj != null) 
            currentInstructionObj.SetActive(false);
        if(showInstructionAnimation != null)
            StopCoroutine(showInstructionAnimation);
    }

    private WaitForSecondsRealtime instructionDur = new WaitForSecondsRealtime(1.5f);
    private IEnumerator showInstructionAnimation;
    private IEnumerator ShowInstructionAnimation()
    {
        isShowingInstruction = true;
        currentInstructionObj.SetActive(true);
        currentInstructionTMP.text = currentInstructionContent;
        
        yield return instructionDur;

        isShowingInstruction = false;
        currentInstructionObj.SetActive(false);
    }
}
