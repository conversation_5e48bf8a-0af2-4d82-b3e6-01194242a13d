using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;


public class SlotDataAssigner : MonoBehaviour
{
    //show which progress is highlighted now

    [SerializeField] private Transform content;
    
    [Serializable]
    public class SlotClass
    {
        public SlotClass(GameObject obj)
        {
            UIObj = obj;
            chapterTMP = UIObj.transform.Find("Chapter").Find("ChapterValue").GetComponent<TMP_Text>();
            levelTMP = UIObj.transform.Find("Level").Find("LevelValue").GetComponent<TMP_Text>();
            timeTMP = UIObj.transform.Find("Time").Find("TimeValue").GetComponent<TMP_Text>();
            operationTMP = UIObj.transform.Find("Operation").Find("OperationValue").GetComponent<TMP_Text>();
            dateTMP = UIObj.transform.Find("Date").GetComponent<TMP_Text>();
            addTMP = UIObj.transform.Find("Add").GetComponent<TMP_Text>();
        }

        public GameObject UIObj;
        [HideInInspector]public TMP_Text chapterTMP;
        [HideInInspector]public TMP_Text levelTMP;
        [HideInInspector]public TMP_Text timeTMP;
        [HideInInspector]public TMP_Text operationTMP;
        [HideInInspector]public TMP_Text dateTMP;
        [HideInInspector]public TMP_Text addTMP; 
    }

    [SerializeField] private ImageButtonInteractionUI imageButtonInteractionUI;

    [SerializeField] private InputInsctruction inputInsctruction;
    
    private List<SlotClass> slotObjList = new List<SlotClass>();

    private List<Progress> progressList = new List<Progress>();

    private bool hasInitialized = false;

    private int highlightedIndex;

    private void Start()
    {
        if(!hasInitialized)
            InitSlot();
    }

    private void InitSlot()
    {
        //show played data if there is
        GetProgressList();
        
        imageButtonInteractionUI.SubscribeHighlightChangeAction(HighlightChangeAction);

        hasInitialized = true;
    }

    private void GetProgressList()
    {
        progressList.Clear();
        int index = 0;
        foreach (Transform item in content)
        {
            progressList.Add(SaveSystem.LoadProgress(index));
            slotObjList.Add(new SlotClass(item.gameObject));
            PutProgressDataOnSlot(index);
            AddButtonListener(index);
            index++;
        }
    }

    public void ShowSlot()
    {
        for (int i = 0; i < slotObjList.Count; i++)
        {
            PutProgressDataOnSlot(i);
        }
        
    }
    
    private void PutProgressDataOnSlot(int index)
    {
        if (progressList[index].levelResultList.Count > 0)
        {
            ShowText(index, true);
            slotObjList[index].chapterTMP.text = (progressList[index].chapter + 1).ToString();
            slotObjList[index].levelTMP.text = ShouldPlayLevel(index).ToString();
            slotObjList[index].timeTMP.text = WholeTime(index);
            slotObjList[index].operationTMP.text = WholeOperation(index);
            slotObjList[index].dateTMP.text = progressList[index].lastPlayDate;
            slotObjList[index].addTMP.text = "";
        }
        else
        {
            ShowText(index, false);
            slotObjList[index].addTMP.text = "+";
        }
        
    }

    private void ShowText(int index, bool show)
    {
        foreach (Transform textObj in slotObjList[index].UIObj.transform)
        {
            if(textObj.name != "Add")
               textObj.gameObject.SetActive(show);
        }
    }
    
    private void AddButtonListener(int objIndex)
    {
        slotObjList[objIndex].UIObj.GetComponent<Button>().onClick
            .AddListener(() => PickSlotButtonAction(objIndex));
    }

    private void PickSlotButtonAction(int index)
    {
        GameManager.Instance.SelectProgress(progressList[index]);
        GameManager.Instance.StateButtonAction((int)StateEnum.ChooseChapter);
    }

    private int ShouldPlayLevel(int index)
    {
        int levelIndex = 1;
        foreach (var levelResult in progressList[index].levelResultList)
        {
            if (levelResult.hasPassed)
                levelIndex++;
        }
        

        levelIndex -= progressList[index].chapter * 32;
        if (levelIndex > 32) levelIndex = 32;
        return levelIndex;
    }

    private string WholeTime(int index)
    {
        float time = 0;
        foreach (var result in progressList[index].levelResultList)
        {
            time += result.timeDur;
        }

        return TimeSpan.FromSeconds(time).ToString(@"hh\:mm\:ss\.fff");
    }
    
    private string WholeOperation(int index)
    {
        int op = 0;
        foreach (var result in progressList[index].levelResultList)
        {
            op += result.operationCount;
        }

        return op.ToString();
    }

    public void DeleteHighlightedSlot()
    {
        // highlightedIndex = GetComponent<ImageButtonInteractionUI>().ReturnHighlightedItemIndex();
        //clear
        progressList[highlightedIndex] = new Progress(highlightedIndex, string.Empty);
        
        //save
        SaveSystem.SetProgress(progressList[highlightedIndex]);
        
        //show
        ShowSlot();

        //update instruction UI
        inputInsctruction.UpdateChooseSlotUI(IsHighlightedGameDataEmpty());
    }

    private void HighlightChangeAction(int newIndex)
    {
        //set
        highlightedIndex = newIndex;

        //tell input instruction to render
        inputInsctruction.UpdateChooseSlotUI(IsHighlightedGameDataEmpty());
    }

    public bool IsHighlightedGameDataEmpty()
    {
        if(!hasInitialized)
            InitSlot();
        return progressList[highlightedIndex].levelResultList.Count == 0;
    }

    private void ClearProgressWithIndex(int index)
    {
        progressList[index].levelResultList.Clear();
        progressList[index].chapter = 0;
        progressList[index].dialogueProgress.Clear();
    }

    private void AddToLastLevel(int index)
    {
        LevelResult r = new LevelResult()
        {
            hasPassed = true,
            timeDur = 5,
            operationCount = 5,
            playerIndex = 0
        };
        for (int i = progressList[index].levelResultList.Count; i < 31; i++)
        {
            progressList[index].levelResultList.Add(r);
        }

        progressList[index].chapter = 0;

    }

}
