using System.Collections.Generic;
using UnityEditor;
using UnityEngine;
using System.IO;
using System.Linq;
using System.Text;


public class GlobalParameters : MonoBehaviour
{
    //for now, only save the current level info
    //the basics are stickable obj
    //there are 3 types of tool: mover, rotater, killer
    //there are instantiate points for players
    //flag points, flag target points

    public GameObject playerSpawnObjs;
    public GameObject stickableObjs;
    public GameObject levelDialogueObjs;
    public GameObject otherCharacterObjs;


    //if present level template is not saving, then update its data before setting it dirty
    private LevelTemplate presentLevelTemplate;
    private MapTemplate presentMap;

    private int currentMapID;

    private LevelPreviewList builtinMapListTemplate;
    [HideInInspector] public List<MapPreview> allMapList;

    private ChapterPreviewList builtinCollectionListTemplate;
    [HideInInspector] public List<CollectionClass> allCollectionList;

    private int maxMapID = -1;

    private Vector3 dialogueAreaScale = new Vector3(1, 1, 1);

    [SerializeField] private RuntimeAnimatorController killerController;
    [SerializeField] private RuntimeAnimatorController planterController;
    [SerializeField] private RuntimeAnimatorController switchController;
    [SerializeField] private RuntimeAnimatorController transmitterController;

    [SerializeField] private ParticleSystem otherCharacterDieParticle;
    
    
    private PortalConnectionManager portalConnectionManager;
    
    public BlockShaderParam currentBlockShaderParam { private set; get;}

    private int orderInLayer = 0;
    public bool editMode { get; private set; }

    public class MChunkPool : MObjPool<ChunkClass>
    {
        protected override void CreateObj()
        {
            GameObject newChunkObj = new GameObject(objName + objCount);
            newChunkObj.transform.SetParent(parentTransform);
            newChunkObj.tag = "Chunk";
            newChunkObj.layer = LayerMask.NameToLayer("StickableObj");
            ChunkClass chunk = newChunkObj.AddComponent<ChunkClass>();
            ReturnObj(chunk);
            base.CreateObj();
        }

        public override ChunkClass GetObj()
        {
            ChunkClass chunk = base.GetObj();
            chunk.gameObject.SetActive(true);
            return chunk;
        }

        public override void ReturnObj(ChunkClass chunk)
        {
            foreach (var stickable in chunk.chunkChildList)
            {
                stickable.stickableObj.transform.SetParent(parentTransform);
            }
            chunk.CloseAllTools();
            chunk.gameObject.SetActive(false);
            base.ReturnObj(chunk);
        }
    }

    public class MStickablePool : MObjPool<GameObject>
    {
        protected override void CreateObj()
        {
            GameObject newStickable = new GameObject(objName + objCount);
            newStickable.transform.SetParent(parentTransform);
            newStickable.tag = "Stickable";
            newStickable.layer = LayerMask.NameToLayer("StickableObj");
            newStickable.AddComponent<SpriteRenderer>().material = ToolDataManager.Instance.ReturnToolMaterial(ToolID.Block);
            newStickable.AddComponent<BoxCollider2D>();
            newStickable.GetComponent<BoxCollider2D>().size = new Vector2(0.9f, 0.9f);
            newStickable.GetComponent<BoxCollider2D>().edgeRadius = 0.05f;
            ReturnObj(newStickable);
            base.CreateObj();
        }

        public override GameObject GetObj()
        {
            GameObject o = base.GetObj();
            o.SetActive(true);
            return o;
        }

        public override void ReturnObj(GameObject t)
        {
            t.SetActive(false);
            base.ReturnObj(t);
        }
    }
    
    public class MDialogueAreaPool : MObjPool<GameObject>
    {
        protected override void CreateObj()
        {
            GameObject newDialogueArea = new GameObject(objName + objCount);
            newDialogueArea.transform.SetParent(parentTransform);
            newDialogueArea.tag = "DialogueArea";
            newDialogueArea.layer = LayerMask.NameToLayer("StickableObj");
            newDialogueArea.AddComponent<SpriteRenderer>().sprite =
                ToolDataManager.Instance.ReturnToolSprite(ToolID.Info);
            newDialogueArea.AddComponent<DialogueTrigger>();
            ReturnObj(newDialogueArea);
            base.CreateObj();
        }

        public override GameObject GetObj()
        {
            GameObject o = base.GetObj();
            o.SetActive(true);
            return o;
        }

        public override void ReturnObj(GameObject t)
        {
            t.SetActive(false);
            base.ReturnObj(t);
        }
    }
    
    public class MPlayerSpawnPool: MObjPool<GameObject>
    {
        private ColorManager colorManager;
        protected override void CreateObj()
        {
            GameObject newPlayer = new GameObject(objName + objCount);
            newPlayer.transform.SetParent(parentTransform);
            newPlayer.layer = LayerMask.NameToLayer("PlayerSpawn");
            newPlayer.AddComponent<SpriteRenderer>().sprite =
                ToolDataManager.Instance.ReturnToolSprite(ToolID.Player);

            if (colorManager == null)
                colorManager = GameManager.Instance.ReturnColorManager(); 
            ReturnObj(newPlayer);
            base.CreateObj();
        }
        
        public override GameObject GetObj()
        {
            GameObject o = base.GetObj();
            o.SetActive(true);
            UpdateSpawnColor();
            return o;
        }

        public override void ReturnObj(GameObject t)
        {
            t.SetActive(false);
            base.ReturnObj(t);
            UpdateSpawnColor();
        }

        private void UpdateSpawnColor()
        {
            int index = 0;
            foreach (GameObject o in activePool)
            {
                o.GetComponent<SpriteRenderer>().color = colorManager.PlayerColorByIndex(index);
                index++;
            }
        }

        public int ReturnActiveCount()
        {
            return activePool.Count;
        }

        public int ReturnPlayerIndex(GameObject obj)
        {
            int playerIndex = -1;
            for (int i = 0; i < activePool.Count; i++)
            {
                if (obj == activePool[i])
                    playerIndex = i;
            }

            return playerIndex;
        }
    }

    public class MOtherCharacterPool : MObjPool<GameObject>
    {
        protected override void CreateObj()
        {
            GameObject otherCharacter = new GameObject(objName + objCount);
            otherCharacter.transform.SetParent(parentTransform);
            otherCharacter.layer = LayerMask.NameToLayer("OtherCharacter");
            otherCharacter.AddComponent<SpriteRenderer>().sortingLayerName = "OtherCharacter";
            otherCharacter.AddComponent<CharacterInfo>();
            otherCharacter.AddComponent<Animator>();
            ReturnObj(otherCharacter);
            base.CreateObj();
        }

        public override GameObject GetObj()
        {
            GameObject o = base.GetObj();
            o.SetActive(true);
            return o;
        }

        public override void ReturnObj(GameObject t)
        {
            t.GetComponent<CharacterInfo>().DisableAllBehavior();
            t.SetActive(false);
            base.ReturnObj(t);
        }
    }
    

    public MChunkPool chunkPool = new MChunkPool();
    public MStickablePool stickablePool = new MStickablePool();
    public MDialogueAreaPool dialogAreaPool = new MDialogueAreaPool();
    public MPlayerSpawnPool playerSpawnObjPool = new MPlayerSpawnPool();
    public MOtherCharacterPool otherCharacterPool = new MOtherCharacterPool();

    private Dictionary<GameObject, ChunkStickableIndex> csIndexDict = new Dictionary<GameObject, ChunkStickableIndex>();
    

    public static GlobalParameters Instance { get; private set; }

    private void Awake()
    {
        if (Instance != null && Instance != this)
        {
            Destroy(this);
            return;
        }

        Instance = this;
        editMode = false;
        chunkPool.InitPool("Chunk", 10, stickableObjs.transform);
        stickablePool.InitPool("Stickable", 10, stickableObjs.transform);
        dialogAreaPool.InitPool("DialogueArea", 5, levelDialogueObjs.transform);
        playerSpawnObjPool.InitPool("PlayerSpawn", 8, playerSpawnObjs.transform);
        otherCharacterPool.InitPool("OtherCharacter", 8, otherCharacterObjs.transform);
        portalConnectionManager = FindObjectOfType<PortalConnectionManager>();

        InitAllMapList();
        InitAllCollectionList();
        InitMaxMapID();
    }

    //this should be called, when in choose editor map
    //use async and show loading sign
    private void InitMaxMapID()
    {
        maxMapID = allMapList.Max(i => i.mapID);
    }

    private void InitAllMapList()
    {
        allMapList = SaveSystem.LoadMapList();
        builtinMapListTemplate = Resources.Load(Path.Combine("MapData", "MapList")) as LevelPreviewList;
        
        //the builtin part of all map list needs to be updated
        var localMapPreview = allMapList.Where(i => !i.isBuiltin);
        allMapList = builtinMapListTemplate.previewList.Concat(localMapPreview).ToList();
        SaveSystem.SetMapList(allMapList);

        CheckIfMapMissed();
    }

    private void InitAllCollectionList()
    {
        allCollectionList = SaveSystem.LoadCollectionList();
        builtinCollectionListTemplate = Resources.Load(Path.Combine("MapData", "CollectionList")) as ChapterPreviewList;
        
        //the builtin part of all collection list needs to be updated
        var localCollection = allCollectionList.Where(i => !i.isBuiltin);
        allCollectionList = builtinCollectionListTemplate.previewList.Concat(localCollection).ToList();
        SaveSystem.SetCollectionList(allCollectionList);
    }

    public bool LoadMap(int id)
    {
        currentMapID = id;
        int mapIndex = ReturnMapIndexByID(id);
        if (allMapList[mapIndex].isBuiltin)
        {
            presentLevelTemplate = Resources.Load(Path.Combine("MapData", "Maps", id.ToString())) as LevelTemplate;
            if (presentLevelTemplate != null)
            {
                presentMap = new MapTemplate(presentLevelTemplate);
                return true;
            }
        }
        else
        {
            presentMap = SaveSystem.LoadMap(id.ToString());
            return true;
        }

        return false;
    }

    public void SetPresentMap(MapTemplate map)
    {
        presentMap = map;
    }

    public MapTemplate ReturnPresentMap()
    {
        return presentMap;
    }


    #region Get scene info into presentLevel

    public void GetInfo()
    {
        //get player spawn list
        GetPlayerSpawnPosList();

        //get all other objs info
        GetPropInfo();

        GetBagInfo();

        GetDialogueAreaInfo();

        GetOtherCharacterInfo();

        GetPortalInfo();

        int mapIndex = ReturnMapIndexByID(currentMapID);

        if (allMapList[mapIndex].isBuiltin)
        {
#if UNITY_EDITOR
            CopyMapTemplateToLevelTemplate();
            EditorUtility.SetDirty(presentLevelTemplate);
#endif
        }
        else
        {
            SaveSystem.SetMap(currentMapID.ToString(), presentMap);
        }
    }

    private void GetPlayerSpawnPosList()
    {
        presentMap.playerPosList.Clear();
        foreach (GameObject o in playerSpawnObjPool.activePool)
        {
            presentMap.playerPosList.Add(new MyVector3(o.transform.position));
        }
    }

    private void GetPropInfo()
    {
        presentMap.sceneChunkList.Clear();

        int index = 0;

        foreach (Transform obj in stickableObjs.transform)
        {
            if (obj.CompareTag("Chunk") && obj.gameObject.activeSelf)
            {
                presentMap.sceneChunkList.Add(new Chunk());
                foreach (var item in obj.GetComponent<ChunkClass>().chunkChildList)
                {
                    presentMap.sceneChunkList[index].chunkPropList.Add(new PropTool((int)item.toolID,
                        (int)item.toolDir, item.stickableObj.transform.position));
                }

                index++;
            }
        }
    }

    private void GetBagInfo()
    {
        presentMap.bagToolList.Clear();
        foreach (BagTool tool in BagManager.Instance.currentBagList)
        {
            presentMap.bagToolList.Add(tool);
        }
    }

    private void GetDialogueAreaInfo()
    {
        int dialogueIndex = 0;
        foreach (Transform dialogueObj in levelDialogueObjs.transform)
        {
            if (dialogueObj.gameObject.activeSelf)
            {
                string newName = $"{currentMapID}_{ReturnCurrentMapPreview().mapName}_{dialogueIndex}";
                if (presentMap.dialogueAreaList.Count > dialogueIndex)
                {
                    //there is already an dialogue asset, then just change the asset name
                    if (presentMap.dialogueAreaList[dialogueIndex].dialogueName != newName)
                    {
                        RenameDialogueAsset(presentMap.dialogueAreaList[dialogueIndex].dialogueName, newName);
                        presentMap.dialogueAreaList[dialogueIndex].dialogueName = newName;
                    }
                    
                    //update position
                    presentMap.dialogueAreaList[dialogueIndex].triggerPos = dialogueObj.position;
                }
                else
                {
                    //there is no dialogue asset, then create a new one
                    CreateNewDialogueAsset(newName);
                    //这个obj的名字即为dialogue文件的名字
                    dialogueObj.gameObject.name = newName;
                    presentMap.dialogueAreaList.Add(new DialogueArea(dialogueObj.gameObject));
                }

                dialogueIndex++;
            }
        }

        if (presentMap.dialogueAreaList.Count > dialogueIndex)
        {
            //delete all the asset from dialogueIndex  to the last
            for (int i = dialogueIndex; i < presentMap.dialogueAreaList.Count; i++)
            {
                DeleteDialogueAsset(presentMap.dialogueAreaList[i].dialogueName);
                presentMap.dialogueAreaList.RemoveAt(i);
            }
        }
    }

    private void GetOtherCharacterInfo()
    {
        presentMap.otherCharacterList.Clear();
        foreach (Transform otherCharacter in otherCharacterObjs.transform)
        {
            if (otherCharacter.gameObject.activeSelf)
                presentMap.otherCharacterList.Add(new OtherCharacterArea(otherCharacter.position,
                    otherCharacter.GetComponent<CharacterInfo>().ReturnCharacterInfo()));
        }
    }

    
    private void GetPortalInfo()
    {
        presentMap.portalSequence = portalConnectionManager.ReturnCurrentMapPortalDataList();
    }

    #endregion

    #region Set presentLevel into scene

    //call this after this level is set up
    public void ResetLevel()
    {
        orderInLayer = 1;
        
        //first clear former map entirely,
        //in case, part of old map interact with new ones
        ClearFormerMap();

        SetPropInfo();
        SetPlayerInfo();
        SetBagToolInfo();
        SetDialogueAreaInfo();
        SetOtherCharacter();
        SetPortalInfo();
    }

    public void ClearFormerMap()
    {
        //clear player
        playerSpawnObjPool.ReturnAll();
        
        //clear chunk first and then stickable
        chunkPool.ReturnAll();
        stickablePool.ReturnAll();
        csIndexDict.Clear();
        
        //clear bag
        BagManager.Instance.currentBagList.Clear();
        
        //clear dialogue 
        dialogAreaPool.ReturnAll();
        
        //clear other character
        otherCharacterPool.ReturnAll();
    }

    private void SetPlayerInfo()
    {
        bool gameStateCondition = GameManager.Instance.ReturnPresentState() == StateEnum.ChooseEditorMap ||
                                  GameManager.Instance.ReturnPresentState() == StateEnum.CollectionEditor ||
                                  GameManager.Instance.ReturnPresentState() == StateEnum.ChooseMapPopup;
        if (editMode || gameStateCondition)
        {
            foreach (var playerSpawn in presentMap.playerPosList)
            {
                playerSpawnObjPool.GetObj().transform.position = new Vector3(playerSpawn.x, playerSpawn.y, playerSpawn.z);
            }
        }
        else
        {
            int index = 0;
            foreach (var playerSpawn in presentMap.playerPosList)
            {
                if (index < GameManager.Instance.playerList.Count)
                {
                    GameManager.Instance.SpawnPlayer(index, new Vector3(playerSpawn.x, playerSpawn.y, playerSpawn.z), orderInLayer);
                    orderInLayer++;
                }
                else
                {
                    break;
                }


                index++;
            }
        }
    }

    private void SetPropInfo()
    {
        foreach (var chunkStruct in presentMap.sceneChunkList)
        {
            ChunkClass chunkClass = chunkPool.GetObj();
            chunkClass.transform.rotation = Quaternion.identity;
            foreach (var toolStruct in chunkStruct.chunkPropList)
            {
                GameObject toolObj = stickablePool.GetObj();
                toolObj.transform.position = toolStruct.toolPos;
                toolObj.transform.SetParent(chunkClass.transform);
                chunkClass.chunkChildList.Add(new ChunkClass.StickableClass(toolStruct.toolID, toolStruct.toolDirection,
                    toolObj));
                toolObj.GetComponent<SpriteRenderer>().sortingOrder = orderInLayer;
                
            }
            
            chunkClass.InitChunk();
            //there is one "show tool" layer above each stickable
            orderInLayer += 2;
        }
        
        SetCsIndexDict();
    }

    public void SetCsIndexDict()
    {
        if (GameManager.Instance.ReturnPresentState() == StateEnum.GamePlay)
        {
            //clear former
            csIndexDict.Clear();
            
            //add new ones
            for (int i = 0; i < chunkPool.activePool.Count; i++)
            {
                for (int j = 0; j < chunkPool.activePool[i].chunkChildList.Count; j++)
                {
                    csIndexDict.Add(chunkPool.activePool[i].chunkChildList[j].stickableObj, new ChunkStickableIndex(i, j));
                }
            }
        }
    }

    private void SetBagToolInfo()
    {
        foreach (var item in presentMap.bagToolList)
        {
            BagManager.Instance.currentBagList.Add(new(item.toolID, item.toolDirection));
        }

        BagManager.Instance.UpdateAllTools();
    }

    private void SetDialogueAreaInfo()
    {
        foreach (DialogueArea area in presentMap.dialogueAreaList)
        {
            GameObject newArea = dialogAreaPool.GetObj();
            newArea.GetComponent<SpriteRenderer>().sprite = ToolDataManager.Instance.ReturnToolSprite(ToolID.Info);
            newArea.name = area.dialogueName;
            newArea.transform.position = area.triggerPos;
            newArea.transform.localScale = dialogueAreaScale;
            newArea.GetComponent<SpriteRenderer>().sortingOrder = orderInLayer + 1;
            newArea.GetComponent<DialogueTrigger>().InitTrigger(dialogueAreaScale.x);
            newArea.GetComponent<DialogueTrigger>().UpdateState();
        }
    }

    private void SetOtherCharacter()
    {
        int characterOrder = 0;
        foreach (OtherCharacterArea characterArea in presentMap.otherCharacterList)
        {
            GameObject newCharacter = otherCharacterPool.GetObj();
            newCharacter.transform.position = characterArea.characterPos;
            newCharacter.GetComponent<SpriteRenderer>().sprite =
                ToolDataManager.Instance.ReturnToolSprite(characterArea.characterInfo.toolID);
            GlobalMethod.OperateUIDirection(newCharacter, (int)characterArea.characterInfo.toolDirection);
            newCharacter.GetComponent<SpriteRenderer>().sortingOrder = characterOrder;
            newCharacter.GetComponent<CharacterInfo>().GameStartCreateCharacter(characterArea.characterInfo);

            characterOrder++;
        }
    }

    private void SetPortalInfo()
    {
        portalConnectionManager.InitPortalConnection(presentMap.portalSequence);
    }

    #endregion

    #region API

    public void EditMode(bool edit)
    {
        editMode = edit;
    }

    public void UpdateDialogueAreaState(string name)
    {
        foreach (Transform dialogue in levelDialogueObjs.transform)
        {
            if (name == dialogue.name)
                dialogue.GetComponent<DialogueTrigger>().UpdateState();
        }
    }

    public RuntimeAnimatorController ReturnKillerController()
    {
        return killerController;
    }

    public RuntimeAnimatorController ReturnPlanterController()
    {
        return planterController;
    }

    public RuntimeAnimatorController ReturnSwitchController()
    {
        return switchController;
    }

    public RuntimeAnimatorController ReturnTransmitterController()
    {
        return transmitterController;
    }

    public ParticleSystem ReturnOtherCharacterDieParticle() => otherCharacterDieParticle;

    public bool OutOfScreen(Vector3 pos)
    {
        return Mathf.Abs(pos.x) > GameConst.horizontalBound || Mathf.Abs(pos.y) > GameConst.verticalBound;
    }

    public GameObject ReturnStickableByPos(Vector3 pos)
    {
        for (int i = 0; i < stickablePool.activePool.Count; i++)
        {
            if (Vector3.Distance(stickablePool.activePool[i].transform.position, pos) < 0.1f)
                return stickablePool.activePool[i];
        }
        return null;
    }

    public ChunkStickableIndex ReturnChunkStickableIndexByObj(GameObject obj)
    {
        if (obj != null && csIndexDict.TryGetValue(obj, out ChunkStickableIndex csIndex))
        {
            return csIndex;
        }
        return new ChunkStickableIndex(-1, -1);
    }

    public ChunkClass ReturnChunkByObj(GameObject obj)
    {
        (int chunkIndex, int objIndex) = ReturnChunkStickableIndexByObj(obj);
        if (chunkIndex != -1)
        {
            return chunkPool.activePool[chunkIndex];
        }

        return null;
    }

    public BagTool ReturnBagToolByObj(GameObject obj)
    {
        if (GameManager.Instance.ReturnPresentState() == StateEnum.GamePlay)
        {
            (int chunkIndex, int objIndex) = ReturnChunkStickableIndexByObj(obj);
            if (chunkIndex != -1)
                return new BagTool(chunkPool.activePool[chunkIndex].chunkChildList[objIndex].toolID, chunkPool.activePool[chunkIndex].chunkChildList[objIndex].toolDir);
        }
        else
        {
            return MapEditor.Instance.MapEditorReturnBagToolByObj(obj);
        }

        return new BagTool(ToolID.Block, ToolDirection.Original);
    }
    
    public void SetCurrentBlockShaderParam(BlockShaderParam bsp) => currentBlockShaderParam = bsp;

    public Vector3[] GetCurrentMapPlayerPosArr()
    {
        Vector3[] posArr = new Vector3[presentMap.playerPosList.Count];
        for (int i = 0; i < presentMap.playerPosList.Count; i++)
        {
            posArr[i] = presentMap.playerPosList[i].ConvertToVector3();
        }

        return posArr;
    }

    public bool HasRequiredToolInLevel(ToolID tool, out ChunkStickableIndex csValue)
    {
        csValue = new ChunkStickableIndex(-1, -1);
        foreach (KeyValuePair<GameObject,ChunkStickableIndex> csi in csIndexDict)
        {
            if (chunkPool.activePool[csi.Value.chunkIndex].chunkChildList[csi.Value.stickableIndex].toolID == tool)
            {
                csValue = csi.Value;
                return true;
            }
                
        }

        return false;
    }

    #endregion

    #region Map&Collection preview data
    private int ReturnMapIndexByID(int id)
    {
        int index = -1;
        for (int i = 0; i < allMapList.Count; i++)
        {
            if (allMapList[i].mapID == id)
                index = i;
        }

        return index;
    }

    public MapPreview ReturnCurrentMapPreview()
    {
        MapPreview map = null;
        for (int i = 0; i < allMapList.Count; i++)
        {
            if (allMapList[i].mapID == currentMapID)
                map = allMapList[i];
        }

        return map;
    }

    public MapPreview ReturnMapPreviewByID(int id)
    {
        MapPreview map = null;
        for (int i = 0; i < allMapList.Count; i++)
        {
            if (allMapList[i].mapID == id)
                map = allMapList[i];
        }

        return map;
    }
    
    public void GetNewMapName(StringBuilder name, int count)
    {
        if (allMapList.Any(i => i.mapName == name.ToString()))
        {
            if (count == 0)
                name.Append(" " + count);
            else
            {
                name.Replace((count - 1).ToString(), count.ToString());
            }

            count++;

            GetNewMapName(name, count);
        }
    }

    public void GetNewCollectionName(StringBuilder name, int count)
    {
        if (allCollectionList.Any(i => i.name == name.ToString()))
        {
            if (count == 0)
                name.Append(" " + count);
            else
            {
                name.Replace((count - 1).ToString(), count.ToString());
            }

            count++;

            GetNewCollectionName(name, count);
        }
    }

    public int NewMapID()
    {
        if (maxMapID == -1)
            return -1;

        maxMapID++;
        return maxMapID;
    }

    public void SaveMapPreviewList()
    {
#if UNITY_EDITOR
        builtinMapListTemplate.previewList.Clear();
        builtinMapListTemplate.previewList = allMapList.Where(i => i.isBuiltin).ToList();
        EditorUtility.SetDirty(builtinMapListTemplate);
#endif

        SaveSystem.SetMapList(allMapList);
    }

    public void SaveCollectionList()
    {
#if UNITY_EDITOR
        builtinCollectionListTemplate.previewList.Clear();
        builtinCollectionListTemplate.previewList = allCollectionList.Where(i => i.isBuiltin).ToList();
        EditorUtility.SetDirty(builtinCollectionListTemplate);
#endif
        SaveSystem.SetCollectionList(allCollectionList);
    }
    
    private void CheckIfMapMissed()
    {
#if UNITY_EDITOR
        string path = "Assets/Resources/MapData/Maps";
        var builtinMapFile = Directory.EnumerateFiles(path).Where(i => !i.Contains(".meta"));
        
        foreach (string s in builtinMapFile)
        {
            int mapID = int.Parse(Path.GetFileNameWithoutExtension(s));
            if (builtinMapListTemplate.previewList.All(i => i.mapID != mapID))
            {
                Debug.LogError($"missed map ID: {mapID}");
            }
        }
#endif
    }
    
    
    #endregion

    private string dialogueBasePath = "Assets/Resources/Dialogue/";

    private void RenameDialogueAsset(string oldName, string newName)
    {
#if UNITY_EDITOR
        //first check if the old file exist
        //if it does not exist, 
        //just create a new file with new name
        string path = Path.Combine(dialogueBasePath, oldName + ".asset");
        if (!File.Exists(path))
        {
            Debug.Log("the dialogue file does not exist");
            CreateNewDialogueAsset(newName);
            return;
        }
        
        string errorMessage = AssetDatabase.RenameAsset(path, newName);
        if (!string.IsNullOrEmpty(errorMessage))
        {
            Debug.LogError($"Failed to rename asset: {errorMessage}");
        }
        else
        {
            Debug.Log($"Successfully renamed asset to: {newName}");
        }

        // Save changes
        AssetDatabase.SaveAssets();
#endif
    }

    private void CreateNewDialogueAsset(string name)
    {
#if UNITY_EDITOR
        Debug.Log($"create an asset: {dialogueBasePath + name + ".asset"}");
        DialogueTemplate asset = ScriptableObject.CreateInstance<DialogueTemplate>();
        AssetDatabase.CreateAsset(asset, dialogueBasePath + name + ".asset");
        AssetDatabase.SaveAssets();
#endif
    }

    private void DeleteDialogueAsset(string name)
    {
#if UNITY_EDITOR
        //delete map asset
        string path = dialogueBasePath + name + ".asset";

        // Check if the asset exists before deleting
        if (AssetDatabase.LoadAssetAtPath<DialogueTemplate>(path) != null)
        {
            AssetDatabase.DeleteAsset(path);
            AssetDatabase.SaveAssets();
        }
        else
        {
            Debug.LogError("No ScriptableObject found at the path: " + path);
        }
#endif
    }

    private void CopyMapTemplateToLevelTemplate()
    {
        presentLevelTemplate.playerPosList = presentMap.playerPosList;
        presentLevelTemplate.dialogueAreaList = presentMap.dialogueAreaList;
        presentLevelTemplate.bagToolList = presentMap.bagToolList;
        presentLevelTemplate.otherCharacterList = presentMap.otherCharacterList;
        presentLevelTemplate.sceneChunkList = presentMap.sceneChunkList;
        presentLevelTemplate.portalSequence = presentMap.portalSequence;
    }
    
    
    #region chunk attach in game
    
    [SerializeField] private ChunkAttachEffect chunkAttachEffect;

    private void FixedUpdate()
    {
        AttachCollidedChunk();
    }

    private void AttachCollidedChunk()
    {
        for (int i = 0; i < chunkPool.activePool.Count; i++)
        {
            AttachCheck(i);
        }
    }

    private void AttachCheck(int index)
    {
        for (int i = index + 1; i < chunkPool.activePool.Count; i++)
        {
            if(!chunkPool.activePool[index].inRotateProcedure && !chunkPool.activePool[i].inRotateProcedure)
               AttachCheckStickable(chunkPool.activePool[index], chunkPool.activePool[i]);
        }
    }

    private float attachTolerance = 0.01f;
    private void AttachCheckStickable(ChunkClass chunk1, ChunkClass chunk2)
    {
        float leastDistance = 100;
        Vector2 attachPos1 = Vector2.zero;
        Vector2 attachPos2 = Vector2.zero;
        foreach (var s1 in chunk1.chunkChildList)
        {
            foreach (var s2 in chunk2.chunkChildList)
            {
                Vector2 s1Pos = s1.stickableObj.transform.position;
                Vector2 s2Pos = s2.stickableObj.transform.position;
                float distance =
                    Vector2.Distance(s1Pos, s2Pos);
                if (leastDistance > distance)
                {
                    leastDistance = distance;
                    attachPos1 = s1Pos;
                    attachPos2 = s2Pos;
                }
                
            }
        }
        
        
        
        if (leastDistance - 1 < attachTolerance)
        {
            if (leastDistance >  attachTolerance)
            {
                //snap attach
                Vector2 gridPos = attachPos1 + ReturnLeastMovement(attachPos1, attachPos2);
                chunk2.MoveByAttach(gridPos - attachPos2);
                chunkAttachEffect.PutEffect((gridPos + attachPos1)*0.5f, (gridPos - attachPos1).normalized);
            }
            else
            {
                chunkAttachEffect.PutSoundEffect((attachPos1 + attachPos2)*0.5f);
            }
                
            AttachTwoChunk(chunk1, chunk2);
        }
    }

    private void AttachTwoChunk(ChunkClass c1, ChunkClass c2)
    {
        //move the list to c1
        //move the obj to c1
        //update c1 state
        //push c2
        foreach (var s2 in c2.chunkChildList)
        {
            c1.chunkChildList.Add(s2);
            s2.stickableObj.transform.SetParent(c1.transform);
        }
        
        
        c2.ChunkChangeByAttach();
        //clear should be invoked after "ChunkChangeByAttach"
        c2.chunkChildList.Clear();
        c2.gameObject.SetActive(false);
        chunkPool.ReturnObj(c2);
        
        c1.UpdateChunkState(0);
        
        SetCsIndexDict();
    }
    

    private Vector2[] fourDir = new Vector2[]
    {
        Vector2.right,
        Vector2.up, 
        Vector2.left, 
        Vector2.down, 
    };
    private Vector2 ReturnLeastMovement(Vector2 pos1, Vector2 pos2)
    {
        int index = 0;
        float leastDistance = 10;
        for (int i = 0; i < 4; i++)
        {
            float distance = Vector2.Distance(pos1 + fourDir[i], pos2);
            if (leastDistance > distance)
            {
                index = i;
                leastDistance = distance;
            }
        }

        return fourDir[index];
    }
    
    #endregion
}